# Query Invalidation Patterns

This document describes the reusable patterns for handling TanStack Query invalidations in our application.

## Overview

Instead of manually calling `invalidateQueries` multiple times in each mutation, we use centralized invalidation utilities that make the code more maintainable and consistent.

## Key Benefits

1. **DRY Principle**: No more repeating the same invalidation logic
2. **Consistency**: All mutations use the same invalidation patterns
3. **Performance**: Parallel invalidation using `Promise.all`
4. **Maintainability**: Changes to invalidation logic happen in one place
5. **Type Safety**: TypeScript ensures correct query key usage

## Usage Patterns

### 1. Using Predefined Invalidation Groups

For common scenarios, use the predefined invalidation groups:

```typescript
import { invalidateByGroup } from '@/lib/query-invalidation';

export const useCancelReservation = () => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async () => {
      // This invalidates reservations, classes, and activities
      await invalidateByGroup(qc, 'RESERVATION_CANCELLED');
    },
  });
};
```

### 2. Using Custom Query Keys

For specific cases, you can invalidate custom query keys:

```typescript
import { invalidateQueries, QUERY_KEYS } from '@/lib/query-invalidation';

export const useCustomMutation = () => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: customAction,
    onSuccess: async () => {
      await invalidateQueries(qc, [
        QUERY_KEYS.CLASSES,
        ['custom-query', 'with-params'],
      ]);
    },
  });
};
```

### 3. Unified Reservation Mutations

For reservation operations, use the unified `useReserveMutation` hook that handles both classes and activities:

```typescript
import { useReserveMutation, type ReservationData } from '@/data/screens/common/mutations';

// For class reservations
const classReservation: ReservationData = {
  class_id: 123,
  date: '2024-01-15',
  is_virtual: false,
  type: 'class',
};

// For activity reservations
const activityReservation: ReservationData = {
  equipment_id: 456,
  date: '2024-01-15',
  time: '10:00',
  duration: 60,
  attending_persons: 2,
  type: 'equipment',
};

const { mutate } = useReserveMutation({
  onSuccess: () => console.log('Reservation successful!'),
});
```

### 4. Using the Hook Wrapper

For new mutations, you can use the `useMutationWithInvalidation` hook:

```typescript
import { useMutationWithInvalidation, INVALIDATION_GROUPS } from '@/lib/query-invalidation';

export const useNewMutation = () => {
  return useMutationWithInvalidation(
    newMutationFunction,
    INVALIDATION_GROUPS.CLASS_RESERVATION_CHANGED,
    {
      onSuccess: (data, variables) => {
        // Custom success logic
        console.log('Mutation succeeded:', data);
      },
    }
  );
};
```

## Available Invalidation Groups

| Group | Description | Invalidates |
|-------|-------------|-------------|
| `RESERVATION_CANCELLED` | When any reservation is cancelled | reservations, classes, activities |
| `RESERVATION_MADE` | When any reservation is made | reservations, classes, activities |
| `CLASS_RESERVATION_CHANGED` | When class reservations change | classes, reservations |
| `ACTIVITY_RESERVATION_CHANGED` | When activity reservations change | activities, reservations |
| `FAVORITE_TOGGLED` | When favorites are toggled | classes, events, activities |
| `APPOINTMENT_BOOKED` | When appointments are booked | appointments |
| `ACTIVITY_LOG_CREATED` | When activity logs are created | challenge-event-logs |

## Available Query Keys

All query keys are centralized in `QUERY_KEYS`:

```typescript
export const QUERY_KEYS = {
  RESERVATIONS: ['reservations'],
  CLASSES: ['classes'],
  ACTIVITIES: ['activities'],
  EVENTS: ['events'],
  APPOINTMENTS: ['appointments'],
  CHALLENGE_EVENT_LOGS: ['challenge-event-logs'],
} as const;
```

## Migration Guide

### Before (Old Pattern)
```typescript
export const useCancelReservation = () => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async () => {
      await qc.invalidateQueries({ queryKey: ['reservations'] });
      await qc.invalidateQueries({ queryKey: ['classes'] });
      await qc.invalidateQueries({ queryKey: ['activities'] });
    },
  });
};
```

### After (New Pattern)
```typescript
import { invalidateByGroup } from '@/lib/query-invalidation';

export const useCancelReservation = () => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async () => {
      await invalidateByGroup(qc, 'RESERVATION_CANCELLED');
    },
  });
};
```

### Unified Reservation Pattern

#### Before (Separate Hooks)
```typescript
// Class reservation
export const useReserveClass = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['classes'] });
      onSuccess?.();
    },
  });
};

// Activity reservation
export const useReserveActivity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: ActivityReservationRequest) => apiClient.reserveActivity(data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });
      queryClient.invalidateQueries({
        queryKey: ['activity-time-slots', variables.equipment_id],
      });
    },
  });
};
```

#### After (Unified Hook)
```typescript
import { useReserveMutation, type ReservationData } from '@/data/screens/common/mutations';

// Both class and activity reservations use the same hook
const { mutate } = useReserveMutation({
  onSuccess: () => console.log('Reservation successful!'),
});

// Usage for class reservation
const classData: ReservationData = {
  class_id: 123,
  date: '2024-01-15',
  is_virtual: false,
  type: 'class',
};

// Usage for activity reservation
const activityData: ReservationData = {
  equipment_id: 456,
  date: '2024-01-15',
  time: '10:00',
  duration: 60,
  attending_persons: 2,
  type: 'equipment',
};
```

## Adding New Invalidation Groups

To add a new invalidation group:

1. Add it to `INVALIDATION_GROUPS` in `lib/query-invalidation.ts`:
```typescript
export const INVALIDATION_GROUPS = {
  // ... existing groups
  NEW_FEATURE_CHANGED: [
    QUERY_KEYS.NEW_FEATURE,
    QUERY_KEYS.RELATED_FEATURE,
  ],
} as const;
```

2. Use it in your mutations:
```typescript
await invalidateByGroup(qc, 'NEW_FEATURE_CHANGED');
```

## Best Practices

1. **Use predefined groups** when possible for consistency
2. **Add new groups** for new features that affect multiple queries
3. **Use `onSettled`** instead of `onSuccess` when you want invalidation to happen regardless of success/failure
4. **Consider refetchType** - use `'active'` (default) for immediate updates, `'inactive'` for background updates
5. **Keep invalidation groups focused** - don't invalidate unrelated queries

## Performance Considerations

- All invalidations in a group run in parallel using `Promise.all`
- Only active queries are refetched by default (use `refetchType: 'all'` to refetch inactive queries too)
- Consider the impact of invalidating many queries at once

## Examples

### Simple Invalidation
```typescript
// Invalidate a single query
await invalidateQueries(qc, [QUERY_KEYS.CLASSES]);
```

### Group Invalidation
```typescript
// Invalidate related queries
await invalidateByGroup(qc, 'FAVORITE_TOGGLED');
```

### Custom Invalidation with Options
```typescript
// Invalidate with custom refetch behavior
await invalidateQueries(qc, [QUERY_KEYS.CLASSES], {
  refetchType: 'inactive' // Only refetch inactive queries
});
```

### Hook-based Mutation
```typescript
// Complete mutation with built-in invalidation
const mutation = useMutationWithInvalidation(
  myMutationFunction,
  INVALIDATION_GROUPS.CLASS_RESERVATION_CHANGED,
  {
    onSuccess: (data) => {
      toast.success('Success!');
    },
    onError: (error) => {
      toast.error('Failed!');
    },
  }
);
```
