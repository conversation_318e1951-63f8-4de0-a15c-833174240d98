## Filtering architecture

- UI filter components are stateless controls that emit typed values.
- `FacilityFilter` emits `string[]` of facility IDs. Screens join these as `gym_id` query params (comma-separated) to align with backend expectations.
- Data fetching hooks (`useClassesQuery`, `useAppointmentByCategory`) accept `gym_id?: string` for transport while UI state remains `string[]`.

### Activities filtering

### Challenges

- Mirrors Events/Activities list architecture: container screen + header + list + card.
- Data is fetched via `useChallengesQuery` with params `{ status, search }`.
- Response is validated with zod (`ChallengesResponseSchema`).
- UI uses lightweight `Progress` component from `components/ui/progress` for percentage bar.
- Navigation entry is from `More` screen routing to `app/challenges.tsx`.

- Activities uses `timeRange` filter in the filter sheet, which normalizes to `{ startTime, endTime }`.
- When the filter is applied, the hook maps `timeRange` to transport params `start_time` and `end_time` for potential server-side filtering.
- Client-side list additionally filters by comparing each slot's `time_value` with the selected range.

## Screens and Components

- The screens are structured under `components/screens/*` and routed via files under `app/*` using Expo Router.

### Help & Support

- Route: `app/help-support.tsx` now renders a thin wrapper that mounts `HelpSupportScreen`.
- Screen: `components/screens/help-support/index.tsx` composes the UI.
- Subcomponents:
  - `header.tsx` – title and location selector.
  - `support-type-select.tsx` – support type dropdown.
  - `message-field.tsx` – message textarea.
  - `submit-bar.tsx` – fixed submit button.
- Hook: `components/screens/help-support/useHelpSupportForm.ts` encapsulates form state, submission, and validation.
- Data: `data/screens/help-support/queries/useSupport.ts` fetches types and submits the request; `data/api-client/support.ts` includes Zod validation for API responses.
