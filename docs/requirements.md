## Facility filter multi-selection

## Facility hours

- On date select, refetch facilities with `?date=<selected yyyy-MM-dd>`.
- Determine display from the slot matching the selected date.
- If `has_closure = false` and `closed_allday = false`: show "Today's hours" with slot time `start_time - end_time`.
- If `has_closure = true` and `closed_allday = true`: show "Today's hours" with `closure_reason` instead of times.
- If `has_closure = true` and `closed_allday = false`: show "Updated Hours" with `start_time - end_time`.

## Activities filters: time range and categories

- Activities screen must support filtering by a time range: `start_time` and `end_time` expressed as 24h strings (e.g., `06:00`, `18:00`).
- Default time range is 04:00–24:00. Values at defaults are treated as inactive.
- Activities should also support category filtering with multiple categories displayed; at minimum one category can be applied to the request.
- Client-side filtering must honor the selected time range when server does not filter.
- The filter UI uses a single `timeRange` field with `{ startTime, endTime }` and maps to `start_time`/`end_time` in query params when applied.

- The facilities/location filter must support selecting multiple facilities.
- Backend filtering is authoritative; the UI must pass selected IDs via `gym_id` as a comma-separated string (e.g., `gym_id=1,2,3`).
- Components consuming the filter should accept `string[]` for the selected facilities and join them when building query params.

Affected areas:

- `components/shared/facility-filter` accepts `value: string[]` and `onChange: (value: string[]) => void`.
- `app/(tabs)/classes` and `app/appointments` pass joined `gym_id` to their queries and routes.

## Challenges

- List of challenges accessible from `More → Challenges`.
- Two tabs: Active and History.
- Each item shows: image, title, participants (+N), days left, progress percentage with bar, and Join/Joined button.
- Search by name.
- Pull to refresh.
- Pixel-match the provided list and details designs (tabs, info rows, join action).
