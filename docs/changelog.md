## 2025-08-23

- Refactor Help & Support screen into modular components.
  - New: `components/screens/help-support/*` (header, select, message, submit bar, screen, hook)
  - Route `app/help-support.tsx` now a thin wrapper.
  - API: `data/api-client/support.ts` now validates responses with Zod.
  - No visual changes; structure improves extensibility.
# 2025-08-24

- Facility hours behavior update:
  - API client: `fetchFacilities(date?)` now handles responses wrapped in `data`, coerces id/capacity numbers, and normalizes `slots` to an empty array when missing. Slot flags `has_closure`/`closed_allday` are coerced from number/string to boolean.
  - Query: `useFacilities(date)` keys by date and forwards it to the client for refetch on date change.
  - UI: `app/facility-hours.tsx` shows labels based on slot for selected date:
    - "Today's hours" with `start_time - end_time` when no closure and not closed all day.
    - "Today's hours" with `closure_reason` when closure and closed all day.
    - "Updated Hours" with `start_time - end_time` when closure and not closed all day.
  - Loading: Added `CardSkeletonLoader` to Facility Hours while fetching facilities.
  
 - Activity Log moved to live data:
   - API client: `data/api-client/challenge-logs.ts` with `fetchChallengeEventLogs(params)`.
   - Hook: `useActivityLogsQuery(params)` validates with Zod schema in `data/screens/activity-log/types.ts`.
   - Screen: `app/activity-log.tsx` now groups logs by date and renders sections dynamically.
# Changelog

## [2024-12-19] - Prettier and ESLint Setup

### Added
- **Prettier Configuration**: Set up Prettier with custom formatting rules
  - Single quotes preference
  - 80 character line width
  - 2 space indentation
  - Trailing commas enabled
  - Consistent spacing rules
- **ESLint Integration**: Enhanced ESLint configuration with Prettier integration
  - Added `eslint-config-prettier` to prevent conflicts
  - Configured code quality rules (no-var, prefer-const, object-shorthand, etc.)
  - Removed formatting rules that conflict with Prettier
- **VS Code Configuration**: Added workspace settings for automatic formatting
  - Format on save enabled
  - Prettier as default formatter
  - ESLint auto-fix on save
  - Recommended extensions list
- **NPM Scripts**: Added convenient commands for code quality
  - `npm run format`: Format all files with Prettier
  - `npm run format:check`: Check formatting without changes
  - `npm run lint`: Run ESLint on all files
  - `npm run lint:fix`: Auto-fix ESLint issues
  - `npm run format:fix`: Format and fix all issues
- **Pre-commit Hooks**: Set up Husky for automatic code quality checks
  - Automatic formatting before commits
  - ESLint auto-fix before commits
  - Prevents committing code with formatting issues

### Changed
- **Code Formatting**: Applied Prettier formatting to entire codebase
  - 256 files formatted automatically
  - Consistent code style across all TypeScript/JavaScript files
  - Resolved formatting conflicts between ESLint and Prettier
- **Code Quality**: Started addressing ESLint issues systematically
  - Fixed 13+ unused variable imports
  - Removed unused imports from multiple components
  - Cleaned up reservation card, auth layouts, and filter components

### Technical Details
- Prettier version: 3.6.2
- ESLint version: 9.30.1
- Husky version: Latest (for pre-commit hooks)
- Integration: `eslint-config-prettier` for conflict resolution
- VS Code extensions: Prettier, ESLint, TypeScript support
- Configuration files: `.prettierrc`, `.prettierignore`, `.vscode/settings.json`, `.husky/pre-commit`

### Progress Status
- **Formatting**: ✅ All files properly formatted
- **ESLint Errors**: ⚠️ 6,268 remaining (down from 6,281)
- **Auto-fixable**: 7,767+ issues can be automatically resolved
- **Pre-commit Hooks**: ✅ Configured and ready

### Next Steps
- Continue addressing remaining ESLint code quality issues
- Focus on unused variables, console statements, and React best practices
- Consider adding more specific ESLint rules for React Native
- Monitor pre-commit hook performance and adjust as needed

### 2025-08-08

### 2025-08-09

- Added Challenges feature:
  - New screen `app/challenges.tsx` with header tabs (Active/History), search, and list.
  - Data layer: `data/api-client/challenges.ts`, query hook `useChallengesQuery`, and zod schemas in `data/screens/challenges/types.ts`.
  - UI: `ChallengeCard`, `ChallengesList`, `ChallengesHeader` matching the provided design with progress and join button.
  - Navigation: entry from `More → Challenges`; route registered in `app/_layout.tsx`.

- Challenges join flow:
  - Added `joinChallenge` API `POST /challenges/participate` with body `{ challenge_id }` matching backend envelope `{ success, data }`.
  - Extended `ChallengeSchema.has_challenge_participation` to accept participation object with progress fields.
  - Implemented `useJoinChallengeMutation` to join and invalidate challenges query.
  - Wired join actions in `ChallengeCard`, `ChallengesList`, and `ChallengeDetails`.

- Challenge details + leave flow:
  - Added details fetch `GET /challenges/:id` via `apiClient.getChallengeById` and hook `useChallengeDetailsQuery` used in `app/(challenge-details)/[id].tsx`.
  - Added leave API `POST /challenges/exit` with `{ challenge_id }`, client `leaveChallenge`, and hook `useLeaveChallengeMutation` with cache invalidation.
  - Updated `ChallengeDetails` to show a rounded 'Leave challenge' button when joined and a confirmation actionsheet with 'No, stay' and 'Yes, leave'.
  - Added progress section on challenge details using `Progress` bar when user is participating, showing percentage based on `has_challenge_participation.progress_total_percentage`.

- Activities: added time range filter (maps to `start_time`/`end_time`), preserved categories multiselect UI.
- Updated query params and API client to forward `start_time` and `end_time` when applied.
- Client-side filtering respects selected time range when backend does not filter.

### 2025-08-14

- Activities: separated reservation form UI/logic from `ActivityEquipmentCard` into a new component `components/screens/activities/activity-equipment-form.tsx`.
  - Card now renders the form component only when expanded.
  - Extracted responsibilities: card handles display/expand/cancel; form handles start time, duration, attendees, and submit.

- Activities reservation: switched to live reservation API.
  - Updated client to call `POST /reserve` with payload `{ equipment_id, date, time, duration, type: 'equipment', attending_persons }`.
  - Wired selected date from `ActivityEquipmentCard` into `ActivityEquipmentForm` to build the correct `date` field (formatted `yyyy-MM-dd`).
  - Request/response types updated in `data/screens/activities/types.ts`.
  - Form submission now requires time and duration; attendees defaults to 1 when not applicable.

### 2025-01-27

- Fixed TypeScript errors in reservations feature:
  - Created missing `ReservationCard` component in `components/screens/reservations/reservation-card.tsx` with proper UI layout matching the design pattern of other card components.
  - Created missing `ReservationsHeader` component in `components/screens/reservations/reservations-header.tsx` with tab navigation between upcoming and past reservations.
  - Updated `ReservationsHeader` to match design with back arrow, proper title styling, and underline tabs instead of rounded buttons.
  - Updated `ReservationCard` to match design with circular avatar, simplified layout, and cancel button on the right.
  - Fixed icon import in `components/screens/weather/hourly-card/index.tsx` by replacing non-existent `@/components/shared/icon` imports with proper Lucide React Native icons.
  - Reduced total TypeScript errors from 34 to 32, resolving all reservation-related compilation issues.
  - Reservation feature now compiles successfully and displays properly with header tabs, category filters, search, and reservation cards showing all relevant details.
