import { useMutation, useQueryClient } from '@tanstack/react-query';
import { formatDate } from '@/data/common/common.utils';
import { api } from '@/lib/api';
import { invalidateByGroup } from '@/lib/query-invalidation';

// Base reservation data that both classes and activities share
interface BaseReservationData {
  date: string;
  is_virtual?: boolean;
}

interface ClassReservationData extends BaseReservationData {
  class_id: number;
  type: 'class';
}

// Activity-specific reservation data
interface ActivityReservationData extends BaseReservationData {
  equipment_id: number;
  time: string;
  duration: number;
  attending_persons: number;
  type: 'equipment';
}


export type ReservationData = ClassReservationData | ActivityReservationData;

// Type guard to check if it's a class reservation
const isClassReservation = (data: ReservationData): data is ClassReservationData => {
  return data.type === 'class';
};

// Type guard to check if it's an activity reservation
const isActivityReservation = (data: ReservationData): data is ActivityReservationData => {
  return data.type === 'equipment';
};

// Unified reservation function
export const reserveAction = async (data: ReservationData) => {
  try {
    const result = await api
      .post('reserve', {
        json: {
          ...data,
          date: formatDate(data.date),
        },
      })
      .json();

    return result;
  } catch (err) {

    if (err instanceof Error) {
      throw err;
    }
    throw new Error(String(err));
  }
};

// Hook options
interface UseReserveMutationOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}


export const useReserveMutation = (options?: UseReserveMutationOptions) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async (_, variables) => {
    
      await invalidateByGroup(queryClient, 'RESERVATION_MADE');
      
    
      if (isClassReservation(variables)) {
        await invalidateByGroup(queryClient, 'CLASS_RESERVATION_CHANGED');
      } else if (isActivityReservation(variables)) {
        await invalidateByGroup(queryClient, 'ACTIVITY_RESERVATION_CHANGED'); // Also invalidate activity time slots for the specific equipment
        await queryClient.invalidateQueries({
          queryKey: ['activity-time-slots', variables.equipment_id],
        });
      }
      
      options?.onSuccess?.();
    },
    onError: (error) => {
      options?.onError?.(error);
    },
  });
};
