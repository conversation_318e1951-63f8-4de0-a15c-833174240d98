import { apiClient } from '@/data/api-client';
import { useSession } from '@/modules/login/auth-provider';
import { useQuery } from '@tanstack/react-query';

export const useClientInfo = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ['client-info', sessionData?.university_id],
    queryFn: () =>
      apiClient.getClientInfoByOrgId({
        orgId: sessionData?.university_id as string,
      }),
    enabled: !!sessionData?.university_id,
  });
};
