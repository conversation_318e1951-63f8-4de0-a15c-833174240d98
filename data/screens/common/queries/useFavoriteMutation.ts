import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/data/api-client';
import { ToastManager } from '@/components/shared/toast/ToastManager';
import { invalidateByGroup } from '@/lib/query-invalidation';

export const useFavoriteMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.toggleFavorite,
    onSuccess: data => ToastManager.show(data.message, 'success'),
    onSettled: async () => {
      await invalidateByGroup(queryClient, 'FAVORITE_TOGGLED');
    },
  });
};
