import { useQuery } from '@tanstack/react-query';
import {
  reservationsApi,
  type ReservationsQueryParams,
} from '@/data/api-client/reservations';

export const useReservationsQuery = (params: ReservationsQueryParams) => {
  return useQuery({
    queryKey: ['reservations', params],
    queryFn: async () => {
      const raw = await reservationsApi.getReservations(params);
      return raw.data;
    },
  });
};
