import { z } from 'zod';

// Raw API schema as returned by /reservations/mine
export const ApiReservationSchema = z.object({
  id: z.coerce.number(),
  created: z.string(),
  updated: z.string(),
  class_id: z.coerce.number().nullable(),
  ref_type: z.coerce.number().nullable(),
  user_id: z.coerce.number(),
  trainer_id: z.coerce.number().nullable(),
  start_time: z.string(),
  end_time: z.string(),
  active: z.coerce.number().nullable(),
  deleted: z.coerce.number().nullable(),
  checkin: z.coerce.number().nullable(),
  is_no_show: z.coerce.number().nullable(),
  checked_in_at: z.string().nullable(),
  has_feedback: z.coerce.number().nullable(),
  is_member: z.coerce.number().nullable(),
  is_virtual: z.coerce.number().nullable(),
  membership_id: z.coerce.number().nullable(),
  paid_session_id: z.coerce.number().nullable(),
  decrements_sync_id: z.coerce.number().nullable(),
  attending_persons: z.coerce.number().nullable(),
  cancelled_on: z.string().nullable(),
  cancellation_source: z.string().nullable(),
  source: z.string().nullable(),
  added_by: z.coerce.number().nullable(),
  notes: z.string().nullable(),
  equ_id: z.coerce.number().nullable(),
  meta: z.string().nullable(),
  schedule_id: z.coerce.number().nullable(),
  child_name: z.string().nullable(),
  child_age: z.string().nullable(),
  trainer_pay_amount: z.coerce.number().nullable(),
  equipment_id: z.coerce.number().nullable(),
  equipment_type_id: z.coerce.number().nullable(),
  equipment_name: z.string().nullable(),
  classes_id: z.coerce.number().nullable(),
  class_name: z.string().nullable(),
  is_sgt: z.coerce.number().nullable(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string(),
  image: z.string().nullable(),
  instructor_first_name: z.string().nullable(),
  instructor_last_name: z.string().nullable(),
  instructor_email: z.string().nullable(),
  added_by_first_name: z.string().nullable(),
  added_by_last_name: z.string().nullable(),
  gym_name: z.string().nullable(),
  gym_id: z.coerce.number().nullable(),
  room_id: z.coerce.number().nullable(),
  room_name: z.string().nullable(),
  membership_purchase_id: z.coerce.number().nullable(),
  membership_package: z.string().nullable(),
  client_name: z.string().nullable(),
  pt_equipment_name: z.string().nullable(),
  cancellation_user: z.coerce.number().nullable(),
  type: z.string().nullable(),
});

export type ApiReservation = z.infer<typeof ApiReservationSchema>;

export const ApiReservationsResponseSchema = z.object({
  data: z.object({
    reservations: z.array(ApiReservationSchema),
  }),
  success: z.boolean(),
});

export type ApiReservationsResponse = z.infer<
  typeof ApiReservationsResponseSchema
>;

// UI-friendly normalized model used by components
export const ReservationCategorySchema = z.enum([
  'class',
  'appointment',
  'equipment',
]);
export type ReservationCategory = z.infer<typeof ReservationCategorySchema>;

export const ReservationStatusSchema = z.enum(['upcoming', 'past']);
export type ReservationStatus = z.infer<typeof ReservationStatusSchema>;

export const ReservationSchema = z.object({
  id: z.number(),
  title: z.string(),
  subtitle: z.string().optional().default(''),
  dateLabel: z.string(),
  timeLabel: z.string(),
  instructor: z.string().optional().nullable(),
  location: z.string().optional().nullable(),
  imageUrl: z.string().optional().nullable(),
  category: ReservationCategorySchema,
  status: ReservationStatusSchema,
  attendingPersons: z.number().optional().nullable(),
  room: z.string().optional().nullable(),
  startDate: z.string().optional().nullable(), 
  startTime: z.string().optional().nullable(),
  endTime: z.string().optional().nullable(), 
  type: z.string().optional().nullable(),
});

export type Reservation = z.infer<typeof ReservationSchema>;
