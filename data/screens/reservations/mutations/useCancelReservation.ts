import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { invalidateByGroup } from '@/lib/query-invalidation';
import { removeCalendarEventForReservation } from '@/utils/calendar';

export const cancelReservation = async (id: number) => {
  const res = await api
    .post('reservations/cancel', {
      json: { reservation_id: id },
    })
    .json();
  return res;
};

export const useCancelReservation = (onSuccess?: () => void) => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: cancelReservation,
    onSuccess: async (_data, reservationId) => {
      try {
        await removeCalendarEventForReservation(reservationId as number);
      } catch {}
      await onSuccess?.();
      await invalidateByGroup(qc, 'RESERVATION_CANCELLED');
    },
  });
};
