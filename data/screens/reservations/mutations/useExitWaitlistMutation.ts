import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { invalidateByGroup } from '@/lib/query-invalidation';

export const exitWaitlist = async (waitlistId: number) => {
  const res = await api
    .post('waitlists/exit', {
      json: { waitlist_id: waitlistId },
    })
    .json();
  return res;
};

interface UseExitWaitlistOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export const useExitWaitlistMutation = (options?: UseExitWaitlistOptions) => {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: exitWaitlist,
    onSuccess: async () => {
      await invalidateByGroup(qc, 'CLASS_RESERVATION_CHANGED');
      options?.onSuccess?.();
    },
    onError: (err) => {
      options?.onError?.(err as Error);
    },
  });
};

