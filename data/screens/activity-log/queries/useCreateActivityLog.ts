import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createActivityLog, type CreateActivityLogRequest } from '@/data/api-client/activity-logs';
import { invalidateByGroup } from '@/lib/query-invalidation';

export const useCreateActivityLog = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateActivityLogRequest) => createActivityLog(data),
    onSuccess: async () => {
      await invalidateByGroup(queryClient, 'ACTIVITY_LOG_CREATED');
      onSuccess?.();
    },
  });
};


