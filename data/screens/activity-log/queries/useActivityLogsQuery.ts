import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/data/api-client';
import { ChallengeEventLogsResponseSchema } from '@/data/screens/activity-log/types';
import type { ChallengeEventLogsParams } from '@/data/api-client/challenge-logs';

export const useActivityLogsQuery = (params: ChallengeEventLogsParams = {}) => {
  return useQuery({
    queryKey: ['challenge-event-logs', params],
    queryFn: async () => {
      const raw = await apiClient.getChallengeEventLogs(params);
      const parsed = ChallengeEventLogsResponseSchema.parse(raw);
      return parsed.data;
    },
  });
};


