import { z } from 'zod';

export const ChallengeEventLogSchema = z
  .object({
    id: z.number(),
    created: z.string(),
    updated: z.string(),
    entry_date: z.string(),
    user_id: z.number(),
    challenge_id: z.number(),
    university_id: z.number(),
    activity_type_id: z.number(),
    quantity: z.number(),
    notes: z.string().nullable(),
    meta: z.any().nullable(),
    source: z.any().nullable(),
    active: z.union([z.number(), z.boolean()]),
    deleted: z.union([z.number(), z.boolean()]),
    challenge_title: z.string(),
    activity_type_name: z.string(),
  })
  .passthrough();

export const ChallengeEventLogsResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.array(ChallengeEventLogSchema),
});

export type ChallengeEventLog = z.infer<typeof ChallengeEventLogSchema>;
export type ChallengeEventLogsResponse = z.infer<
  typeof ChallengeEventLogsResponseSchema
>;


