import { useQuery } from '@tanstack/react-query';
import { EventsQueryParams } from '../types';
import { apiClient } from '@/data/api-client';

export const useEventsQuery = (params?: EventsQueryParams) => {
  return useQuery({
    queryKey: ['events', params],
    queryFn: () => {
      if (!params?.month_year) {
        throw new Error('Date parameter is required');
      }
      return apiClient.getEventsByDate({
        ...(params as Record<string, string>),
      });
    },
    enabled: !!params?.month_year,
  });
};
