import { useMutation, useQuery } from '@tanstack/react-query';
import { apiClient } from '@/data/api-client';

export const useSupportTypes = () =>
  useQuery({
    queryKey: ['support', 'types'],
    queryFn: () => apiClient.getSupportTypes(),
    staleTime: 1000 * 60 * 30, // 30 minutes
  });

export const useSubmitSupport = (onSuccess?: () => void) =>
  useMutation({
    mutationFn: apiClient.submitSupportEmail,
    onSuccess: () => {
      onSuccess?.();
    },
  });

