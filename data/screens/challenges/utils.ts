import type { Challenge } from './types';

type Participation = Exclude<
  Challenge['has_challenge_participation'],
  boolean | null | undefined
>;

export const isCurrentlyParticipating = (
  hasChallengeParticipation: Challenge['has_challenge_participation']
): boolean => {
  if (
    hasChallengeParticipation &&
    typeof hasChallengeParticipation === 'object'
  ) {
    const exited = (hasChallengeParticipation as Participation).exited as
      | boolean
      | number
      | undefined;
    return exited === false || exited === 0;
  }
  return false;
};

export const getParticipationProgress = (
  hasChallengeParticipation: Challenge['has_challenge_participation']
): number => {
  if (
    hasChallengeParticipation &&
    typeof hasChallengeParticipation === 'object'
  ) {
    const progress = (hasChallengeParticipation as Participation)
      .progress_total_percentage;
    return progress ?? 0;
  }
  return 0;
};
