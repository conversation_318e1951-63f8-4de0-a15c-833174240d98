import { z } from 'zod';

// Participation payload returned when a user joins a challenge
export const ChallengeParticipationSchema = z
  .object({
    id: z.number(),
    created: z.string(),
    updated: z.string(),
    entry_date: z.string(),
    exit_date: z.string().nullable(),
    exited: z.union([z.number(), z.boolean()]),
    completed: z.union([z.number(), z.boolean()]),
    completion_date: z.string().nullable(),
    user_id: z.number(),
    challenge_id: z.number(),
    university_id: z.number(),
    cached_challenge_progress: z.string().nullable().optional(),
    progress_details: z.any().optional(),
    progress_total_percentage: z.number().optional(),
  })
  .passthrough();

export const ChallengeSchema = z
  .object({
    id: z.number(),
    created: z.string(),
    updated: z.string(),
    title: z.string(),
    description: z.string().nullable().optional(),
    start_date: z.string(),
    end_date: z.string(),
    challenge_type: z.string(),
    // Details endpoint may return string or object; accept any
    challenge_rules: z.any().nullable().optional(),
    wearable_connection_required: z
      .union([z.number(), z.boolean()])
      .nullable()
      .optional(),
    reward_type: z.string().nullable().optional(),
    reward: z.string().nullable().optional(),
    external_document: z.string().nullable().optional(),
    default_image: z.string().nullable().optional(),
    university_id: z.number(),
    show_adv_exp: z.string().nullable().optional(),
    active: z.number(),
    deleted: z.number(),
    days_left: z.number(),
    challenge_participations_count: z.number(),
    has_challenge_participation: z
      .union([z.null(), ChallengeParticipationSchema])
      .optional(),
  })
  .passthrough();

export const ChallengesResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(ChallengeSchema),
});

export const ChallengeResponseSchema = z.object({
  success: z.boolean(),
  data: ChallengeSchema,
});

export type Challenge = z.infer<typeof ChallengeSchema>;
export type ChallengesResponse = z.infer<typeof ChallengesResponseSchema>;

export type ChallengeParticipation = z.infer<
  typeof ChallengeParticipationSchema
>;

export const JoinChallengeResponseSchema = z.object({
  success: z.boolean(),
  data: ChallengeSchema,
});

export type JoinChallengeResponse = z.infer<typeof JoinChallengeResponseSchema>;
