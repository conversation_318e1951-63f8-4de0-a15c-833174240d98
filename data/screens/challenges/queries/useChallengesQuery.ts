import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/data/api-client';
import {
  ChallengeResponseSchema,
  ChallengesResponseSchema,
  JoinChallengeResponseSchema,
} from '../types';
import type { ChallengesQueryParams } from '@/data/api-client/challenges';

export const useChallengesQuery = (params: ChallengesQueryParams) => {
  return useQuery({
    queryKey: ['challenges', params],
    queryFn: async () => {
      const raw = await apiClient.getChallenges(params);
      const parsed = ChallengesResponseSchema.parse(raw);
      return parsed.data;
    },
  });
};

export const useJoinChallengeMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (challengeId: number) => {
      const raw = await apiClient.joinChallenge(challengeId);
      return JoinChallengeResponseSchema.parse(raw);
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['challenges'] });
      queryClient.invalidateQueries({ queryKey: ['challenge'] });
    },
  });
};

export const useLeaveChallengeMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (challengeId: number) => {
      const raw = await apiClient.leaveChallenge(challengeId);
      return raw;
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['challenges'] });
      queryClient.invalidateQueries({ queryKey: ['challenge'] });
    },
  });
};

export const useChallengeByIdQuery = (challengeId: number | string) => {
  return useQuery({
    queryKey: ['challenge', challengeId],
    enabled: !!challengeId,
    queryFn: async () => {
      const raw = await apiClient.getChallengeById(challengeId);
      const parsed = ChallengeResponseSchema.parse(raw);
      return parsed.data;
    },
  });
};
