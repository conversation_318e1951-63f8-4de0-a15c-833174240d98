import { useQuery } from '@tanstack/react-query';
import { ActivitiesQueryParams } from '../types';
import { apiClient } from '@/data/api-client';
import { useSession } from '@/modules/login/auth-provider';

export const useActivitiesQuery = (params?: ActivitiesQueryParams) => {
  const { data: session } = useSession();

  return useQuery({
    queryKey: ['activities', params, session?.university_id],
    queryFn: () => {
      if (!params?.date) {
        throw new Error('Date parameter is required');
      }
      return apiClient.getActivitiesByDate({
        ...params,
        university_id: session?.university_id as string,
      });
    },
    enabled: !!params?.date,
  });
};
