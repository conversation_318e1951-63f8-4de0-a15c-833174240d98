import { apiClient } from '@/data/api-client';
import { useQuery } from '@tanstack/react-query';

export const useActivitiesDuration = (
  equipmentId: number,
  selectedStartTime: string
) => {
  return useQuery({
    queryKey: ['available-durations', equipmentId, selectedStartTime || ''],
    queryFn: async () => {
      return apiClient.getAvailableDurations(equipmentId, selectedStartTime);
    },
    enabled: <PERSON><PERSON><PERSON>(selectedStartTime),
    staleTime: Infinity,
  });
};
