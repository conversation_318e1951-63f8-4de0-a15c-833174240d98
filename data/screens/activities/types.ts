export interface TimeSlot {
  equ_slot_id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
}

export interface AvailableTimeSlot {
  time_value: string;
  time_label: string;
  full_date_time: string;
  minimum_end_time: string;
  full_minimum_end_time: string;
  reservations_at_slot: number;
  remarks: string;
  allow_reservations: boolean;
}

export interface Duration {
  value: number;
  label: string;
}

export interface AttendingPerson {
  value: number;
  label: string;
}

export interface CurrentUserReservation {
  id: number;
  class_id: number;
  checkin: number;
  start_time: string;
  end_time: string;
  attending_persons: number;
}

export interface ActivityEquipment {
  id: number;
  gym_id: number;
  room_id: number;
  total_slots: number;
  name: string;
  description: string;
  use_room_times: number;
  duration_time_interval: number;
  max_reservations: number;
  min_time_lt: string;
  mac_time_lt: string;
  max_attending_persons: number;
  type_id: number;
  start_date: string | null;
  end_date: string | null;
  image_url: string | null;
  type: string;
  gym_name: string;
  room_name: string;
  day_of_week: string;
  uniq: string;
  facility_closed: boolean;
  is_user_favorite: boolean;
  time_slots: TimeSlot[];
  available_time_slots: AvailableTimeSlot[];
  next_available_time_slot: AvailableTimeSlot | null;
  durations: Duration[];
  attending_persons: AttendingPerson[];
  status: string | null;
  current_status: string;
  is_favourite: boolean;
  current_user_reservation?: CurrentUserReservation;
}

export interface ActivityReservation {
  id: number;
  equipment_id: number;
  user_id: number;
  start_time: string;
  end_time: string;
  duration: number; // in minutes
  status: 'active' | 'cancelled' | 'completed';
  created_at: string;
}

export interface ActivityTimeSlot {
  start_time: string;
  end_time: string;
  available: boolean;
  equipment_id: number;
}

export interface ActivitiesApiResponse {
  success: boolean;
  equipment?: ActivityEquipment[];
  gyms?: { id: number; name: string }[];
}

export type AvailableDurationsResponse =
  | { success?: boolean; durations: Duration[] }
  | Duration[];

export interface ActivityReservationRequest {
  equipment_id: number;
  date: string; // yyyy-MM-dd
  time: string; // HH:mm
  duration: number; // in minutes
  type: 'equipment';
  attending_persons: number;
}

export interface ActivityReservationResponse {
  success: boolean;
  message: string;
  reservation_id?: number;
}

export interface ActivitiesQueryParams {
  date?: string;
  gym_id?: string;
  equipment_type_id?: string[];
  university_id?: string;
  start_time?: string;
  end_time?: string;
  only_available?: boolean;
}
