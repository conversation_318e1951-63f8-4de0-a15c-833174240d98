import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/data/api-client';
import type { OnDemandVideo } from '@/data/api-client/on-demand';

export const useOnDemandVideosByCategory = (categoryId?: number) => {
  return useQuery<OnDemandVideo[]>({
    queryKey: ['on-demand', 'videos', categoryId],
    queryFn: async () => {
      if (!categoryId) throw new Error('categoryId is required');
      return apiClient.getOnDemandVideosByCategory(categoryId);
    },
    enabled: !!categoryId,
  });
};

