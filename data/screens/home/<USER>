import React from 'react';
import type { Reservation } from '@/data/screens/reservations/types';
import type { ActivityCardData } from '@/components/shared/activity-card';

export type QuickNavItem = {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: string | number; className?: string }>;
  route?: string;
};

export type QuickAction = QuickNavItem;

export type HoursItem = {
  id: string;
  title: string; // Aquatics, Culture & Events, etc.
  status: 'open' | 'closed' | 'updating';
  percent: number; // progress ring value
  subtitle: string; // e.g. Today 5:30AM - 10PM
};

export type HomeExperience = ActivityCardData & {
  intensity?: 'Low' | 'Medium' | 'High';
};

export type ChallengeSummary = {
  id: string | number;
  title: string;
  progress: number; // 0-100
  imageUrl?: string;
  participants?: number; // participant count
};

export type GoalItem = {
  id: string;
  name: string;
  progress: number; // 0-100
  progressLabel: string; // e.g., 2 of 3 this week
  iconId?: string; // e.g., 'workout', 'classes'
};

export type GroupItem = {
  id: string;
  name: string;
  imageUrl?: string;
  members?: number;
};

export type BadgeItem = {
  id: string;
  name: string;
  color?: string;
};

export type HomeHeaderData = {
  gymName: string;
  firstName: string;
  bannerUrl?: string;
};

export type HomeData = {
  header: HomeHeaderData;
  quickNav: QuickNavItem[];
  reservations: Reservation[];
  hours: HoursItem[];
  quickActions: QuickAction[];
  experiences: HomeExperience[];
  challenges: ChallengeSummary[];
  goals: GoalItem[];
  groups: GroupItem[];
  badges: BadgeItem[];
};
