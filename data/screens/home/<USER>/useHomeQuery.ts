import { useQuery } from '@tanstack/react-query';
import { featureFlags } from '@/constants/feature-flags';
import { homeMockData } from '@/data/mocks/home';
import type { HomeData } from '@/data/screens/home/<USER>';

const fetchHomeData = async (): Promise<HomeData> => {
  if (featureFlags.USE_HOME_MOCKS) {
    return homeMockData;
  }
  // Placeholder for real API wiring later
  return homeMockData;
};

export const useHomeQuery = () => {
  return useQuery({
    queryKey: ['home'],
    queryFn: fetchHomeData,
    staleTime: 1000 * 60 * 5,
  });
};

