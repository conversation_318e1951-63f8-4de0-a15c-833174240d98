import { useToaster } from '@/components/screens/classes/class-card/toast';
import { formatDate } from '@/data/common/common.utils';
import { api } from '@/lib/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { uniqueId } from 'lodash/fp';

interface BookAppointmentData {
  appointment_type_id: number;
  trainer_id: number;
  date: string;
  time: string;
  duration?: number;
  notes?: string;
}

export const bookAppointmentAction = async (data: BookAppointmentData) => {
  try {
    const result = await api
      .post('appointments/book', {
        json: {
          ...data,
          date: formatDate(data.date),
        },
      })
      .json();

    return result;
  } catch (err) {
    // If err is already an Error object with a message, use it directly
    if (err instanceof Error) {
      throw err;
    }
    // Otherwise, create a new Error with the string representation
    throw new Error(String(err));
  }
};

export const useBookAppointment = (onSuccess?: () => void) => {
  const toast = useToaster(uniqueId('book-appointment'));

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bookAppointmentAction,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['appointments'],
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },

    onError: e => {
      console.log('Appointment booking error:', e);
      const errorMessage = e instanceof Error ? e.message : String(e);
      toast(errorMessage || 'Failed to book appointment. Please try again.');
    },
  });
};

// Mock booking function for testing (since we don't have real API yet)
export const mockBookAppointmentAction = async (data: BookAppointmentData) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Simulate random success/failure for testing
  if (Math.random() > 0.7) {
    throw new Error('Appointment slot is no longer available');
  }

  return {
    success: true,
    message: 'Appointment booked successfully',
    appointment_id: Math.floor(Math.random() * 1000),
  };
};

export const useMockBookAppointment = (onSuccess?: () => void) => {
  const toast = useToaster(uniqueId('book-appointment'));

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mockBookAppointmentAction,
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['appointments'],
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },

    onError: e => {
      console.log('Appointment booking error:', e);
      const errorMessage = e instanceof Error ? e.message : String(e);
      toast(errorMessage || 'Failed to book appointment. Please try again.');
    },
  });
};
