import { useQuery } from '@tanstack/react-query';
import { useSession } from '@/modules/login/auth-provider';
import { apiClient } from '@/data/api-client';

export const useClassesQuery = ({
  date,
  gym_id,
}: {
  date: string;
  gym_id?: string;
}) => {
  const { data: session } = useSession();

  return useQuery({
    queryKey: ['classes', date, gym_id],
    queryFn: () =>
      apiClient.getClassesByOrgId({
        date,
        orgId: session?.university_id as string,
        gym_id,
      }),
  });
};
