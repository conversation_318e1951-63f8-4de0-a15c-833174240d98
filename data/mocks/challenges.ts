// Lightweight mock payload to exercise the Challenges UI
export const challengesMockEnvelope = {
  success: true,
  data: {
    current_page: 1,
    total: 5,
    data: [
      {
        id: 101,
        created: '2025-08-01 09:00:00',
        updated: '2025-08-01 09:00:00',
        title: '100 burpees challenge',
        description:
          'A form of cardio exercise alternating bouts of intense anaerobic exercise with less intense recovery periods strengthening the core and overall body.',
        start_date: '2025-08-01 00:00:00',
        end_date: '2025-08-31 23:59:59',
        challenge_type: 'EVENT_LOG',
        challenge_rules: null,
        wearable_connection_required: 0,
        reward_type: 'DIGITAL',
        reward: 'Bragging rights',
        external_document: null,
        default_image:
          'https://images.unsplash.com/photo-1517963879433-6ad2b056d712?q=80&w=1200&auto=format&fit=crop',
        university_id: 33,
        show_adv_exp: '1 MONTH',
        active: 1,
        deleted: 0,
        days_left: 10,
        challenge_participations_count: 100,
        has_challenge_participation: false,
        progress_percent: 40,
      },
      {
        id: 102,
        created: '2025-08-01 09:00:00',
        updated: '2025-08-01 09:00:00',
        title: 'Weight loss - 22lbs',
        description: 'Lose 22lbs this month.',
        start_date: '2025-08-01 00:00:00',
        end_date: '2025-09-01 00:00:00',
        challenge_type: 'EVENT_LOG',
        challenge_rules: null,
        wearable_connection_required: 0,
        reward_type: 'DIGITAL',
        reward: 'PT session',
        external_document: null,
        default_image:
          'https://images.unsplash.com/photo-1546484959-fb12c1f1cbe9?q=80&w=1200&auto=format&fit=crop',
        university_id: 33,
        show_adv_exp: '1 MONTH',
        active: 1,
        deleted: 0,
        days_left: 16,
        challenge_participations_count: 18,
        has_challenge_participation: true,
        progress_percent: 75,
      },
      {
        id: 103,
        created: '2025-08-01 09:00:00',
        updated: '2025-08-01 09:00:00',
        title: 'Cycling - 2500 mins',
        description: 'Cycle for 2500 minutes',
        start_date: '2025-08-01 00:00:00',
        end_date: '2025-09-24 00:00:00',
        challenge_type: 'EVENT_LOG',
        challenge_rules: null,
        wearable_connection_required: 0,
        reward_type: 'DIGITAL',
        reward: 'Medal',
        external_document: null,
        default_image:
          'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?q=80&w=1200&auto=format&fit=crop',
        university_id: 33,
        show_adv_exp: '1 MONTH',
        active: 1,
        deleted: 0,
        days_left: 24,
        challenge_participations_count: 107,
        has_challenge_participation: false,
      },
      {
        id: 104,
        created: '2025-08-01 09:00:00',
        updated: '2025-08-01 09:00:00',
        title: 'Kettlebell swing repetitions',
        description: 'Perform kettlebell swings.',
        start_date: '2025-08-01 00:00:00',
        end_date: '2025-08-18 00:00:00',
        challenge_type: 'EVENT_LOG',
        challenge_rules: null,
        wearable_connection_required: 0,
        reward_type: 'DIGITAL',
        reward: 'PT session',
        external_document: null,
        default_image:
          'https://images.unsplash.com/photo-1594385208975-279e02a8b2d1?q=80&w=1200&auto=format&fit=crop',
        university_id: 33,
        show_adv_exp: '1 MONTH',
        active: 1,
        deleted: 0,
        days_left: 18,
        challenge_participations_count: 74,
        has_challenge_participation: false,
      },
      {
        id: 105,
        created: '2025-06-01 09:00:00',
        updated: '2025-07-01 09:00:00',
        title: 'Squats - 250 reps',
        description: 'Do 250 squats',
        start_date: '2025-06-01 00:00:00',
        end_date: '2025-07-01 00:00:00',
        challenge_type: 'EVENT_LOG',
        challenge_rules: null,
        wearable_connection_required: 0,
        reward_type: 'DIGITAL',
        reward: 'Badge',
        external_document: null,
        default_image:
          'https://images.unsplash.com/photo-1518310383802-640c2de311b2?q=80&w=1200&auto=format&fit=crop',
        university_id: 33,
        show_adv_exp: '1 MONTH',
        active: 0,
        deleted: 0,
        days_left: 0,
        challenge_participations_count: 45,
        has_challenge_participation: false,
      },
    ],
  },
} as const;
