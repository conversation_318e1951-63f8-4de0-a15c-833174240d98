import {
  Dumbbell,
  CalendarDays,
  Users,
  ActivitySquare,
  Plus,
  Medal,
  Clock,
  TicketPercent,
} from 'lucide-react-native';
import type { HomeData } from '@/data/screens/home/<USER>';

export const homeMockData: HomeData = {
  header: {
    gymName: 'YMCA, Center',
    firstName: 'Gideon',
    bannerUrl:
      'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?q=80&w=1200&auto=format&fit=crop',
  },
  quickNav: [
    { id: 'classes', label: 'Classes', icon: Dumbbell },
    { id: 'challenges', label: 'Challenges', icon: ActivitySquare },
    { id: 'groups', label: 'Groups', icon: Users },
    { id: 'activities', label: 'Activity', icon: CalendarDays },
  ],
  reservations: [
    {
      id: 1,
      title: 'High-intensity interval training',
      subtitle: 'Studio A • Downtown Center',
      dateLabel: 'Today',
      timeLabel: '12:00 PM - 12:45 PM',
      instructor: '<PERSON>',
      location: 'Studio A',
      imageUrl:
        'https://images.unsplash.com/photo-1554344728-77cf90d9ed26?q=80&w=1200&auto=format&fit=crop',
      category: 'class',
      status: 'upcoming',
      attendingPersons: 1,
      room: 'Studio A',
    },
    {
      id: 2,
      title: 'Pickleball (Open play)',
      subtitle: 'Gymnasium • City Center',
      dateLabel: 'Today',
      timeLabel: '2:30 PM - 3:30 PM',
      instructor: null,
      location: 'Gymnasium',
      imageUrl:
        'https://images.unsplash.com/photo-1592428055023-cf1bcd235d72?q=80&w=1200&auto=format&fit=crop',
      category: 'class',
      status: 'upcoming',
      attendingPersons: 2,
      room: 'Court 1',
    },
  ],
  hours: [
    {
      id: 'aquatics',
      title: 'Aquatics',
      status: 'open',
      percent: 71,
      subtitle: 'Sat-Sun • 10AM - 6PM',
    },
    {
      id: 'culture',
      title: 'Culture Pavilion',
      status: 'open',
      percent: 100,
      subtitle: 'Sat-Sun • 9AM - 9PM',
    },
    {
      id: 'golf',
      title: 'Golf View',
      status: 'closed',
      percent: 0,
      subtitle: 'Mon-Fri • Closed',
    },
  ],
  quickActions: [
    { id: 'add-pt', label: 'Add a PT', icon: Plus },
    { id: 'classes', label: 'Classes', icon: Dumbbell },
    { id: 'checkins', label: 'Check-ins', icon: Clock },
    { id: 'coupon', label: 'Coupon', icon: TicketPercent },
    { id: 'log-activity', label: 'Log & track', icon: ActivitySquare },
    { id: 'goals', label: 'Goals', icon: Medal },
  ],
  experiences: [
    {
      id: '501',
      title: 'Pickleball (Open play)',
      imageUrl:
        'https://images.unsplash.com/photo-1609710228159-3f007bd2e722?q=80&w=1200&auto=format&fit=crop',
      startTime: '2025-08-21 13:00:00',
      endTime: '2025-08-21 14:00:00',
      spotsLeft: 2,
      instructor: 'Staff hosted',
      location: 'Gym 2 • Downtown Center',
      intensity: 'Medium',
    },
  ],
  challenges: [
    {
      id: 'c1',
      title: '100 burpees challenge',
      progress: 40,
      imageUrl:
        'https://images.unsplash.com/photo-1546483875-ad9014c88eba?q=80&w=1200&auto=format&fit=crop',
      participants: 128,
    },
  ],
  goals: [
    {
      id: 'g1',
      name: 'Work out 3 times per week',
      progress: 67,
      progressLabel: '2 of 3 this week',
      iconId: 'workout',
    },
    {
      id: 'g2',
      name: 'Attend 8 classes per month',
      progress: 58,
      progressLabel: '5 of 8 this month',
      iconId: 'classes',
    },
  ],
  groups: [
    {
      id: 'grp1',
      name: 'Meditation club',
      imageUrl:
        'https://images.unsplash.com/photo-1506126613408-eca07ce68773?q=80&w=1200&auto=format&fit=crop',
      members: 23,
    },
    {
      id: 'grp2',
      name: 'Rep Warriors',
      imageUrl:
        'https://images.unsplash.com/photo-1571731956672-c8d54da64f17?q=80&w=1200&auto=format&fit=crop',
      members: 16,
    },
  ],
  badges: [
    { id: 'b1', name: 'Weekly Warrior', color: '#E6F9FC' },
    { id: 'b2', name: 'Consistent King', color: '#FFE8F2' },
    { id: 'b3', name: 'Challenge Champion', color: '#F1F5FF' },
  ],
};
