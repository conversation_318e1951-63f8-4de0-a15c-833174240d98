import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Message, Conversation, PopularQuestion } from '@/stores/ai-store';
import { POPULAR_QUESTIONS, SAMPLE_CONVERSATIONS } from '@/constants/ai-mock-data';

// Query Keys
export const AI_QUERY_KEYS = {
  conversations: ['ai', 'conversations'] as const,
  conversation: (id: string) => ['ai', 'conversation', id] as const,
  popularQuestions: ['ai', 'popular-questions'] as const,
  chatResponse: ['ai', 'chat-response'] as const,
} as const;

// Mock API functions (replace with real API calls later)
const mockAPI = {
  getConversations: async (): Promise<Conversation[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return SAMPLE_CONVERSATIONS;
  },

  getConversation: async (id: string): Promise<Conversation | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return SAMPLE_CONVERSATIONS.find(conv => conv.id === id) || null;
  },

  getPopularQuestions: async (): Promise<PopularQuestion[]> => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return POPULAR_QUESTIONS;
  },

  sendMessage: async (params: {
    conversationId?: string;
    message: string;
  }): Promise<{ message: Message; conversationId: string }> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock AI response
    const aiResponse: Message = {
      id: `msg-${Date.now()}`,
      content: `I understand you said: "${params.message}". How can I help you further?`,
      role: 'assistant',
      timestamp: new Date(),
    };

    return {
      message: aiResponse,
      conversationId: params.conversationId || `conv-${Date.now()}`,
    };
  },

  createConversation: async (params: {
    title: string;
    initialMessage?: string;
  }): Promise<Conversation> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const conversation: Conversation = {
      id: `conv-${Date.now()}`,
      title: params.title,
      messages: params.initialMessage ? [{
        id: `msg-${Date.now()}`,
        content: params.initialMessage,
        role: 'user',
        timestamp: new Date(),
      }] : [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return conversation;
  },

  deleteConversation: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 200));
    // Mock deletion
  },
};

// Query Hooks
export const useConversations = () => {
  return useQuery({
    queryKey: AI_QUERY_KEYS.conversations,
    queryFn: mockAPI.getConversations,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useConversation = (id: string) => {
  return useQuery({
    queryKey: AI_QUERY_KEYS.conversation(id),
    queryFn: () => mockAPI.getConversation(id),
    enabled: !!id,
  });
};

export const usePopularQuestions = () => {
  return useQuery({
    queryKey: AI_QUERY_KEYS.popularQuestions,
    queryFn: mockAPI.getPopularQuestions,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Mutation Hooks
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mockAPI.sendMessage,
    onSuccess: (data) => {
      // Invalidate conversations to refresh the list
      queryClient.invalidateQueries({ queryKey: AI_QUERY_KEYS.conversations });
      
      // Update specific conversation if it exists
      if (data.conversationId) {
        queryClient.invalidateQueries({ 
          queryKey: AI_QUERY_KEYS.conversation(data.conversationId) 
        });
      }
    },
  });
};

export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mockAPI.createConversation,
    onSuccess: () => {
      // Invalidate conversations to refresh the list
      queryClient.invalidateQueries({ queryKey: AI_QUERY_KEYS.conversations });
    },
  });
};

export const useDeleteConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mockAPI.deleteConversation,
    onSuccess: () => {
      // Invalidate conversations to refresh the list
      queryClient.invalidateQueries({ queryKey: AI_QUERY_KEYS.conversations });
    },
  });
};

// Custom hook for AI chat integration
export const useAIChatQuery = () => {
  const sendMessageMutation = useSendMessage();
  const createConversationMutation = useCreateConversation();
  
  const sendMessage = async (params: {
    conversationId?: string;
    message: string;
    createNewConversation?: boolean;
    conversationTitle?: string;
  }) => {
    try {
      if (params.createNewConversation && !params.conversationId) {
        // Create new conversation first
        const conversation = await createConversationMutation.mutateAsync({
          title: params.conversationTitle || 'New Chat',
          initialMessage: params.message,
        });
        
        // Then send the message
        return await sendMessageMutation.mutateAsync({
          conversationId: conversation.id,
          message: params.message,
        });
      } else {
        // Send message to existing conversation
        return await sendMessageMutation.mutateAsync({
          conversationId: params.conversationId,
          message: params.message,
        });
      }
    } catch (error) {
      throw error;
    }
  };

  return {
    sendMessage,
    isLoading: sendMessageMutation.isPending || createConversationMutation.isPending,
    error: sendMessageMutation.error || createConversationMutation.error,
  };
};
