import { api } from '@/lib/api';

export type OnDemandCategory = {
  id: number;
  name: string;
  category_image?: string | null;
  active?: number;
  videos_count?: number;
  created_at?: string; // backend may send created_at or created
  created?: string;
};

export const fetchOnDemandCategories = async (): Promise<
  OnDemandCategory[]
> => {
  try {
    const res = await api
      .get<{ data: OnDemandCategory[] }>('ondemand_videos/categories')
      .json();
    return Array.isArray(res.data) ? res.data : [];
  } catch (err) {
    throw new Error('Could not fetch on-demand categories');
  }
};

export type OnDemandVideo = {
  id: number;
  created: string;
  name: string;
  description?: string;
  video_thumbnail?: string;
  video_url: string;
  duration_minutes?: string;
  level?: string | null;
  equipments?: string;
  active?: number;
  category_id: number;
  category_name?: string;
  gym_name?: string;
  instructor_first_name?: string;
  instructor_last_name?: string;
  instructor_id?: number;
  is_new?: boolean;
  is_favourite?: boolean;
};

export const fetchOnDemandVideosByCategory = async (
  categoryId: number,
  universityId = 33
) => {
  try {
    const res = await api
      .get<{ success: boolean; data: OnDemandVideo[] }>(
        'ondemand_videos/unauth',
        {
          searchParams: {
            university_id: String(universityId),
            category_id: String(categoryId),
          },
        }
      )
      .json();
    return Array.isArray(res.data) ? res.data : [];
  } catch (err) {
    throw new Error('Could not fetch on-demand videos');
  }
};
