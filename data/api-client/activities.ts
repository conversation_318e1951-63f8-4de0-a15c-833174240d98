import { api } from '@/lib/api';
import {
  ActivitiesApiResponse,
  ActivitiesQueryParams,
  ActivityReservationRequest,
} from '../screens/activities/types';
// Remove mock data; use live API

export const fetchActivitiesByDate = async (params: ActivitiesQueryParams) => {
  try {
    const urlParams = new URLSearchParams();
    if (params.date) urlParams.append('date', params.date);
    if (params.gym_id) urlParams.append('gym_id', params.gym_id);
    if (params.university_id)
      urlParams.append('university_id', params.university_id);
    if (params.start_time) urlParams.append('start_time', params.start_time);
    if (params.end_time) urlParams.append('end_time', params.end_time);
    if (params.equipment_type_id)
      urlParams.append('equipment_type_id', params.equipment_type_id.join(','));
    if (params.only_available) urlParams.append('only_available', 'true');

    const response = await api
      .get<ActivitiesApiResponse>(
        `equipment/list?${urlParams.toString()}&exclude_if_closed_or_cancelled=true`
      )
      .json();
    return response;
  } catch (err) {
    throw new Error('Could not fetch activities');
  }
};

export const fetchAvailableDurations = async (
  equipmentId: number,
  startTime: string
) => {
  try {
    const response = await api
      .get<{
        success: boolean;
        data: { value: number; label: string }[];
      }>(
        `equipment/${equipmentId}/available_durations?start_time=${encodeURIComponent(startTime)}`
      )
      .json();

    return response?.data ?? [];
  } catch (err) {
    if (err instanceof Error) throw err;
    throw new Error(String(err));
  }
};

export const reserveActivity = async (data: ActivityReservationRequest) => {
  try {
    const response = await api
      .post<{ success: boolean; message: string; reservation_id?: number }>(
        'reserve',
        {
          json: data,
        }
      )
      .json();

    return response;
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error(String(err));
  }
};

export const cancelActivityReservation = async (reservationId: number) => {
  try {
    const response = await api
      .post<{ success: boolean; message?: string }>(
        'activities/reservations/cancel',
        {
          json: {
            reservation_id: reservationId,
          },
        }
      )
      .json();

    return response;
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error(String(err));
  }
};
