import { api } from '@/lib/api';
import { z } from 'zod';

// Coerce common API inconsistencies (number/boolean/string) into boolean
const booleanish = z.preprocess((value: unknown) => {
  if (value == null) return false;
  if (typeof value === 'boolean') return value;
  if (typeof value === 'number') return value === 1;
  const normalized = String(value).trim().toLowerCase();
  return (
    normalized === '1' ||
    normalized === 'true' ||
    normalized === 'yes' ||
    normalized === 'y'
  );
}, z.boolean());

const FacilitySlotSchema = z.object({
  id: z.coerce.number(),
  item_id: z.coerce.number().optional().nullable(),
  start_time: z.string().optional().nullable(), // e.g., "11:00 AM"
  end_time: z.string().optional().nullable(), // e.g., "8:30 AM"
  day_of_week_number: z.union([z.string(), z.number()]).optional().nullable(), // "1"-"7"
  day_of_week_name: z.string().optional().nullable(), // "mon"
  date: z.string().optional().nullable(), // yyyy-mm-dd
  has_closure: booleanish.optional().nullable(),
  closed_allday: booleanish.optional().nullable(),
  closure_reason: z.string().optional().nullable(),
});

const FacilitySchema = z.object({
  id: z.coerce.number(),
  name: z.string(),
  address: z.string().nullable().optional(),
  city: z.string().nullable().optional(),
  state: z.string().nullable().optional(),
  zip: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  url: z.string().nullable().optional(),
  capacity: z.coerce.number().nullable().optional(),
  primary_facility: z.coerce.number().nullable().optional(),
  closures: z.array(z.unknown()).optional().nullable(),
  alerts: z.array(z.unknown()).optional().nullable(),
  // Ensure null/undefined becomes []
  slots: z.preprocess(
    (value: unknown) => (Array.isArray(value) ? value : []),
    z.array(FacilitySlotSchema)
  ),
  rooms: z.array(z.unknown()).optional().nullable(),
});

export type Facility = z.infer<typeof FacilitySchema>;
export type FacilitySlot = z.infer<typeof FacilitySlotSchema>;

export const fetchFacilities = async (date?: string) => {
  try {
    const query = date ? `?date=${date}` : '';
    const res = await api.get(`facilities${query}`).json<unknown>();

    const raw =
      typeof res === 'object' && res !== null && 'data' in res
        ? (res as { data: unknown }).data
        : res;
    const items = Array.isArray(raw) ? raw : [];

    const parsed = z.array(FacilitySchema).safeParse(items);
    if (!parsed.success) {
      return [] as Facility[];
    }
    return parsed.data as Facility[];
  } catch (err) {
    return [] as Facility[];
  }
};
