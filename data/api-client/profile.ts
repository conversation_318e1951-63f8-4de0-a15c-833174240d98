import { api } from '@/lib/api';
import { UserProfileData } from '@/hooks/useUserProfile';

export interface UpdateProfileRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  sex?: string;
  dob?: string;
  // Additional fields that might be supported by the API
  height?: string;
  weight?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: any;
}

export const updateUserProfile = async (
  userId: string,
  profileData: UserProfileData
): Promise<UpdateProfileResponse> => {
  try {
    const requestData: UpdateProfileRequest = {
      first_name: profileData.firstName,
      last_name: profileData.lastName,
      email: profileData.email,
      phone: profileData.phone,
      sex: profileData.gender,
      // Convert age back to date of birth if needed
      // This would require more complex logic to calculate DOB from age
      height: profileData.height,
      weight: profileData.weight,
    };

    const response = await api
      .put(`users/${userId}`, { json: requestData })
      .json<UpdateProfileResponse>();
    
    return response;
  } catch (error) {
    console.error('Update profile error:', error);
    throw new Error('Failed to update profile');
  }
};

export const uploadProfileImage = async (
  userId: string,
  imageUri: string
): Promise<{ success: boolean; imageUrl?: string }> => {
  try {
    // This would implement actual image upload logic
    // For now, return a mock response
    console.log('Uploading profile image for user:', userId, 'URI:', imageUri);
    
    // In a real implementation, you would:
    // 1. Create FormData with the image file
    // 2. Upload to your image storage service (AWS S3, Cloudinary, etc.)
    // 3. Return the uploaded image URL
    
    return {
      success: true,
      imageUrl: imageUri, // Mock response
    };
  } catch (error) {
    console.error('Upload profile image error:', error);
    return { success: false };
  }
};
