import { api } from '@/lib/api';

export interface BookingRequest {
  trainerId: string;
  date: string;
  time: string;
  passId: string;
}

export interface BookingResponse {
  id: number;
  trainer_id: number;
  user_id: number;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  pass_id: number;
  created_at: string;
}

// Mock booking function - in real app this would make an API call
export const bookAppointment = async (booking: BookingRequest): Promise<BookingResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock successful response
  return {
    id: Math.floor(Math.random() * 1000),
    trainer_id: booking.trainerId === 'anyone' ? 0 : parseInt(booking.trainerId),
    user_id: 1, // Would come from session
    date: booking.date,
    time: booking.time,
    status: 'confirmed',
    pass_id: parseInt(booking.passId),
    created_at: new Date().toISOString(),
  };
};

// Mock function to get user's available passes
export const getUserPasses = async (): Promise<Array<{
  id: number;
  name: string;
  remaining: number | string;
  type: string;
}>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock passes data
  return [
    { id: 1, name: 'Personal Training Package - 10 sessions', remaining: 8, type: 'package' },
    { id: 2, name: 'Premium Membership', remaining: 'Unlimited', type: 'membership' },
    { id: 3, name: 'Monthly Training Pass', remaining: 5, type: 'monthly' },
  ];
};
