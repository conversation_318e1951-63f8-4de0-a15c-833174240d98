import { api } from '@/lib/api';
import {
  ApiReservationsResponseSchema,
  Reservation,
  ReservationStatus,
  ApiReservationSchema,
  ApiReservation,
} from '@/data/screens/reservations/types';
import { formatDate, obtainDateFrame } from '@/data/common/common.utils';
import { parse, subMonths, addMonths } from 'date-fns';
import { z } from 'zod';

export type ReservationsQueryParams = {
  status?: ReservationStatus;
  start_time?: string;
  end_time?: string;
  type?: 'class' | 'equipment' | 'pt';
};

const mapCategory = (type: ApiReservation['type']) => {
 
  if (type === 'class') return 'class';
  if (type === 'equipment') return 'equipment';
  if (type === 'pt') return 'appointment';
  return 'class';
};

const parseApiDate = (value: string | null | undefined) => {
  if (!value) return null;
  try {
    return parse(value, 'yyyy-MM-dd HH:mm:ss', new Date());
  } catch {
    return null;
  }
};

const normalizeReservation = (
  r: z.infer<typeof ApiReservationSchema>,
  status: ReservationStatus
): Reservation => {
  const category = mapCategory(r.type);
  const start = parseApiDate(r.start_time);
  const dateLabel = start ? formatDate(start) : '';
  const timeLabel = obtainDateFrame(r.start_time, r.end_time);
  return {
    id: r.id,
    title: r.class_name ?? r.equipment_name ?? 'Appointment',
    subtitle: r.gym_name ?? '',
    dateLabel,
    timeLabel,
    instructor:
      r.instructor_first_name && r.instructor_last_name
        ? `${r.instructor_first_name} ${r.instructor_last_name}`
        : null,
    location: r.room_name ?? null,
    imageUrl: r.image ?? null,
    category,
    status,
    attendingPersons: r.attending_persons,
    room: r.room_name,
    startDate: r.start_time?.split(' ')[0] ?? null,
    startTime: r.start_time?.split(' ')[1] ?? null,
    endTime: r.end_time?.split(' ')[1] ?? null,
  };
};

export const reservationsApi = {
  async getReservations(
    params: ReservationsQueryParams = {}
  ): Promise<{ data: Reservation[] }> {
    const today = new Date();

    let start: string;
    let end: string;

    if (params.status === 'past') {
      start = formatDate(subMonths(today, 6));
      end = formatDate(today);
    } else {
      start = formatDate(today);
      end = formatDate(addMonths(today, 6));
    }

    const searchParams: Record<string, string> = {
      start_time: start,
      end_time: end,
    };

    if (params.type) {
      searchParams.type = params.type;
    }

    const res = await api.get('reservations/mine', { searchParams }).json();
    const parsed = ApiReservationsResponseSchema.parse(res);
    const normalized =
      parsed.data.reservations?.map(r => normalizeReservation(r, params.status || 'upcoming')) ?? [];

    return { data: normalized };
  },
};
