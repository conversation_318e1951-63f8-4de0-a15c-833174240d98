import { api } from '@/lib/api';
import { z } from 'zod';

export type CreateActivityLogRequest = {
  activity_type_id: number;
  quantity: number;
  notes?: string;
  entry_date?: string; // ISO or server expected format
};

const CreateActivityLogResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z
    .object({
      id: z.number(),
    })
    .optional(),
});

export const createActivityLog = async (
  data: CreateActivityLogRequest
): Promise<{ success: boolean; message?: string; id?: number }> => {
  const response = await api
    .post('challenges/event/log', {
      json: data,
    })
    .json<unknown>();

  const parsed = CreateActivityLogResponseSchema.parse(response);
  return parsed;
};
