import { api } from '@/lib/api';
import { z } from 'zod';

export type ActivityType = {
  id: number;
  name: string;
  unit: string | null;
};

const ActivityTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
  unit: z.string().nullable(),
});

const ActivityTypesResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(ActivityTypeSchema).default([]),
});

export const fetchActivityTypes = async (): Promise<ActivityType[]> => {
  const res = await api.get('challenges/activity_types').json<unknown>();
  const parsed = ActivityTypesResponseSchema.parse(res);
  return parsed.data;
};


