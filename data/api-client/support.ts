import { api } from '@/lib/api';
import { z } from 'zod';

export type SupportType = {
  id: number;
  name: string;
};

const SupportTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
});
const SupportTypesResponseSchema = z.object({
  data: z.array(SupportTypeSchema).default([]),
});

// GET /support/types -> { data: SupportType[] }
export const fetchSupportTypes = async (): Promise<SupportType[]> => {
  const res = await api.get('support/types').json<unknown>();
  const parsed = SupportTypesResponseSchema.parse(res);
  return parsed.data;
};

// POST /support/email -> { success: boolean, message?: string }
export const submitSupportEmail = async (payload: {
  support_type_id: number;
  message: string;
}): Promise<{ success: boolean; message?: string }> => {
  const res = await api
    .post('support/email', {
      json: payload,
    })
    .json<unknown>();
  const ResponseSchema = z.object({ data: z.object({ success: z.boolean(), message: z.string().optional() }) });
  return ResponseSchema.parse(res).data;
};

