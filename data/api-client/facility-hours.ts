import { api } from '@/lib/api';
import { z } from 'zod';

const FacilityClosureSchema = z.object({
  id: z.number(),
  gym_id: z.number(),
  gym_name: z.string(),
  type_id: z.number().optional().nullable(),
  reason: z.string().nullable().optional(),
  comment: z.string().nullable().optional(),
  from_date: z.string(),
  to_date: z.string(),
  date: z.string().optional().nullable(),
  start_time: z.string(),
  end_time: z.string(),
  closed_allday: z.number().optional().nullable(),
  created: z.string().optional().nullable(),
  updated: z.string().optional().nullable(),
});

export type FacilityClosure = z.infer<typeof FacilityClosureSchema>;

export type FacilityClosuresParams = {
  uni_id: string;
  date?: string; // yyyy-mm-dd
  gym_ids?: string; // comma separated ids
};

export const fetchFacilityClosures = async (params: FacilityClosuresParams) => {
  const urlParams = new URLSearchParams({
    ...params,
  }).toString();

  const response = await api
    .get<{ data: unknown }>(`facilities/closing_days/list?${urlParams}`)
    .json();

  // Some orgs might return top-level array under data or directly an array; normalize
  const raw =
    typeof response === 'object' && response !== null && 'data' in response
      ? (response as { data: unknown }).data
      : response;

  const items = Array.isArray(raw) ? raw : [];

  const parsed = z.array(FacilityClosureSchema).safeParse(items);

  if (!parsed.success) {
    // Fallback to empty list if validation fails for safety
    return [] as FacilityClosure[];
  }

  return parsed.data as FacilityClosure[];
};
