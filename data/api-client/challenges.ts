import { api } from '@/lib/api';

export interface ChallengesQueryParams {
  page?: number;
  start_date?: string;
  end_date?: string;
  active_only?: boolean;
  history_only?: boolean;
}

type ChallengesApiEnvelope = {
  success: boolean;
  data: unknown;
};

export const fetchChallenges = async (params: ChallengesQueryParams = {}) => {
  const { page = 1, start_date, end_date, active_only, history_only } = params;

  const response = await api
    .get('challenges', {
      searchParams: {
        // page,
        ...(start_date ? { start_date } : {}),
        ...(end_date ? { end_date } : {}),
        ...(typeof active_only === 'boolean' ? { active_only } : {}),
        ...(typeof history_only === 'boolean' ? { history_only } : {}),
      },
    })
    .json();

  return response;
};

export const joinChallenge = async (challengeId: number) => {
  const response = await api
    .post('challenges/participate', {
      json: { challenge_id: challengeId },
    })
    .json<ChallengesApiEnvelope>();

  return response;
};

export const fetchChallengeById = async (id: number | string) => {
  const response = await api
    .get(`challenges/${id}`)
    .json<ChallengesApiEnvelope>();
  return response;
};

export const leaveChallenge = async (challengeId: number) => {
  const response = await api
    .post('challenges/exit', {
      json: { challenge_id: challengeId },
    })
    .json<ChallengesApiEnvelope>();
  return response;
};
