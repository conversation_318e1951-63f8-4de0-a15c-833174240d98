import { api } from '@/lib/api';

export type ChallengeEventLogsParams = {
  challenge_id?: string | number;
  start_date?: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
};

export const fetchChallengeEventLogs = async (
  params: ChallengeEventLogsParams = {}
) => {
  const { challenge_id, start_date, end_date } = params;

  const response = await api
    .get('challenges/event/logs', {
      searchParams: {
        ...(challenge_id ? { challenge_id } : {}),
        ...(start_date ? { start_date } : {}),
        ...(end_date ? { end_date } : {}),
      },
    })
    .json();

  return response;
};


