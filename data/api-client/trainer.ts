/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from '@/lib/api';
import { Trainer } from '../screens/appointments/types';

export const fetchTrainersBySessionId = async (id: string) => {
  try {
    const response = await api
      .get<{
        data: Trainer[];
      }>(`users/trainers?session_id=${id}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error('Could not fetch appointments');
  }
};

export const fetchTrainersByOrgId = async ({ orgId }: { orgId: string }) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: orgId,
      per_page: '600',
    }).toString();

    const response = await api
      .get<{
        trainers: Trainer[];
      }>(`trainers/unauth?${urlParams}`)
      .json();

    return response.trainers;
  } catch (err) {
    throw new Error('Could not fetch appointments');
  }
};
