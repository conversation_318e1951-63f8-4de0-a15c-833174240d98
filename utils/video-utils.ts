/**
 * Utility functions for video URL detection and handling
 */

// Common video file extensions
const VIDEO_EXTENSIONS = [
  '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', 
  '.m4v', '.3gp', '.ogv', '.m3u8', '.ts', '.mpeg', '.mpg'
];

/**
 * Checks if a URL points to a direct video file
 */
export const isVideoFile = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    
    // Check file extension
    const hasVideoExtension = VIDEO_EXTENSIONS.some(ext => 
      pathname.endsWith(ext)
    );
    
    if (hasVideoExtension) return true;
    
    // Check for video in query parameters (common for streaming)
    const searchParams = urlObj.searchParams;
    const hasVideoParam = searchParams.has('format') && 
      (searchParams.get('format')?.includes('video') ?? false);
    
    return hasVideoParam;
  } catch {
    // If URL parsing fails, check if it looks like a video file
    return VIDEO_EXTENSIONS.some(ext => url.toLowerCase().includes(ext));
  }
};

/**
 * Checks if a URL is a YouTube URL (including short URLs)
 */
export const isYouTubeUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  const youtubePatterns = [
    /youtube\.com/i,
    /youtu\.be/i,
    /m\.youtube\.com/i,
    /www\.youtube\.com/i
  ];
  
  return youtubePatterns.some(pattern => pattern.test(url));
};

/**
 * Checks if a URL is a streaming service that should use WebView
 */
export const isStreamingService = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  const streamingPatterns = [
    /youtube\.com/i,
    /youtu\.be/i,
    /vimeo\.com/i,
    /dailymotion\.com/i,
    /twitch\.tv/i,
    /facebook\.com.*\/videos/i,
    /instagram\.com.*\/p\//i,
    /tiktok\.com/i
  ];
  
  return streamingPatterns.some(pattern => pattern.test(url));
};

/**
 * Determines the appropriate player type for a given URL
 */
export const getPlayerType = (url: string): 'video' | 'webview' => {
  if (!url || typeof url !== 'string') return 'webview';
  
  // If it's a direct video file, use VideoView
  if (isVideoFile(url)) {
    return 'video';
  }
  
  // If it's a streaming service, use WebView
  if (isStreamingService(url)) {
    return 'webview';
  }
  
  // Default to WebView for unknown URLs
  return 'webview';
};

/**
 * Expands short URLs (basic implementation)
 * Note: This is a simplified version. For production, you might want to use
 * a library like 'url-expand' or implement proper URL expansion
 */
export const expandShortUrl = async (url: string): Promise<string> => {
  if (!url || typeof url !== 'string') return url;
  
  // Common short URL patterns
  const shortUrlPatterns = [
    /bit\.ly/i,
    /tinyurl\.com/i,
    /short\.link/i,
    /t\.co/i,
    /goo\.gl/i,
    /ow\.ly/i,
    /is\.gd/i,
    /v\.gd/i
  ];
  
  const isShortUrl = shortUrlPatterns.some(pattern => pattern.test(url));
  
  if (!isShortUrl) return url;
  
  try {
    // For now, return the original URL
    // In a real implementation, you would make a HEAD request to get the final URL
    // This would require additional dependencies and network handling
    console.warn('Short URL detected but expansion not implemented:', url);
    return url;
  } catch (error) {
    console.error('Error expanding short URL:', error);
    return url;
  }
};
