import { Alert, Linking, Platform } from 'react-native';
import { format } from 'date-fns';
import * as SecureStore from 'expo-secure-store';

// Type-only import to avoid "any" while still supporting dynamic import
type CalendarModule = typeof import('expo-calendar');

export interface ClassEvent {
  title: string;
  startTime: string; // HH:mm:ss
  endTime: string; // HH:mm:ss
  date: string; // YYYY-MM-DD
  location?: string;
  description?: string;
  instructor?: string;
  reservationId?: number; // Used to map and later remove event on cancel
}

const STORAGE_KEY = (reservationId: number) =>
  `__calendar_event_${reservationId}`;

async function openGoogleCalendarFallback(classEvent: ClassEvent) {
  const startDate = new Date(`${classEvent.date}T${classEvent.startTime}`);
  const endDate = new Date(`${classEvent.date}T${classEvent.endTime}`);

  const formatForCalendar = (date: Date) =>
    format(date, "yyyyMMdd'T'HHmmss'Z'");
  const title = encodeURIComponent(classEvent.title);
  const details = encodeURIComponent(
    `${classEvent.description || 'Fitness class'}\n\nInstructor: ${classEvent.instructor || 'TBD'}`
  );
  const location = encodeURIComponent(classEvent.location || '');
  const url = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${formatForCalendar(
    startDate
  )}/${formatForCalendar(endDate)}&details=${details}&location=${location}`;

  const canOpen = await Linking.canOpenURL(url);
  if (canOpen) {
    await Linking.openURL(url);
  } else {
    Alert.alert(
      'Add to Calendar',
      `Event: ${classEvent.title}\nDate: ${format(startDate, 'MMM dd, yyyy')}\nTime: ${format(startDate, 'h:mm a')} - ${format(endDate, 'h:mm a')}\n\nPlease manually add this event to your calendar.`,
      [{ text: 'OK' }]
    );
  }
}

async function getWritableCalendarId(
  Calendar: CalendarModule
): Promise<string | null> {
  try {
    if (Platform.OS === 'ios' && Calendar.getDefaultCalendarAsync) {
      const defaultCal = await Calendar.getDefaultCalendarAsync();
      if (defaultCal?.id) return defaultCal.id as string;
    }

    const calendars = await Calendar.getCalendarsAsync(
      Calendar.EntityTypes.EVENT
    );
    const writable = calendars?.find(
      c => (c as { allowsModifications?: boolean }).allowsModifications
    );
    return (
      (writable?.id as string | undefined) ??
      (calendars?.[0]?.id as string | undefined) ??
      null
    );
  } catch {
    return null;
  }
}

/**
 * Add to the device's native calendar. Falls back to Google Calendar URL if the
 * native module is unavailable or permission is denied.
 */
export const addToCalendar = async (classEvent: ClassEvent) => {
  try {
    // Try dynamic import so the app still builds even if expo-calendar isn't installed yet
    const Calendar = (await import('expo-calendar').catch(
      () => null
    )) as CalendarModule | null;

    const startDate = new Date(`${classEvent.date}T${classEvent.startTime}`);
    const endDate = new Date(`${classEvent.date}T${classEvent.endTime}`);

    if (!Calendar) {
      return openGoogleCalendarFallback(classEvent);
    }

    const perm = await Calendar.requestCalendarPermissionsAsync();
    if (!perm?.granted) {
      return openGoogleCalendarFallback(classEvent);
    }

    const calendarId = await getWritableCalendarId(Calendar);
    if (!calendarId) {
      return openGoogleCalendarFallback(classEvent);
    }

    const notes = [
      classEvent.description,
      classEvent.instructor ? `Instructor: ${classEvent.instructor}` : '',
    ]
      .filter(Boolean)
      .join('\n');

    const eventId = await Calendar.createEventAsync(calendarId, {
      title: classEvent.title,
      startDate,
      endDate,
      location: classEvent.location,
      notes,
      timeZone: undefined,
    });

    if (classEvent.reservationId) {
      await SecureStore.setItemAsync(
        STORAGE_KEY(classEvent.reservationId),
        JSON.stringify({ eventId, calendarId })
      );
    }

    return eventId;
  } catch {
    Alert.alert(
      'Calendar Error',
      'Unable to add event to calendar. Please try again or add manually.',
      [{ text: 'OK' }]
    );
  }
};

export const removeCalendarEventForReservation = async (
  reservationId: number
): Promise<boolean> => {
  try {
    const stored = await SecureStore.getItemAsync(STORAGE_KEY(reservationId));
    if (!stored) return false;
    const { eventId } = JSON.parse(stored || '{}') as { eventId?: string };

    const Calendar = (await import('expo-calendar').catch(
      () => null
    )) as CalendarModule | null;
    if (!Calendar || !eventId) {
      await SecureStore.deleteItemAsync(STORAGE_KEY(reservationId));
      return false;
    }

    // Ensure we have permission to modify the calendar before attempting delete
    const perm = await Calendar.requestCalendarPermissionsAsync();
    if (!perm?.granted) {
      return false;
    }

    await Calendar.deleteEventAsync(eventId);
    await SecureStore.deleteItemAsync(STORAGE_KEY(reservationId));
    return true;
  } catch {
    return false;
  }
};

/** Check if a calendar event has been stored for a given reservation */
export const hasCalendarEventForReservation = async (
  reservationId?: number
): Promise<boolean> => {
  if (reservationId === undefined || reservationId === null) return false;
  try {
    const stored = await SecureStore.getItemAsync(STORAGE_KEY(reservationId));
    if (!stored) return false;
    const { eventId } = JSON.parse(stored || '{}') as { eventId?: string };
    return !!eventId;
  } catch {
    return false;
  }
};

/**
 * Create a shareable text for a class reservation
 */
export const createShareText = (classEvent: ClassEvent): string => {
  const startDate = new Date(`${classEvent.date}T${classEvent.startTime}`);
  const endDate = new Date(`${classEvent.date}T${classEvent.endTime}`);

  return `🏋️‍♀️ I just booked a class!

📅 ${classEvent.title}
🗓️ ${format(startDate, 'EEEE, MMM dd, yyyy')}
⏰ ${format(startDate, 'h:mm a')} - ${format(endDate, 'h:mm a')}
👨‍🏫 Instructor: ${classEvent.instructor || 'TBD'}
📍 ${classEvent.location || ''}

Join me for a great workout! 💪`;
};
