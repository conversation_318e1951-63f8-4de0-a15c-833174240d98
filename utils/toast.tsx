import {
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@/components/ui/toast';

/**
 * Utility function to show an error toast notification
 * @param message The error message to display
 */
export const showErrorToast = (message: string) => {
  const toast = useToast();

  toast.show({
    placement: 'top',
    render: ({ id }) => {
      return (
        <Toast action="error" variant="solid">
          <ToastTitle>Error</ToastTitle>
          <ToastDescription>{message}</ToastDescription>
        </Toast>
      );
    },
  });
};

/**
 * Utility function to show a success toast notification
 * @param message The success message to display
 */
export const showSuccessToast = (message: string) => {
  const toast = useToast();

  toast.show({
    placement: 'top',
    render: ({ id }) => {
      return (
        <Toast action="success" variant="solid">
          <ToastTitle>Success</ToastTitle>
          <ToastDescription>{message}</ToastDescription>
        </Toast>
      );
    },
  });
};
