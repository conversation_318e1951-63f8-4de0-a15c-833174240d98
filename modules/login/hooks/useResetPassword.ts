import { BASE_API_URL_CLIENT } from '@/constants/base-url';
import { useMutation } from '@tanstack/react-query';

// import { useSession } from "./auth-provider";

// import { showErrorToast, showSuccessToast } from "~/components/toast";

export const resetPassword = async (data: unknown) => {
  const headers: { 'Content-Type': string; authorization?: string } = {
    'Content-Type': 'application/json',
  };

  try {
    const response = await fetch(
      `${BASE_API_URL_CLIENT}auth/password/reset/legacy`,
      {
        headers,
        method: 'POST',
        body: JSON.stringify(data),
      }
    );

    return await response.json();
  } catch (err) {
    throw new Error('Sorry there is a problem resetting the password');
  }
};

export const useResetPassword = (onSuccess?: () => void) => {
  // const { signIn } = useSession();
  return useMutation({
    mutationFn: resetPassword,

    onSuccess: async data => {
      if (data.success) {
        // showSuccessToast(data?.message);
        // await signIn({
        //   email: requestData.email as string,
        //   password: requestData.password as string,
        // });
        // router.
        return onSuccess?.();
      }

      // return showErrorToast(data?.message);
    },

    onError: e => {
      // return showErrorToast(e?.message);
    },
  });
};
