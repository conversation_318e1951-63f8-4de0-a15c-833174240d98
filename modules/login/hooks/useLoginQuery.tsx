import { BASE_API_URL_CLIENT } from '@/constants/base-url';
import { useMutation } from '@tanstack/react-query';

export interface LoginUserResponse {
  success: string;
  message: string;
  token: string;
  university_id: string;
  first_name?: string;
  last_name?: string;
  name?: string;
  id: string;
  email?: string;
  primary_role?: string;
  refresh_token: string;
}

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
  orgId?: string;
}): Promise<LoginUserResponse> => {
  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}auth/login/legacy`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });

    if (response.ok) {
      return await response.json();
    }

    // Try to get more detailed error message from response
    try {
      const errorData = await response.json();
      if (errorData && errorData.message) {
        return Promise.reject(new Error(errorData.message));
      }
    } catch (parseError) {
      // If we can't parse the error response, fall back to status-based messages
    }

    // Status-based error messages
    if (response.status === 401) {
      return Promise.reject(
        new Error('Invalid email or password. Please try again.')
      );
    } else if (response.status === 429) {
      return Promise.reject(
        new Error('Too many login attempts. Please try again later.')
      );
    } else if (response.status >= 500) {
      return Promise.reject(new Error('Server error. Please try again later.'));
    }

    return Promise.reject(
      new Error('Authentication failed. Please check your credentials.')
    );
  } catch (error) {
    throw new Error(
      'Unable to login. Please check your internet connection and try again.'
    );
  }
};

export const useLoginQuery = (onSuccess?: (data?: LoginUserResponse) => void) =>
  useMutation({
    mutationFn: login,
    onSuccess: data => onSuccess?.(data),
  });
