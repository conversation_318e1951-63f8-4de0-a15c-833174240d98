/* eslint-disable no-console */
import { useState, useEffect, useCallback, useRef } from 'react';
import * as LocalAuthentication from 'expo-local-authentication';
import {
  isBiometricEnabled,
  setBiometricEnabled,
  storeBiometricCredentials,
  getBiometricCredentials,
  encryptPassword,
  BiometricCredentials,
} from '../biometric-storage';

export interface BiometricAuthState {
  isSupported: boolean;
  isEnrolled: boolean;
  isEnabled: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  isLoading: boolean;
}

export interface UseBiometricAuthReturn {
  state: BiometricAuthState;
  authenticate: () => Promise<{
    success: boolean;
    credentials?: BiometricCredentials;
  }>;
  enableBiometric: (email: string, password: string) => Promise<boolean>;
  disableBiometric: () => Promise<boolean>;
  checkBiometricStatus: () => Promise<void>;
}

export const useBiometricAuth = (): UseBiometricAuthReturn => {
  const [state, setState] = useState<BiometricAuthState>({
    isSupported: false,
    isEnrolled: false,
    isEnabled: false,
    supportedTypes: [],
    isLoading: true,
  });

  // Track the last time we checked to prevent rapid successive calls
  const lastCheckTimeRef = useRef(0);

  const checkBiometricStatus = useCallback(async () => {
    try {
      // Debounce: prevent calls within 1 second of each other
      const now = Date.now();
      if (now - lastCheckTimeRef.current < 1000) {
        console.log(
          'Skipping biometric status check - too soon after last check'
        );
        return;
      }
      lastCheckTimeRef.current = now;

      setState(prev => ({ ...prev, isLoading: true }));

      // Check if biometric authentication is supported
      const isSupported = await LocalAuthentication.hasHardwareAsync();

      // Check if biometric data is enrolled
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      // Get supported authentication types
      const supportedTypes =
        await LocalAuthentication.supportedAuthenticationTypesAsync();

      // Check if biometric is enabled in our app
      const isEnabled = await isBiometricEnabled();

      // Additional check: if biometric is marked as enabled but no credentials exist,
      // disable it to prevent confusion
      if (isEnabled) {
        const credentials = await getBiometricCredentials();
        if (!credentials) {
          console.log(
            'Biometric enabled but no credentials found, disabling...'
          );
          await setBiometricEnabled(false);
          setState({
            isSupported,
            isEnrolled,
            isEnabled: false,
            supportedTypes,
            isLoading: false,
          });
          return;
        }
      }

      setState({
        isSupported,
        isEnrolled,
        isEnabled,
        supportedTypes,
        isLoading: false,
      });
    } catch (error) {
      console.error('Error checking biometric status:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  const authenticate = async (): Promise<{
    success: boolean;
    credentials?: BiometricCredentials;
  }> => {
    try {
      if (!state.isSupported || !state.isEnrolled || !state.isEnabled) {
        console.log('Biometric authentication not available:', {
          isSupported: state.isSupported,
          isEnrolled: state.isEnrolled,
          isEnabled: state.isEnabled,
        });
        return { success: false };
      }

      // Check if credentials exist before attempting authentication
      const storedCredentials = await getBiometricCredentials();
      if (!storedCredentials) {
        console.log('No biometric credentials found');
        return { success: false };
      }

      // Get the biometric type name for the prompt
      const biometricType = getBiometricTypeName(state.supportedTypes);

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: `Use ${biometricType} to sign in`,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      if (result.success) {
        return { success: true, credentials: storedCredentials };
      }

      return { success: false };
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return { success: false };
    }
  };

  const enableBiometric = async (
    email: string,
    password: string
  ): Promise<boolean> => {
    try {
      if (!state.isSupported || !state.isEnrolled) {
        throw new Error(
          'Biometric authentication is not available on this device'
        );
      }

      // First authenticate with biometrics to confirm user intent
      const biometricType = getBiometricTypeName(state.supportedTypes);

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: `Enable ${biometricType} for quick sign-in`,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Cancel',
        disableDeviceFallback: true,
      });

      if (result.success) {
        // Store credentials securely
        const encryptedPassword = encryptPassword(password);
        await storeBiometricCredentials({ email, password: encryptedPassword });
        await setBiometricEnabled(true);

        // Update state
        setState(prev => ({ ...prev, isEnabled: true }));
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error enabling biometric authentication:', error);
      return false;
    }
  };

  const disableBiometric = async (): Promise<boolean> => {
    try {
      await setBiometricEnabled(false);
      // Note: We don't clear credentials here to allow re-enabling without re-entering password
      // Credentials are only cleared when setBiometricEnabled(false) is called from biometric-storage
      setState(prev => ({ ...prev, isEnabled: false }));
      console.log('Biometric authentication disabled');
      return true;
    } catch (error) {
      console.error('Error disabling biometric authentication:', error);
      return false;
    }
  };

  useEffect(() => {
    checkBiometricStatus();
  }, [checkBiometricStatus]);

  return {
    state,
    authenticate,
    enableBiometric,
    disableBiometric,
    checkBiometricStatus,
  };
};

/**
 * Get a user-friendly name for the biometric authentication type
 */
const getBiometricTypeName = (
  types: LocalAuthentication.AuthenticationType[]
): string => {
  if (
    types.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)
  ) {
    return 'Face ID';
  }
  if (types.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
    return 'Touch ID';
  }
  if (types.includes(LocalAuthentication.AuthenticationType.IRIS)) {
    return 'Iris';
  }
  return 'Biometric';
};
