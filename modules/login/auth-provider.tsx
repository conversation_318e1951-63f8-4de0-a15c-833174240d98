import { createContext, useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { useStorageState } from './hooks/useStorageState';
import { LoginUserResponse, useLoginQuery } from './hooks/useLoginQuery';
import {
  needsToAcceptTerms,
  clearTermsAcceptance,
} from '@/modules/terms/terms-storage';
import { useBiometricAuth, BiometricAuthState } from './hooks/useBiometricAuth';

export type UserSessionType = {
  signIn: ({
    email,
    password,
    orgId,
  }: {
    email: string;
    password: string;
    orgId?: string;
  }) => void;
  signInWithBiometric: () => Promise<boolean>;
  enableBiometric: (email: string, password: string) => Promise<boolean>;
  disableBiometric: () => Promise<boolean>;
  checkBiometricStatus: () => Promise<void>;
  biometricState: BiometricAuthState;
  data?: LoginUserResponse | undefined;
  signOut: () => void;
  isError?: boolean;
  isLoading?: boolean;
  isStarting?: boolean;
  error?: Error | null;
  showTerms?: boolean;
  onTermsAccepted?: () => void;
  setShowTerms?: React.Dispatch<React.SetStateAction<boolean>>;
};

const CurrentUserContext = createContext<UserSessionType>({
  signIn: () => {},
  signInWithBiometric: async () => false,
  enableBiometric: async () => false,
  disableBiometric: async () => false,
  checkBiometricStatus: async () => {},
  biometricState: {
    isSupported: false,
    isEnrolled: false,
    isEnabled: false,
    supportedTypes: [],
    isLoading: false,
  },
  signOut: () => {},
});

export const UPACE_TOKEN = '__upace';

export const useSession = () => useContext(CurrentUserContext);

export const getSession = async () => {
  const record = await SecureStore.getItemAsync(UPACE_TOKEN);

  if (record) {
    const session: UserSessionType['data'] = JSON.parse(record);
    return session;
  }

  return undefined;
};

export const signOut = async () =>
  await SecureStore.deleteItemAsync(UPACE_TOKEN);

export const AuthContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [session, setSession] = useStorageState(UPACE_TOKEN);
  const [showTerms, setShowTerms] = useState(false);

  // Initialize biometric authentication
  const {
    state: biometricState,
    authenticate: authenticateBiometric,
    enableBiometric,
    disableBiometric,
    checkBiometricStatus,
  } = useBiometricAuth();

  useEffect(() => {
    const checkTermsAcceptance = async () => {
      if (session?.value) {
        const needsAcceptance = await needsToAcceptTerms();
        setShowTerms(needsAcceptance);
      }
    };

    checkTermsAcceptance();
  }, [session?.value]);

  const handleTermsAccepted = () => {
    setShowTerms(false);

    router.replace('/(tabs)/(home)');
  };

  const {
    mutateAsync: signIn,
    isError,
    isPending,
    error,
  } = useLoginQuery(rec => {
    if (rec) {
      setSession({
        ...rec,
        name: `${rec?.first_name} ${rec?.last_name}`,
      });

      needsToAcceptTerms().then(needsAcceptance => {
        if (needsAcceptance) {
          router.replace('/terms');
        } else {
          router.replace('/');
        }
      });
    }
  });

  // Biometric sign-in function
  const signInWithBiometric = async (): Promise<boolean> => {
    try {
      console.log('Starting biometric sign-in...');
      const result = await authenticateBiometric();

      if (result.success && result.credentials) {
        console.log(
          'Biometric authentication successful, signing in with stored credentials'
        );
        // Use stored credentials to sign in
        await signIn({
          email: result.credentials.email,
          password: result.credentials.password,
        });
        return true;
      } else {
        console.log('Biometric authentication failed or no credentials:', {
          success: result.success,
          hasCredentials: !!result.credentials,
        });
        return false;
      }
    } catch (error) {
      console.error('Biometric sign-in error:', error);
      return false;
    }
  };

  return (
    <>
      <CurrentUserContext.Provider
        value={{
          signIn,
          signInWithBiometric,
          enableBiometric,
          disableBiometric,
          checkBiometricStatus,
          biometricState,
          data: session?.value as LoginUserResponse | undefined,
          isError,
          error,
          signOut: async () => {
            setSession(undefined);
            await clearTermsAcceptance();
            // Don't clear biometric credentials on logout to allow biometric login on next sign-in
            // await clearBiometricCredentials();
            router.replace('/(auth)/sign-in');
          },
          isLoading: isPending,
          isStarting: session.loading,
          showTerms,
          onTermsAccepted: handleTermsAccepted,
          setShowTerms,
        }}
      >
        {children}
      </CurrentUserContext.Provider>
    </>
  );
};
