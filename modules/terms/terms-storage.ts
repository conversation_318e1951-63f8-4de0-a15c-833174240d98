import * as SecureStore from 'expo-secure-store';

// Key for storing terms acceptance in SecureStore
export const TERMS_ACCEPTANCE_KEY = '__upace_terms_acceptance';

// Current version of terms and conditions
// Increment this when terms are updated to force users to accept again
export const CURRENT_TERMS_VERSION = '1.0';

export interface TermsAcceptanceData {
  accepted: boolean;
  version: string;
  acceptedAt: string; // ISO date string
}

/**
 * Get the current terms acceptance status
 * @returns Promise with terms acceptance data or undefined if not accepted
 */
export const getTermsAcceptance = async (): Promise<
  TermsAcceptanceData | undefined
> => {
  try {
    const storedData = await SecureStore.getItemAsync(TERMS_ACCEPTANCE_KEY);

    if (storedData) {
      return JSON.parse(storedData) as TermsAcceptanceData;
    }

    return undefined;
  } catch (error) {
    console.error('Error retrieving terms acceptance:', error);
    return undefined;
  }
};

/**
 * Save terms acceptance status
 * @param version Version of terms that was accepted
 * @returns Promise that resolves when data is saved
 */
export const saveTermsAcceptance = async (
  version: string = CURRENT_TERMS_VERSION
): Promise<void> => {
  try {
    const acceptanceData: TermsAcceptanceData = {
      accepted: true,
      version,
      acceptedAt: new Date().toISOString(),
    };

    await SecureStore.setItemAsync(
      TERMS_ACCEPTANCE_KEY,
      JSON.stringify(acceptanceData)
    );
  } catch (error) {
    console.error('Error saving terms acceptance:', error);
  }
};

/**
 * Check if the user needs to accept terms
 * @returns Promise that resolves to true if terms need to be accepted
 */
export const needsToAcceptTerms = async (): Promise<boolean> => {
  const termsAcceptance = await getTermsAcceptance();

  // Terms need to be accepted if:
  // 1. They've never been accepted before
  // 2. The accepted version is different from the current version
  if (!termsAcceptance || termsAcceptance.version !== CURRENT_TERMS_VERSION) {
    return true;
  }

  return false;
};

/**
 * Clear terms acceptance data (for testing or logout)
 */
export const clearTermsAcceptance = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(TERMS_ACCEPTANCE_KEY);
  } catch (error) {
    console.error('Error clearing terms acceptance:', error);
  }
};
