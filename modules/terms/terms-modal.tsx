import React, { useState } from 'react';
import { SafeAreaView, View } from 'react-native';

import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
} from '@/components/ui/checkbox';
import { Check } from 'lucide-react-native';
import { HStack } from '@/components/ui/hstack';
import TermsContent from './terms-content';
import { saveTermsAcceptance, CURRENT_TERMS_VERSION } from './terms-storage';

interface TermsModalProps {
  isOpen: boolean;
  onAccept: () => void;
}

/**
 * Modal component that displays terms and conditions with accept checkbox
 */
export const TermsModal: React.FC<TermsModalProps> = ({ isOpen, onAccept }) => {
  const [accepted, setAccepted] = useState(false);

  const handleAccept = async () => {
    if (accepted) {
      // Save acceptance to storage
      await saveTermsAcceptance(CURRENT_TERMS_VERSION);
      // Call the onAccept callback
      onAccept();
    }
  };

  // If not open, don't render anything
  if (!isOpen) {
    return null;
  }

  return (
    <SafeAreaView className="flex-1 justify-center items-center p-4">
      <TermsContent />

      <View className="w-full p-4">
        <View className="w-full">
          <HStack className="mb-4 p-1">
            <Checkbox
              value="terms-accepted"
              isChecked={accepted}
              onChange={setAccepted}
              aria-label="Accept terms and conditions"
              testID="terms-checkbox"
            >
              <CheckboxIndicator>
                <CheckboxIcon as={Check} />
              </CheckboxIndicator>
              <CheckboxLabel>
                I agree to the{' '}
                <Text className="text-[#00697B] font-dm-sans-medium text-sm">
                  terms of service & privacy policy
                </Text>
              </CheckboxLabel>
            </Checkbox>
          </HStack>

          <Button
            className="bg-[#00697B] rounded-full"
            onPress={handleAccept}
            disabled={!accepted}
            testID="continue-button"
          >
            <ButtonText>Continue</ButtonText>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default TermsModal;
