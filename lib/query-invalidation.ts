import { QueryClient } from '@tanstack/react-query';

export const QUERY_KEYS = {
  RESERVATIONS: ['reservations'],
  CLASSES: ['classes'],
  ACTIVITIES: ['activities'],
  EVENTS: ['events'],
  APPOINTMENTS: ['appointments'],
  CHALLENGE_EVENT_LOGS: ['challenge-event-logs'],
} as const;


export const INVALIDATION_GROUPS = {
  // When a reservation is cancelled, these queries need to be invalidated
  RESERVATION_CANCELLED: [
    QUERY_KEYS.RESERVATIONS,
    QUERY_KEYS.CLASSES,
    QUERY_KEYS.ACTIVITIES,
  ],
  
  // When a class is reserved/cancelled
  CLASS_RESERVATION_CHANGED: [
    QUERY_KEYS.CLASSES,
    QUERY_KEYS.RESERVATIONS,
  ],
  
  // When an activity is reserved/cancelled
  ACTIVITY_RESERVATION_CHANGED: [
    QUERY_KEYS.ACTIVITIES,
    QUERY_KEYS.RESERVATIONS,
  ],
  
  // When favorites are toggled
  FAVORITE_TOGGLED: [
    QUERY_KEYS.CLASSES,
    QUERY_KEYS.EVENTS,
    QUERY_KEYS.ACTIVITIES,
  ],
  
  // When an appointment is booked
  APPOINTMENT_BOOKED: [
    QUERY_KEYS.APPOINTMENTS,
  ],
  
  // When activity logs are created
  ACTIVITY_LOG_CREATED: [
    QUERY_KEYS.CHALLENGE_EVENT_LOGS,
  ],
  
  // When any reservation is made (class or activity)
  RESERVATION_MADE: [
    QUERY_KEYS.RESERVATIONS,
    QUERY_KEYS.CLASSES,
    QUERY_KEYS.ACTIVITIES,
  ],
} as const;


export const invalidateQueries = async (
  queryClient: QueryClient,
  queryKeys: readonly (readonly unknown[])[],
  options?: { refetchType?: 'active' | 'inactive' | 'all' | 'none' }
) => {
  await Promise.all(
    queryKeys.map(queryKey => 
      queryClient.invalidateQueries({ 
        queryKey, 
        refetchType: options?.refetchType || 'active'
      })
    )
  );
};


export const invalidateByGroup = async (
  queryClient: QueryClient,
  group: keyof typeof INVALIDATION_GROUPS,
  options?: { refetchType?: 'active' | 'inactive' | 'all' | 'none' }
) => {
  const queryKeys = INVALIDATION_GROUPS[group];
  await invalidateQueries(queryClient, queryKeys, options);
};

