import { renderHook, act, waitFor } from '@testing-library/react-native';
import { useAIChat } from '@/hooks/useAIChat';
import { useAIStore } from '@/stores/ai-store';

// Mock the AI store
jest.mock('@/stores/ai-store');
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

const mockUseAIStore = useAIStore as jest.MockedFunction<typeof useAIStore>;

describe('useAIChat Hook', () => {
  const mockStore = {
    currentConversation: null,
    isTyping: false,
    conversations: [],
    popularQuestions: [
      { id: '1', question: 'What classes do I have today?' },
      { id: '2', question: 'How many challenges have I completed?' },
    ],
    planOptions: [
      { id: '1', title: 'Build a workout plan', icon: 'dumbbell' },
      { id: '2', title: 'Get nutrition tips', icon: 'user' },
    ],
    setCurrentConversation: jest.fn(),
    addMessage: jest.fn(),
    createNewConversation: jest.fn(),
    setIsTyping: jest.fn(),
    setLoading: jest.fn(),
    setError: jest.fn(),
    clearError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAIStore.mockReturnValue(mockStore as any);
  });

  it('should initialize with store values', () => {
    const { result } = renderHook(() => useAIChat());

    expect(result.current.currentConversation).toBeNull();
    expect(result.current.isTyping).toBe(false);
    expect(result.current.conversations).toEqual([]);
    expect(result.current.popularQuestions).toHaveLength(2);
    expect(result.current.planOptions).toHaveLength(2);
  });

  it('should send message and create conversation if none exists', async () => {
    const mockConversation = {
      id: 'conv-1',
      title: 'Hello...',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockStore.createNewConversation.mockReturnValue(mockConversation);

    const { result } = renderHook(() => useAIChat());

    act(() => {
      result.current.sendMessage('Hello');
    });

    expect(mockStore.clearError).toHaveBeenCalled();
    expect(mockStore.createNewConversation).toHaveBeenCalledWith('Hello...');
    expect(mockStore.addMessage).toHaveBeenCalledWith({
      content: 'Hello',
      role: 'user',
    });
    expect(mockStore.setIsTyping).toHaveBeenCalledWith(true);

    // Wait for the AI response timeout
    await waitFor(() => {
      expect(mockStore.setIsTyping).toHaveBeenCalledWith(false);
    }, { timeout: 2000 });
  });

  it('should send message to existing conversation', async () => {
    const mockConversation = {
      id: 'conv-1',
      title: 'Existing Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockStore.currentConversation = mockConversation;

    const { result } = renderHook(() => useAIChat());

    act(() => {
      result.current.sendMessage('How are you?');
    });

    expect(mockStore.createNewConversation).not.toHaveBeenCalled();
    expect(mockStore.addMessage).toHaveBeenCalledWith({
      content: 'How are you?',
      role: 'user',
    });
  });

  it('should handle popular question selection', async () => {
    const { result } = renderHook(() => useAIChat());
    const question = { id: '1', question: 'What classes do I have today?' };

    act(() => {
      result.current.handlePopularQuestion(question);
    });

    expect(mockStore.addMessage).toHaveBeenCalledWith({
      content: 'What classes do I have today?',
      role: 'user',
    });
  });

  it('should handle plan option selection', async () => {
    const { result } = renderHook(() => useAIChat());
    const option = { id: '1', title: 'Build a workout plan', icon: 'dumbbell' };

    act(() => {
      result.current.handlePlanOption(option);
    });

    expect(mockStore.addMessage).toHaveBeenCalledWith({
      content: "I'd like to build a workout plan",
      role: 'user',
    });
  });

  it('should start new conversation', () => {
    const { result } = renderHook(() => useAIChat());

    act(() => {
      result.current.startNewConversation();
    });

    expect(mockStore.setCurrentConversation).toHaveBeenCalledWith(null);
  });

  it('should select conversation', () => {
    const { result } = renderHook(() => useAIChat());
    const conversation = {
      id: 'conv-1',
      title: 'Test Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    act(() => {
      result.current.selectConversation(conversation);
    });

    expect(mockStore.setCurrentConversation).toHaveBeenCalledWith(conversation);
  });

  it('should generate appropriate AI responses', async () => {
    const { result } = renderHook(() => useAIChat());

    // Test workout-related response
    act(() => {
      result.current.sendMessage('I want to build a workout plan');
    });

    await waitFor(() => {
      const addMessageCalls = mockStore.addMessage.mock.calls;
      const aiResponseCall = addMessageCalls.find(call => call[0].role === 'assistant');
      expect(aiResponseCall[0].content).toContain('Got it');
    }, { timeout: 2000 });
  });
});
