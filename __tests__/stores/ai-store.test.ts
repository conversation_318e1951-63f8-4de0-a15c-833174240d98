import { renderHook, act } from '@testing-library/react-native';
import { useAIStore } from '@/stores/ai-store';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('AI Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useAIStore());
    act(() => {
      result.current.reset();
    });
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useAIStore());
    
    expect(result.current.currentConversation).toBeNull();
    expect(result.current.isTyping).toBe(false);
    expect(result.current.conversations).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.popularQuestions).toHaveLength(3);
    expect(result.current.planOptions).toHaveLength(3);
  });

  it('should create a new conversation', () => {
    const { result } = renderHook(() => useAIStore());
    
    act(() => {
      const conversation = result.current.createNewConversation('Test Chat');
      expect(conversation.title).toBe('Test Chat');
      expect(conversation.messages).toEqual([]);
    });

    expect(result.current.conversations).toHaveLength(1);
    expect(result.current.currentConversation).toBeTruthy();
    expect(result.current.currentConversation?.title).toBe('Test Chat');
  });

  it('should add messages to current conversation', () => {
    const { result } = renderHook(() => useAIStore());
    
    act(() => {
      result.current.createNewConversation('Test Chat');
    });

    act(() => {
      result.current.addMessage({
        content: 'Hello AI',
        role: 'user'
      });
    });

    expect(result.current.currentConversation?.messages).toHaveLength(1);
    expect(result.current.currentConversation?.messages[0].content).toBe('Hello AI');
    expect(result.current.currentConversation?.messages[0].role).toBe('user');
  });

  it('should set typing state', () => {
    const { result } = renderHook(() => useAIStore());
    
    act(() => {
      result.current.setIsTyping(true);
    });

    expect(result.current.isTyping).toBe(true);

    act(() => {
      result.current.setIsTyping(false);
    });

    expect(result.current.isTyping).toBe(false);
  });

  it('should delete conversation', () => {
    const { result } = renderHook(() => useAIStore());
    
    let conversationId: string;
    
    act(() => {
      const conversation = result.current.createNewConversation('Test Chat');
      conversationId = conversation.id;
    });

    expect(result.current.conversations).toHaveLength(1);

    act(() => {
      result.current.deleteConversation(conversationId);
    });

    expect(result.current.conversations).toHaveLength(0);
    expect(result.current.currentConversation).toBeNull();
  });

  it('should update conversation', () => {
    const { result } = renderHook(() => useAIStore());
    
    let conversationId: string;
    
    act(() => {
      const conversation = result.current.createNewConversation('Test Chat');
      conversationId = conversation.id;
    });

    act(() => {
      result.current.updateConversation(conversationId, {
        title: 'Updated Chat'
      });
    });

    expect(result.current.currentConversation?.title).toBe('Updated Chat');
    expect(result.current.conversations[0].title).toBe('Updated Chat');
  });

  it('should handle error state', () => {
    const { result } = renderHook(() => useAIStore());
    
    act(() => {
      result.current.setError('Test error');
    });

    expect(result.current.error).toBe('Test error');

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
  });

  it('should handle loading state', () => {
    const { result } = renderHook(() => useAIStore());
    
    act(() => {
      result.current.setLoading(true);
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.setLoading(false);
    });

    expect(result.current.isLoading).toBe(false);
  });
});
