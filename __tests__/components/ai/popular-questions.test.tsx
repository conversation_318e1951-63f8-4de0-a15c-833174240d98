import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PopularQuestions } from '@/components/ai/popular-questions';
import { PopularQuestion } from '@/stores/ai-store';

const mockQuestions: PopularQuestion[] = [
  {
    id: '1',
    question: 'What classes do I have today?',
    category: 'schedule'
  },
  {
    id: '2',
    question: 'How many challenges have I completed?',
    category: 'progress'
  },
  {
    id: '3',
    question: 'Can I book a spot for yoga tomorrow?',
    category: 'booking'
  }
];

describe('PopularQuestions Component', () => {
  const mockOnQuestionPress = jest.fn();
  const mockOnShowMorePress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render popular questions title', () => {
    const { getByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
      />
    );

    expect(getByText('Popular questions')).toBeTruthy();
  });

  it('should render all questions', () => {
    const { getByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
      />
    );

    mockQuestions.forEach(question => {
      expect(getByText(question.question)).toBeTruthy();
    });
  });

  it('should call onQuestionPress when question is pressed', () => {
    const { getByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
      />
    );

    const firstQuestion = getByText(mockQuestions[0].question);
    fireEvent.press(firstQuestion);

    expect(mockOnQuestionPress).toHaveBeenCalledWith(mockQuestions[0]);
  });

  it('should render show more button when showMore is true', () => {
    const { getByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
        showMore={true}
        onShowMorePress={mockOnShowMorePress}
      />
    );

    expect(getByText('Show more')).toBeTruthy();
  });

  it('should call onShowMorePress when show more is pressed', () => {
    const { getByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
        showMore={true}
        onShowMorePress={mockOnShowMorePress}
      />
    );

    const showMoreButton = getByText('Show more');
    fireEvent.press(showMoreButton);

    expect(mockOnShowMorePress).toHaveBeenCalled();
  });

  it('should not render show more button when showMore is false', () => {
    const { queryByText } = render(
      <PopularQuestions
        questions={mockQuestions}
        onQuestionPress={mockOnQuestionPress}
        showMore={false}
      />
    );

    expect(queryByText('Show more')).toBeNull();
  });

  it('should handle empty questions array', () => {
    const { getByText, queryByText } = render(
      <PopularQuestions
        questions={[]}
        onQuestionPress={mockOnQuestionPress}
      />
    );

    expect(getByText('Popular questions')).toBeTruthy();
    mockQuestions.forEach(question => {
      expect(queryByText(question.question)).toBeNull();
    });
  });

  it('should truncate long questions to 2 lines', () => {
    const longQuestion: PopularQuestion = {
      id: '4',
      question: 'This is a very long question that should be truncated to two lines maximum when displayed in the component',
      category: 'test'
    };

    const { getByText } = render(
      <PopularQuestions
        questions={[longQuestion]}
        onQuestionPress={mockOnQuestionPress}
      />
    );

    const questionText = getByText(longQuestion.question);
    expect(questionText.props.numberOfLines).toBe(2);
  });
});
