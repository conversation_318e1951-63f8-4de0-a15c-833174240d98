import React from 'react';
import { render } from '@testing-library/react-native';
import { MessageBubble } from '@/components/ai/message-bubble';
import { Message } from '@/stores/ai-store';

describe('MessageBubble Component', () => {
  const mockUserMessage: Message = {
    id: '1',
    content: 'Hello, how are you?',
    role: 'user',
    timestamp: new Date('2024-01-15T10:00:00Z')
  };

  const mockAssistantMessage: Message = {
    id: '2',
    content: 'I am doing well, thank you for asking!',
    role: 'assistant',
    timestamp: new Date('2024-01-15T10:00:30Z')
  };

  const mockSystemMessage: Message = {
    id: '3',
    content: 'Conversation started',
    role: 'system',
    timestamp: new Date('2024-01-15T09:59:00Z')
  };

  it('should render user message correctly', () => {
    const { getByText } = render(
      <MessageBubble message={mockUserMessage} />
    );

    expect(getByText('Hello, how are you?')).toBeTruthy();
  });

  it('should render assistant message correctly', () => {
    const { getByText } = render(
      <MessageBubble message={mockAssistantMessage} />
    );

    expect(getByText('I am doing well, thank you for asking!')).toBeTruthy();
  });

  it('should render system message correctly', () => {
    const { getByText } = render(
      <MessageBubble message={mockSystemMessage} />
    );

    expect(getByText('Conversation started')).toBeTruthy();
  });

  it('should show avatar for assistant message when showAvatar is true', () => {
    const { getByTestId } = render(
      <MessageBubble message={mockAssistantMessage} showAvatar={true} />
    );

    // The AI avatar should be present for assistant messages
    // This would need to be tested with a testID on the AIAvatar component
  });

  it('should show user avatar when showAvatar is true for user message', () => {
    const { getByText } = render(
      <MessageBubble message={mockUserMessage} showAvatar={true} />
    );

    // User avatar shows "U" as fallback text
    expect(getByText('U')).toBeTruthy();
  });

  it('should not show avatar when showAvatar is false', () => {
    const { queryByText } = render(
      <MessageBubble message={mockUserMessage} showAvatar={false} />
    );

    // User avatar should not be present
    expect(queryByText('U')).toBeNull();
  });

  it('should apply correct styling for user messages', () => {
    const { getByText } = render(
      <MessageBubble message={mockUserMessage} />
    );

    const messageText = getByText('Hello, how are you?');
    // User messages should have white text (would need to check computed styles)
    expect(messageText).toBeTruthy();
  });

  it('should apply correct styling for assistant messages', () => {
    const { getByText } = render(
      <MessageBubble message={mockAssistantMessage} />
    );

    const messageText = getByText('I am doing well, thank you for asking!');
    // Assistant messages should have dark text (would need to check computed styles)
    expect(messageText).toBeTruthy();
  });

  it('should center system messages', () => {
    const { getByText } = render(
      <MessageBubble message={mockSystemMessage} />
    );

    const messageText = getByText('Conversation started');
    // System messages should be centered (would need to check computed styles)
    expect(messageText).toBeTruthy();
  });

  it('should handle empty message content', () => {
    const emptyMessage: Message = {
      ...mockUserMessage,
      content: ''
    };

    const { container } = render(
      <MessageBubble message={emptyMessage} />
    );

    // Should still render the component structure
    expect(container).toBeTruthy();
  });

  it('should handle long message content', () => {
    const longMessage: Message = {
      ...mockAssistantMessage,
      content: 'This is a very long message that should wrap properly within the message bubble and not overflow the container boundaries. It should maintain proper spacing and readability.'
    };

    const { getByText } = render(
      <MessageBubble message={longMessage} />
    );

    expect(getByText(longMessage.content)).toBeTruthy();
  });
});
