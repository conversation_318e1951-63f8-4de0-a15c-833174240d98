import { PopularQuestion, PlanOption, Conversation, Message } from '@/stores/ai-store';

export const POPULAR_QUESTIONS: PopularQuestion[] = [
  {
    id: '1',
    question: 'What classes do I have today?',
    category: 'schedule'
  },
  {
    id: '2', 
    question: 'How many challenges have I completed?',
    category: 'progress'
  },
  {
    id: '3',
    question: 'Can I book a spot for yoga tomorrow?',
    category: 'booking'
  }
];

export const PLAN_OPTIONS: PlanOption[] = [
  {
    id: '1',
    title: 'Build a workout plan',
    icon: 'dumbbell',
    route: '/ai/workout-plan'
  },
  {
    id: '2',
    title: 'Get nutrition tips', 
    icon: 'user',
    route: '/ai/nutrition'
  },
  {
    id: '3',
    title: 'Help me plan times to take classes',
    icon: 'calendar',
    route: '/ai/class-planning'
  }
];

export const SAMPLE_CONVERSATIONS: Conversation[] = [
  {
    id: 'conv-1',
    title: 'Find beginner-friendly classes',
    messages: [
      {
        id: 'msg-1',
        content: 'I\'m looking for beginner-friendly classes. Can you help?',
        role: 'user',
        timestamp: new Date('2024-01-15T10:00:00Z')
      },
      {
        id: 'msg-2',
        content: 'Here are three low-impact classes you can try this week...',
        role: 'assistant',
        timestamp: new Date('2024-01-15T10:00:30Z')
      }
    ],
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T10:00:30Z')
  },
  {
    id: 'conv-2',
    title: 'Set my weekly wellness goal',
    messages: [
      {
        id: 'msg-3',
        content: 'Help me set a weekly wellness goal',
        role: 'user',
        timestamp: new Date('2024-01-14T14:30:00Z')
      },
      {
        id: 'msg-4',
        content: 'Here\'s a simple, achievable target to stay on track this...',
        role: 'assistant',
        timestamp: new Date('2024-01-14T14:30:30Z')
      }
    ],
    createdAt: new Date('2024-01-14T14:30:00Z'),
    updatedAt: new Date('2024-01-14T14:30:30Z')
  }
];

export const FITNESS_GOALS = [
  'Get stronger',
  'Lose weight', 
  'Improve endurance',
  'Just stay active'
];

export const WORKOUT_FREQUENCY_OPTIONS = [
  { value: '1', label: '1 day per week' },
  { value: '2', label: '2 days per week' },
  { value: '3', label: '3 days per week' },
  { value: '4', label: '4 days per week' },
  { value: '5', label: '5 days per week' },
  { value: '6', label: '6 days per week' },
  { value: '7', label: '7 days per week' }
];

// AI Response templates for different scenarios
export const AI_RESPONSES = {
  GREETING: "Hey! 👋\n\nI can help you build a workout plan that fits your goals and schedule.\n\nLet's start with this",
  WORKOUT_PLAN_START: "Got it. 💪\n\nAnd how many days a week do you want to work out?",
  NUTRITION_TIPS: "I'd be happy to help with nutrition tips! What specific area would you like to focus on?",
  CLASS_PLANNING: "Let me help you plan the perfect class schedule. What's your preferred workout time?",
  DEFAULT: "I'm here to help! You can ask me about classes, book appointments, get workout plans, or nutrition advice."
};

export const TYPING_DELAY = 1500; // milliseconds
export const MESSAGE_ANIMATION_DELAY = 300; // milliseconds
