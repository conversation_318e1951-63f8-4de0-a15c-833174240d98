import { createContext, useState } from 'react';

export type ThemeContextValue = {
  colorMode: 'light' | 'dark';
  setColorMode: (mode: 'light' | 'dark') => void;
};

export const ThemeContext = createContext<ThemeContextValue>({
  colorMode: 'light',
  setColorMode: () => undefined,
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [colorMode, setColorMode] = useState<'light' | 'dark'>('light');

  return (
    <ThemeContext.Provider value={{ colorMode, setColorMode }}>
      {children}
    </ThemeContext.Provider>
  );
};
