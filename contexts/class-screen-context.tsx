import React, { createContext, useRef, useState } from 'react';
import { View, ScrollView } from 'react-native';

type ChildRef = { ref: React.RefObject<View>; isVisible: boolean };

export type ClassTabContextValue = {
  isChildVisible: boolean;
  setIsChildVisible: (v: boolean) => void;
  selectedTabIndex: number;
  setSelectedTabIndex: (v: number) => void;
  scrollViewRef: React.RefObject<ScrollView> & {
    current: (ScrollView & { _scrollY?: number }) | null;
  };
  hasDaysTabAnimated: React.MutableRefObject<boolean>;
  hasHourlyTabChild1Animated: React.MutableRefObject<boolean>;
  hasProgressBarAnimated: React.MutableRefObject<number>;
  childRefs: ChildRef[];
  setChildRefs: React.Dispatch<React.SetStateAction<ChildRef[]>>;
};

export const ClassTabContext = createContext<ClassTabContextValue>({
  isChildVisible: false,
  setIsChildVisible: () => undefined,
  selectedTabIndex: 0,
  setSelectedTabIndex: () => undefined,
  // @ts-expect-error initialize ref-like shape
  scrollViewRef: { current: null },
  hasDaysTabAnimated: { current: false },
  hasHourlyTabChild1Animated: { current: false },
  hasProgressBarAnimated: { current: 0 },
  childRefs: [],
  setChildRefs: () => undefined,
});

export const ClassTabProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isChildVisible, setIsChildVisible] = useState(false);
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const scrollViewRef = useRef<(ScrollView & { _scrollY?: number }) | null>(
    null
  );
  const hasDaysTabAnimated = useRef(false);
  const hasHourlyTabChild1Animated = useRef(false);
  const hasProgressBarAnimated = useRef(0);
  const [childRefs, setChildRefs] = useState<ChildRef[]>([
    { ref: useRef<View>(null), isVisible: false },
    { ref: useRef<View>(null), isVisible: false },
  ]);

  return (
    <ClassTabContext.Provider
      value={{
        isChildVisible,
        setIsChildVisible,
        selectedTabIndex,
        setSelectedTabIndex,
        scrollViewRef,
        hasDaysTabAnimated,
        hasHourlyTabChild1Animated,
        hasProgressBarAnimated,
        childRefs,
        setChildRefs,
      }}
    >
      {children}
    </ClassTabContext.Provider>
  );
};
