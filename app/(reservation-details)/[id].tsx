import React, { useMemo } from 'react';
import {  useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';
import { ReservationDetails } from '@/components/screens/reservations/details/reservation-details';
import { useReservationsQuery } from '@/data/screens/reservations/queries/useReservationsQuery';

const ReservationDetailsScreen = () => {
  const { id } = useLocalSearchParams<{ id: string }>();

  const { data: upcomingData = [], isLoading: isLoadingUpcoming } = useReservationsQuery({ status: 'upcoming' });
  const { data: pastData = [], isLoading: isLoadingPast } = useReservationsQuery({ status: 'past' });
  
  const allReservations = useMemo(() => [...upcomingData, ...pastData], [upcomingData, pastData]);
  const found = allReservations.find(item => item.id === Number(id));
  const isLoading = isLoadingUpcoming || isLoadingPast;

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Spinner size="large" />
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2 mt-4">
            Loading reservation...
          </Text>
        </VStack>
      </SafeAreaView>
    );
  }

  if (!found) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Reservation Not Found
          </Text>
        </VStack>
      </SafeAreaView>
 
    );
  }

  return <ReservationDetails item={found} />;
};

export default ReservationDetailsScreen;

