import React, { useMemo } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { EventDetails } from '@/components/screens/events/event-details/event-details';
import { EventDetailsSkeleton } from '@/components/screens/events/event-details/event-details-skeleton';
import { useEventsQuery } from '@/data/screens/events/queries/useEventsQuery';

const EventDetailsScreen = () => {
  const { id, month_year, date} = useLocalSearchParams<{
    id: string;
    month_year: string;
    date: string;
  }>();

  const { data: eventData, isLoading, error } = useEventsQuery({ month_year });

  const foundData = useMemo(
    () => eventData?.find(item => item.id === Number(id) && item.date === date),
    [eventData, id]
  );

  if (isLoading) {
    return <EventDetailsSkeleton />;
  }

  if (error || !foundData) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Event Not Found
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 text-center mb-6">
            The event you&#39;re looking for doesn&#39;t exist or has been
            removed
          </Text>
          <Pressable
            onPress={() => router.back()}
            className="bg-primary-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-dm-sans-medium">Go Back</Text>
          </Pressable>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <EventDetails
      eventItem={foundData}
      isLoading={isLoading}
    />
  );
};

export default EventDetailsScreen;
