import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { ActivityHeader } from '@/components/screens/activities/activity-header';
import { SearchWithFilter } from '@/components/shared/search-with-filter';
import { HorizontalDatePicker } from '@/components/shared/horizontal-date-picker';
import { useActivitiesWithFilter } from '@/hooks/useActivitiesWithFilter';
import { ActivityList } from '@/components/screens/activities/activity-list';
import FilterComponent from '@/components/shared/filter-component';

export const Activity = () => {
  const {
    activities,
    isLoading,
    isRefetching,
    selectedDate,
    searchTerm,
    filterProps,
    activeFilterCount,
    handleDateChange,
    setSearchTerm,
    clearSearch,
    refetch,
    clearAllFilters,
  } = useActivitiesWithFilter();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ActivityHeader />

        <VStack space="md" className="pb-4">
          <VStack>
            <HorizontalDatePicker
              selectedDate={selectedDate}
              onDateSelect={handleDateChange}
              config={{
                mode: 'days',
                showCalendarButton: true,
              }}
            />
            <SearchWithFilter
              onSearch={setSearchTerm}
              searchTerm={searchTerm}
              placeholder="Search activities"
              filter={
                <FilterComponent
                  {...filterProps}
                  title="Filter"
                  activeFilterCount={activeFilterCount}
                  onReset={clearAllFilters}
                  size={65}
                />
              }
            />
          </VStack>

          <VStack
            space="sm"
            className={activities?.length ? 'px-4 bg-gray-100' : ''}
          >
            <ActivityList
              activities={activities}
              isLoading={isLoading}
              isRefreshing={isRefetching}
              searchTerm={searchTerm}
              selectedDate={selectedDate}
              onRefresh={refetch}
              onClearSearch={clearSearch}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Activity;
