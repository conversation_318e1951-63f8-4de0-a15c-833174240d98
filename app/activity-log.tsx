import React, { useMemo } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import ActivityLogHeader from '@/components/screens/activity-log/activity-log-header';
import { FlatList } from 'react-native';
import { ActivityLogSection, ActivityLogSectionData } from '@/components/screens/activity-log/activity-log-section';
import { Fab, FabIcon } from '@/components/ui/fab';
import { Plus } from 'lucide-react-native';
import { useActivityLogsQuery } from '@/data/screens/activity-log/queries/useActivityLogsQuery';
import { format, parse } from 'date-fns';
import { EmptyState } from '@/components/screens/classes/empty-state';
import { CardSkeletonLoader } from '@/components/shared/card-skeleton';

const parseEntryDate = (entry: string) =>
  parse(entry, 'yyyy-MM-dd HH:mm:ss', new Date());

const ActivityLogScreen = () => {
  const { data = [], isLoading } = useActivityLogsQuery();

  const sections: ActivityLogSectionData[] = useMemo(() => {
    if (!data?.length) return [];

    const groups = data.reduce<Record<string, ActivityLogSectionData>>(
      (acc, log) => {
        const date = parseEntryDate(log.entry_date);
        const dateKey = format(date, 'yyyy-MM-dd');
        const dateLabel = format(date, 'EEE, MMM d, yyyy');

        const section = acc[dateKey] ?? {
          id: dateKey,
          dateLabel,
          countLabel: '0 activities',
          items: [],
        };

        section.items.push({
          id: String(log.id),
          title: log.activity_type_name ?? log.challenge_title ?? 'Activity',
          note: log.notes ?? undefined,
          timeLabel: format(parseEntryDate(log.entry_date), 'h:mma'),
          metaBadge: log.quantity != null ? String(log.quantity) : undefined,
        });

        section.countLabel = `${section.items.length} ${
          section.items.length === 1 ? 'activity' : 'activities'
        }`;
        acc[dateKey] = section;
        return acc;
      },
      {}
    );

    return Object.values(groups).sort((a, b) => {
      // sort descending by date id (yyyy-MM-dd)
      return a.id < b.id ? 1 : a.id > b.id ? -1 : 0;
    });
  }, [data]);

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        <ActivityLogHeader />
        {isLoading ? (
          <CardSkeletonLoader />
        ) : sections.length === 0 ? (
          <EmptyState
            title="No activity yet"
            subtitle="Your activity logs will appear here when you add or track activities."
          />
        ) : (
          <FlatList
            data={sections}
            keyExtractor={(s) => s.id}
            renderItem={({ item }) => <ActivityLogSection section={item} />}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 120, paddingTop: 8 }}
          />
        )}
        <Fab placement="bottom right" className="bg-[#00AECC] right-5 bottom-6">
          <FabIcon as={Plus} size="xl" />
        </Fab>
      </VStack>
    </SafeAreaView>
  );
};

export default ActivityLogScreen;

