import React from 'react';
import { ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { AIChatHeader } from '@/components/ai/ai-chat-header';
import { PopularQuestions } from '@/components/ai/popular-questions';
// import { PlanWithAI } from '@/components/ai/plan-with-ai';
import { RecentChats } from '@/components/ai/recent-chats';
import { ChatInput } from '@/components/ai/chat-input';
import { useAIChat } from '@/hooks/useAIChat';
import { useRouter } from 'expo-router';

const AiSheet = () => {
  const router = useRouter();
  const {
    conversations,
    popularQuestions,
    // planOptions,
    sendMessage,
    handlePopularQuestion,
    handlePlanOption,
    selectConversation,
  } = useAIChat();

  const handleSendMessage = (message: string) => {
    sendMessage(message);
    // Navigate to conversation view
    router.push('/ai/chat');
  };

  const handleQuestionPress = (question: any) => {
    handlePopularQuestion(question);
    router.push('/ai/chat');
  };

  const handlePlanPress = (option: any) => {
    if (option.id === '1') {
      // Navigate to workout plan builder
      router.push('/ai/workout-plan');
    } else {
      handlePlanOption(option);
      router.push('/ai/chat');
    }
  };

  const handleChatPress = (conversation: any) => {
    selectConversation(conversation);
    router.push('/ai/chat');
  };

  const handleViewAllChats = () => {
    router.push('/ai/conversations');
  };

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <AIChatHeader title="Ask Dela AI" />

      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        <VStack space="lg">
          <PopularQuestions
            questions={popularQuestions}
            onQuestionPress={handleQuestionPress}
          />

          {/* <PlanWithAI planOptions={planOptions} onPlanPress={handlePlanPress} /> */}

          <RecentChats
            conversations={conversations}
            onChatPress={handleChatPress}
            onViewAllPress={handleViewAllChats}
          />
        </VStack>
      </ScrollView>

      <ChatInput
        onSendMessage={handleSendMessage}
        placeholder="Start typing..."
      />
    </SafeAreaView>
  );
};

export default AiSheet;
