/* eslint-disable react/no-children-prop */
/* eslint-disable @typescript-eslint/no-require-imports */
import { Box } from '@/components/ui/box';
import { useSession } from '@/modules/login/auth-provider';
import { useLocalSearchParams } from 'expo-router';
import * as React from 'react';
import { View, Text, ImageBackground, Platform } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { Input, InputField, InputSlot, InputIcon } from '@/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { Alert, AlertText, AlertIcon } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react-native';

import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { Eye, EyeOff, Mail, Lock, Fingerprint } from 'lucide-react-native';
import { useState, useEffect, useRef } from 'react';
import { Alert as RNAlert } from 'react-native';
import { router } from 'expo-router';
import { useForm } from '@tanstack/react-form';
import { loginSchema } from '@/modules/login/schemas';
import { useFocusEffect } from '@react-navigation/native';

import { isEmpty } from 'lodash/fp';

export default function Login() {
  const { email: emailParam } = useLocalSearchParams<{ email?: string }>();
  const {
    signIn,
    signInWithBiometric,
    biometricState,
    enableBiometric,
    data,
    isLoading,
    isError,
    error,
    checkBiometricStatus,
  } = useSession();
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [biometricLoading, setBiometricLoading] = useState(false);
  const [lastLoginCredentials, setLastLoginCredentials] = useState<{
    email: string;
    password: string;
  } | null>({ email: emailParam ?? '', password: '' });

  // Track if we've already checked biometric status on this screen focus
  const hasCheckedBiometricRef = useRef(false);

  // Refresh biometric status when screen comes into focus (only once per focus)
  useFocusEffect(
    React.useCallback(() => {
      if (!hasCheckedBiometricRef.current && checkBiometricStatus) {
        hasCheckedBiometricRef.current = true;
        checkBiometricStatus();
      }

      // Reset the flag when screen loses focus
      return () => {
        hasCheckedBiometricRef.current = false;
      };
    }, [checkBiometricStatus])
  );

  // Update error message when authentication fails
  useEffect(() => {
    if (isError && error) {
      setLoginError('Login failed. Check your email and password.');
    } else {
      setLoginError(null);
    }
  }, [isError, error]);

  // Get biometric type name for display
  const getBiometricTypeName = React.useCallback(() => {
    if (biometricState.supportedTypes.includes(1)) {
      // FACIAL_RECOGNITION
      return 'Face ID';
    }
    if (biometricState.supportedTypes.includes(2)) {
      // FINGERPRINT
      return 'Touch ID';
    }
    return 'Biometric';
  }, [biometricState.supportedTypes]);

  // Prompt user to enable biometric authentication after successful login
  const promptBiometricSetup = React.useCallback(
    (email: string, password: string) => {
      if (
        biometricState.isSupported &&
        biometricState.isEnrolled &&
        !biometricState.isEnabled
      ) {
        RNAlert.alert(
          `Enable ${getBiometricTypeName()}?`,
          `Would you like to enable ${getBiometricTypeName()} for faster sign-in next time?`,
          [
            {
              text: 'Not Now',
              style: 'cancel',
            },
            {
              text: 'Enable',
              onPress: async () => {
                const success = await enableBiometric(email, password);
                if (success) {
                  RNAlert.alert(
                    'Success',
                    `${getBiometricTypeName()} has been enabled for your account.`
                  );
                }
              },
            },
          ]
        );
      }
    },
    [
      biometricState.isSupported,
      biometricState.isEnrolled,
      biometricState.isEnabled,
      getBiometricTypeName,
      enableBiometric,
    ]
  );

  // Watch for successful login to prompt biometric setup
  useEffect(() => {
    if (data && lastLoginCredentials && !isLoading && !isError) {
      // Successful login detected
      setTimeout(() => {
        promptBiometricSetup(
          lastLoginCredentials.email,
          lastLoginCredentials.password
        );
        setLastLoginCredentials(null); // Clear credentials after use
      }, 1000);
    }
  }, [data, lastLoginCredentials, isLoading, isError, promptBiometricSetup]);

  const form = useForm({
    defaultValues: {
      email: emailParam || '',
      password: '',
    },
    validators: {
      onChange: loginSchema,
    },
    onSubmit: async ({ value }) => {
      setLoginError(null);
      // Store credentials for potential biometric setup prompt
      setLastLoginCredentials({
        email: value.email,
        password: value.password,
      });
      // Trigger sign in
      signIn({
        email: value.email,
        password: value.password,
      });
    },
  });

  const handleForgotPassword = () => router.push('/(auth)/forgot-password');

  const handleContactSupport = () => {
    // Navigate to contact support screen or open email/chat
  };

  const handleGuestAccess = () => router.replace('/(tabs)/(weather)');

  const handleBiometricLogin = async () => {
    setBiometricLoading(true);
    setLoginError(null);

    try {
      const success = await signInWithBiometric();
      if (!success) {
        setLoginError('Biometric authentication failed. Please try again.');
      } else {
        // eslint-disable-next-line no-console
        console.log('Biometric login successful');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Biometric login error:', error);
      setLoginError('Biometric authentication failed. Please try again.');
    } finally {
      setBiometricLoading(false);
    }
  };

  return (
    <KeyboardAwareScrollView
      bottomOffset={90}
      enabled={true}
      keyboardShouldPersistTaps="handled"
      contentContainerStyle={{
        flexGrow: 1,
      }}
    >
      <View className="flex-1 bg-background-0">
        <View className="h-[330px]">
          <ImageBackground
            source={require('@/assets/images/login-bg.png')}
            className="h-full w-full justify-center items-center"
          >
            <Text className="text-3xl font-dm-sans-bold text-white">YMCA</Text>
          </ImageBackground>
        </View>

        <Box className="bg-background-0 rounded-t-3xl -mt-5 px-6 pt-6 pb-10">
          <Text className="text-2xl font-dm-sans-bold text-[#00697B] mb-2">
            Login
          </Text>
          <Text className="text-gray-600 mb-6">
            Provide the email address associated with your YMCA account, and
            your password
          </Text>

          {loginError && (
            <Alert action="error" className="mb-3">
              <AlertIcon as={AlertCircle} />
              <AlertText className="text-sm">{loginError}</AlertText>
            </Alert>
          )}

          {/* Email Input */}
          <form.Field
            name="email"
            children={field => (
              <FormControl className="mb-4">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    Email
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? 'border-red-500'
                      : 'border-gray-300'
                  } rounded-lg`}
                  accessibilityLabel="Email input"
                  accessibilityHint="Enter your email address"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={Mail} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Enter your email address"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    className="text-gray-800"
                    accessibilityRole="text"
                    textContentType="emailAddress"
                    returnKeyType="next"
                  />
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(', ')}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          {/* Password Input */}
          <form.Field
            name="password"
            children={field => (
              <FormControl className="mb-2">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    Password
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? 'border-red-500'
                      : 'border-gray-300'
                  } rounded-lg`}
                  accessibilityLabel="Password input"
                  accessibilityHint="Enter your password"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={Lock} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Enter your password"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    className="text-gray-800"
                    accessibilityRole="text"
                    textContentType="password"
                    returnKeyType="done"
                    onSubmitEditing={() => form.handleSubmit()}
                  />
                  <InputSlot
                    className="pr-3"
                    onPress={() => setShowPassword(!showPassword)}
                    accessibilityLabel={
                      showPassword ? 'Hide password' : 'Show password'
                    }
                    accessibilityRole="button"
                  >
                    <InputIcon
                      as={showPassword ? EyeOff : Eye}
                      className="text-gray-500"
                    />
                  </InputSlot>
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(', ')}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          {/* Forgot Password Link */}
          <View className="items-end mb-4">
            <Text
              className="text-[#00697B] font-dm-sans-medium"
              onPress={handleForgotPassword}
            >
              Forgot password?
            </Text>
          </View>

          <form.Subscribe
            selector={state => [
              state.canSubmit,
              state.isSubmitting,
              state.isValidating,
            ]}
          >
            {([canSubmit, isSubmitting]) => (
              <Button
                className="bg-[#00697B] rounded-full mb-4"
                onPress={() => form.handleSubmit()}
                disabled={!canSubmit || isLoading || isSubmitting}
                accessibilityLabel={isLoading ? 'Logging in...' : 'Login'}
                accessibilityRole="button"
                accessibilityState={{
                  disabled: !canSubmit || isLoading || isSubmitting,
                }}
              >
                <ButtonText>{isLoading ? 'Logging in...' : 'Login'}</ButtonText>
              </Button>
            )}
          </form.Subscribe>

          {/* Biometric Authentication Button */}
          {biometricState.isSupported &&
            biometricState.isEnrolled &&
            biometricState.isEnabled && (
              <Button
                variant="link"
                className="rounded-full mb-4"
                onPress={handleBiometricLogin}
                disabled={biometricLoading || isLoading}
                accessibilityLabel={`Sign in with ${getBiometricTypeName()}`}
                accessibilityRole="button"
              >
                <Fingerprint className="text-[#00697B] mr-2" size={20} />
                <ButtonText>
                  {biometricLoading
                    ? 'Authenticating...'
                    : `Sign in with ${Platform.OS === 'ios' ? 'Face ID' : 'Touch ID'}`}
                </ButtonText>
              </Button>
            )}

          <View className="flex-row items-center mb-4">
            <View className="flex-1 h-[1px] bg-gray-300" />
            <Text className="mx-4 text-gray-500 font-dm-sans-medium">
              Or continue with
            </Text>
            <View className="flex-1 h-[1px] bg-gray-300" />
          </View>

          <Button
            className="bg-white border border-[#00697B] rounded-full py-3 mb-4"
            onPress={handleGuestAccess}
          >
            <Text className="text-[#00697B]  font-dm-sans-medium text-center">
              Continue as Guest
            </Text>
          </Button>

          <View className="items-center pt-2">
            <Text className="text-gray-500">
              Need help?{' '}
              <Text
                className="text-[#00697B] font-dm-sans-medium"
                onPress={handleContactSupport}
              >
                Contact support
              </Text>
            </Text>
          </View>
        </Box>
      </View>
    </KeyboardAwareScrollView>
  );
}
