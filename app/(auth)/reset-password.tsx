/* eslint-disable react/no-children-prop */
/* eslint-disable @typescript-eslint/no-require-imports */
import { Box } from '@/components/ui/box';
import * as React from 'react';
import { View, Text, ImageBackground } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { Input, InputField, InputSlot, InputIcon } from '@/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import { Alert, AlertText, AlertIcon } from '@/components/ui/alert';
import {
  AlertCircle,
  ArrowLeft,
  Eye,
  EyeOff,
  Lock,
  KeyRound,
} from 'lucide-react-native';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { useState, useEffect } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import { useResetPassword } from '@/modules/login/hooks/useResetPassword';
import { Pressable } from '@/components/ui/pressable';
import { useForm } from '@tanstack/react-form';
import { resetPasswordSchema } from '@/modules/login/schemas';
import { isEmpty } from 'lodash/fp';

export default function ResetPassword() {
  const { email } = useLocalSearchParams<{ email: string }>();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    mutate: resetPasswordMutation,
    isPending,
    isError,
    error: apiError,
    data,
  } = useResetPassword(() => router.replace('/(auth)/reset-success'));

  // Handle API errors
  useEffect(() => {
    if (isError && apiError) {
      setError(
        apiError.message || 'Failed to reset password. Please try again.'
      );
    } else if (data && !data.success) {
      setError(data.message || 'Failed to reset password. Please try again.');
    }
  }, [isError, apiError, data]);

  const form = useForm({
    defaultValues: {
      pin: '',
      password: '',
      confirmPassword: '',
    },
    validators: {
      onChange: resetPasswordSchema,
    },
    onSubmit: async ({ value }) => {
      setError(null);
      resetPasswordMutation({
        email,
        pin: value.pin,
        password: value.password,
        confirm_password: value.confirmPassword,
      });
    },
  });

  const handleBack = () => {
    router.back();
  };

  return (
    <KeyboardAwareScrollView
      bottomOffset={90}
      enabled={true}
      keyboardShouldPersistTaps="handled"
      contentContainerStyle={{
        flexGrow: 1,
      }}
    >
      <View className="flex-1 bg-background-0">
        <View className="h-[330px]">
          <ImageBackground
            source={require('@/assets/images/login-bg.png')}
            className="h-full w-full justify-center items-center"
          >
            <Text className="text-3xl font-dm-sans-bold text-white">YMCA</Text>
          </ImageBackground>
        </View>

        <Box className="bg-background-0 rounded-t-3xl -mt-5 px-6 pt-6 pb-10">
          <Pressable onPress={handleBack} className="mb-4">
            <ArrowLeft className="text-[#00697B]" />
          </Pressable>

          <Text className="text-2xl font-dm-sans-bold text-[#00697B] mb-2">
            Reset password
          </Text>
          <Text className="text-gray-600 mb-6">
            Enter the PIN sent to your email and create a new password
          </Text>

          {/* Error Message */}
          {error && (
            <Alert action="error" className="mb-3">
              <AlertIcon as={AlertCircle} />
              <AlertText className="text-sm">{error}</AlertText>
            </Alert>
          )}

          {/* PIN Input */}
          <form.Field
            name="pin"
            children={field => (
              <FormControl className="mb-4">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    PIN
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? 'border-red-500'
                      : 'border-gray-300'
                  } rounded-lg`}
                  accessibilityLabel="PIN input"
                  accessibilityHint="Enter the PIN sent to your email"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={KeyRound} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Enter PIN from email"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    keyboardType="number-pad"
                    maxLength={6}
                    className="text-gray-800"
                    accessibilityRole="text"
                    returnKeyType="next"
                  />
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(', ')}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          {/* New Password Input */}
          <form.Field
            name="password"
            children={field => (
              <FormControl className="mb-4">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    New password
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? 'border-red-500'
                      : 'border-gray-300'
                  } rounded-lg`}
                  accessibilityLabel="New password input"
                  accessibilityHint="Enter your new password"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={Lock} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Enter password"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    className="text-gray-800"
                    accessibilityRole="text"
                    textContentType="newPassword"
                    returnKeyType="next"
                  />
                  <InputSlot
                    className="pr-3"
                    onPress={() => setShowPassword(!showPassword)}
                    accessibilityLabel={
                      showPassword ? 'Hide password' : 'Show password'
                    }
                    accessibilityRole="button"
                  >
                    <InputIcon
                      as={showPassword ? EyeOff : Eye}
                      className="text-gray-500"
                    />
                  </InputSlot>
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(', ')}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          {/* Confirm Password Input */}
          <form.Field
            name="confirmPassword"
            children={field => (
              <FormControl className="mb-6">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    Confirm password
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? 'border-red-500'
                      : 'border-gray-300'
                  } rounded-lg`}
                  accessibilityLabel="Confirm password input"
                  accessibilityHint="Re-enter your new password to confirm"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={Lock} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Re-enter new password"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    className="text-gray-800"
                    accessibilityRole="text"
                    textContentType="newPassword"
                    returnKeyType="done"
                    onSubmitEditing={() => form.handleSubmit()}
                  />
                  <InputSlot
                    className="pr-3"
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    accessibilityLabel={
                      showConfirmPassword ? 'Hide password' : 'Show password'
                    }
                    accessibilityRole="button"
                  >
                    <InputIcon
                      as={showConfirmPassword ? EyeOff : Eye}
                      className="text-gray-500"
                    />
                  </InputSlot>
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(', ')}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          <form.Subscribe
            selector={state => [state.canSubmit, state.isSubmitting]}
          >
            {([canSubmit, isSubmitting]) => (
              <Button
                className="bg-[#00697B] rounded-full mb-4"
                onPress={() => form.handleSubmit()}
                disabled={!canSubmit || isPending || isSubmitting}
                accessibilityLabel={
                  isPending ? 'Resetting...' : 'Reset password'
                }
                accessibilityRole="button"
                accessibilityState={{
                  disabled: !canSubmit || isPending || isSubmitting,
                }}
              >
                <ButtonText>
                  {isPending ? 'Resetting...' : 'Reset password'}
                </ButtonText>
              </Button>
            )}
          </form.Subscribe>
        </Box>
      </View>
    </KeyboardAwareScrollView>
  );
}
