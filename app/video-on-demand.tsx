import React, { useMemo } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import { SearchInput } from '@/components/shared/search';
import { FlatList } from 'react-native';
import { OnDemandCategoryCard } from '@/components/screens/on-demand/category-card';
import { useOnDemandCategories } from '@/data/screens/on-demand/queries/useOnDemandCategories';
import { CategoriesGridSkeleton } from '@/components/screens/on-demand/category-skeleton';
import { EmptySearchState } from '@/components/shared/empty-search';
import { EmptyState } from '@/components/screens/classes/empty-state';
import { EmptyVideoIcon } from '@/components/shared/icon/empty-video';
import { matchSorter } from 'match-sorter';

const isWithinLastNDays = (dateStr?: string | null, days = 8) => {
  if (!dateStr) return false;
  const created = new Date(dateStr);
  if (Number.isNaN(created.getTime())) return false;
  const diff = Date.now() - created.getTime();
  return diff <= days * 24 * 60 * 60 * 1000;
};

const VideoOnDemand = () => {
  const { data = [], isLoading, isFetching, refetch } = useOnDemandCategories();
  const [searchTerm, setSearchTerm] = React.useState('');

  const filtered = useMemo(() => {
    if (!searchTerm) return data;
    return matchSorter(data, searchTerm, {
      keys: ['name', 'videos_count'],
    });
  }, [data, searchTerm]);

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <VStack space="md" className="px-4 pt-4 pb-2">
          <HStack className="justify-between items-center">
            <HStack className="items-center" space="md">
              <Pressable
                onPress={() => router.back()}
                className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
              >
                <Icon
                  as={ChevronLeft}
                  size="lg"
                  className="text-typography-900"
                />
              </Pressable>
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Video on demand
              </Text>
            </HStack>
          </HStack>
        </VStack>

        <SearchInput
          onSearch={setSearchTerm}
          searchTerm={searchTerm}
          placeholder="Search category"
        />
        {isLoading ? (
          <CategoriesGridSkeleton />
        ) : filtered.length === 0 && searchTerm ? (
          <EmptySearchState
            searchTerm={searchTerm}
            onClearSearch={() => setSearchTerm('')}
          />
        ) : filtered.length === 0 ? (
          <EmptyState
            icon={<EmptyVideoIcon />}
            title="No video categories available"
            subtitle="There are no video categories available at the moment. Please check back later."
          />
        ) : (
          <FlatList
            data={filtered}
            keyExtractor={item => String(item.id)}
            numColumns={2}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 12,
              paddingBottom: 24,
            }}
            refreshing={isFetching}
            onRefresh={refetch}
            renderItem={({ item }) => (
              <OnDemandCategoryCard
                id={item.id}
                name={item.name}
                imageUrl={item.category_image}
                videosCount={item.videos_count || 0}
                isNew={isWithinLastNDays(item.created_at || item.created)}
                onPress={id =>
                  router.push({
                    pathname: '/(on-demand)/[categoryId]',
                    params: { categoryId: String(id), name: item.name },
                  })
                }
              />
            )}
          />
        )}
      </VStack>
    </SafeAreaView>
  );
};

export default VideoOnDemand;
