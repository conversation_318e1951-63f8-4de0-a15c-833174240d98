import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import { useForm } from '@tanstack/react-form';
import {
  getInitials,
  getRandomColorForInitials,
} from '@/data/common/common.utils';
import {
  Radio,
  RadioGroup,
  RadioIndicator,
  RadioLabel,
} from '@/components/ui/radio';
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxLabel,
  CheckboxIcon,
} from '@/components/ui/checkbox';
import { Check, CheckCircle } from 'lucide-react-native';
import {
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from '@/components/ui/avatar';

// Mock trainer data for the avatar section
const mockTrainers = [
  { id: 1, name: 'Anyone', image: null },
  {
    id: 2,
    name: 'David',
    image:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 3,
    name: 'Gideon',
    image:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 4,
    name: 'RJ',
    image:
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 5,
    name: 'Vyles',
    image:
      'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 6,
    name: 'Victor',
    image:
      'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
  },
];

const RequestInfoHeader = () => {
  return (
    <HStack className="items-center px-4 py-4" space="md">
      <Pressable
        onPress={() => router.back()}
        className="w-10 h-10 items-center justify-center"
      >
        <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
      </Pressable>

      <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
        Request Info
      </Text>
    </HStack>
  );
};

const RequestInfo = () => {
  const form = useForm({
    defaultValues: {
      trainingDays: [] as string[],
      timesPerWeek: '',
      availableTimes: [] as string[],
      focusAreas: [] as string[],
      gender: '',
      preferredTrainer: '',
    },
    onSubmit: async ({ value }) => {
      console.log('Form submitted:', value);
      // Handle form submission here
      // You can add API call or navigation logic
      router.back();
    },
  });

  const handleDayToggle = (day: string, currentDays: string[]) => {
    if (currentDays.includes(day)) {
      return currentDays.filter(d => d !== day);
    } else {
      return [...currentDays, day];
    }
  };

  const handleTimeToggle = (time: string, currentTimes: string[]) => {
    if (currentTimes.includes(time)) {
      return currentTimes.filter(t => t !== time);
    } else {
      return [...currentTimes, time];
    }
  };

  const handleFocusToggle = (focus: string, currentFocus: string[]) => {
    if (currentFocus.includes(focus)) {
      return currentFocus.filter(f => f !== focus);
    } else {
      return [...currentFocus, focus];
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <RequestInfoHeader />

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="px-4 pb-6" space="lg">
          {/* Description */}
          <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
            Please fill out the information below and we will get back to you.
          </Text>

          {/* Training Days */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What days per week are you looking to train?
            </Text>

            <form.Field name="trainingDays">
              {field => (
                <VStack space="sm">
                  <HStack space="sm" className="flex-wrap">
                    {['Monday', 'Tuesday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Wednesday', 'Thursday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Friday', 'Saturday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <Pressable
                    onPress={() =>
                      field.handleChange(
                        handleDayToggle('Sunday', field.state.value)
                      )
                    }
                    className={`px-4 py-3 rounded-full border self-start ${
                      field.state.value.includes('Sunday')
                        ? 'border-[#00BFE0]'
                        : 'bg-background-50 border-background-300'
                    }`}
                    style={
                      field.state.value.includes('Sunday')
                        ? { backgroundColor: '#00BFE0' }
                        : {}
                    }
                  >
                    <HStack className="items-center" space="xs">
                      <Text
                        className={`font-dm-sans-medium ${
                          field.state.value.includes('Sunday')
                            ? 'text-white'
                            : 'text-typography-700'
                        }`}
                      >
                        Sunday
                      </Text>
                      {field.state.value.includes('Sunday') && (
                        <CheckCircle size={16} color="white" />
                      )}
                    </HStack>
                  </Pressable>
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Times per week */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              How many times per week are you looking to train?
            </Text>

            <form.Field name="timesPerWeek">
              {field => (
                <VStack space="sm">
                  <HStack space="sm" className="flex-wrap">
                    {['One', 'Two', 'Three', 'Four'].map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value === time
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value === time
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {time}
                          </Text>
                          {field.state.value === time && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Five', 'Six'].map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value === time
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value === time
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {time}
                          </Text>
                          {field.state.value === time && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Available Times */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What times are you available to train?
            </Text>

            <form.Field name="availableTimes">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {[
                    { key: 'morning', label: 'Morning', icon: '🌅' },
                    { key: 'afternoon', label: 'Afternoon', icon: '☀️' },
                    { key: 'evening', label: 'Evening', icon: '🌙' },
                  ].map(timeSlot => (
                    <Pressable
                      key={timeSlot.key}
                      onPress={() =>
                        field.handleChange(
                          handleTimeToggle(timeSlot.key, field.state.value)
                        )
                      }
                      className={`px-4 py-3 rounded-2xl border flex-row items-center ${
                        field.state.value.includes(timeSlot.key)
                          ? 'border-[#00BFE0]'
                          : 'bg-background-50 border-background-300'
                      }`}
                      style={
                        field.state.value.includes(timeSlot.key)
                          ? { backgroundColor: '#00BFE0' }
                          : {}
                      }
                    >
                      <Text className="text-lg mr-2">{timeSlot.icon}</Text>
                      <Text
                        className={`font-dm-sans-medium flex-1 ${
                          field.state.value.includes(timeSlot.key)
                            ? 'text-white'
                            : 'text-typography-700'
                        }`}
                      >
                        {timeSlot.label}
                      </Text>
                      {field.state.value.includes(timeSlot.key) && (
                        <CheckCircle size={16} color="white" className="ml-2" />
                      )}
                    </Pressable>
                  ))}
                </HStack>
              )}
            </form.Field>
          </VStack>

          {/* Focus Areas */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What do you want to focus on?
            </Text>

            <form.Field name="focusAreas">
              {field => (
                <VStack space="sm">
                  {[
                    'Weight management',
                    'Sports conditioning',
                    'Strength training',
                    'Marathon coaching',
                  ].map(focus => (
                    <Checkbox
                      key={focus}
                      value={focus}
                      isChecked={field.state.value.includes(focus)}
                      onChange={() =>
                        field.handleChange(
                          handleFocusToggle(focus, field.state.value)
                        )
                      }
                      className="items-start"
                    >
                      <CheckboxIndicator className="mr-3 mt-0.5">
                        <CheckboxIcon as={Check} />
                      </CheckboxIndicator>
                      <CheckboxLabel className="flex-1">
                        <Text className="text-base font-dm-sans-regular text-typography-900">
                          {focus}
                        </Text>
                      </CheckboxLabel>
                    </Checkbox>
                  ))}
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Gender */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Gender
            </Text>

            <form.Field name="gender">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {['Male', 'Female', 'Prefer not to say'].map(gender => (
                    <Pressable
                      key={gender}
                      onPress={() => field.handleChange(gender)}
                      className={`px-4 py-3 rounded-full border ${
                        field.state.value === gender
                          ? 'border-[#00BFE0]'
                          : 'bg-background-50 border-background-300'
                      }`}
                      style={
                        field.state.value === gender
                          ? { backgroundColor: '#00BFE0' }
                          : {}
                      }
                    >
                      <HStack className="items-center" space="xs">
                        <Text
                          className={`font-dm-sans-medium ${
                            field.state.value === gender
                              ? 'text-white'
                              : 'text-typography-700'
                          }`}
                        >
                          {gender}
                        </Text>
                        {field.state.value === gender && (
                          <CheckCircle size={16} color="white" />
                        )}
                      </HStack>
                    </Pressable>
                  ))}
                </HStack>
              )}
            </form.Field>
          </VStack>

          {/* Trainer Selection */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Is there a particular trainer you want to work with?
            </Text>

            <form.Field name="preferredTrainer">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {mockTrainers.map(trainer => (
                    <Pressable
                      key={trainer.id}
                      onPress={() => field.handleChange(trainer.name)}
                      className={`items-center ${
                        field.state.value === trainer.name
                          ? 'opacity-100'
                          : 'opacity-70'
                      }`}
                    >
                      <VStack className="items-center" space="xs">
                        <VStack className="relative">
                          <Avatar
                            size="lg"
                            className={`border-2 ${
                              field.state.value === trainer.name
                                ? 'border-[#00BFE0]'
                                : 'border-transparent'
                            }`}
                          >
                            {trainer.image ? (
                              <AvatarImage
                                source={{ uri: trainer.image }}
                                alt={trainer.name}
                              />
                            ) : (
                              <AvatarFallbackText className="bg-background-300 text-typography-700">
                                {getInitials(trainer.name)}
                              </AvatarFallbackText>
                            )}
                          </Avatar>
                          {field.state.value === trainer.name && (
                            <VStack className="absolute -top-1 -right-1 bg-[#00BFE0] rounded-full p-1">
                              <CheckCircle size={16} color="white" />
                            </VStack>
                          )}
                        </VStack>
                        <Text
                          className={`text-xs font-dm-sans-medium ${
                            field.state.value === trainer.name
                              ? 'text-[#00BFE0]'
                              : 'text-typography-700'
                          }`}
                        >
                          {trainer.name}
                        </Text>
                      </VStack>
                    </Pressable>
                  ))}
                </HStack>
              )}
            </form.Field>
          </VStack>
        </VStack>
      </ScrollView>

      {/* Submit Button */}
      <VStack className="bg-background-0 px-4 py-4 border-t border-background-200">
        <Button
          variant="solid"
          size="lg"
          className="rounded-full"
          style={{ backgroundColor: '#00BFE0' }}
          onPress={() => form.handleSubmit()}
        >
          <ButtonText className="text-white font-dm-sans-bold text-base">
            Submit
          </ButtonText>
        </Button>
      </VStack>
    </SafeAreaView>
  );
};

export default RequestInfo;
