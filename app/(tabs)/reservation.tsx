import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { ReservationsHeader } from '@/components/screens/reservations/reservations-header';
import { ReservationsCategories } from '@/components/screens/reservations/reservations-categories';
import { SearchInput } from '@/components/shared/search';
import { ReservationsList } from '@/components/screens/reservations/reservations-list';
import { useReservationsWithFilter } from '@/hooks/useReservationsWithFilter';

const ReservationsScreen = () => {
  const {
    reservations,
    isLoading,
    isRefetching,
    selectedTab,
    setSelectedTab,
    selectedCategory,
    setSelectedCategory,
    searchTerm,
    setSearchTerm,
    clearSearch,
    refetch,
    counts,
  } = useReservationsWithFilter();

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <VStack className="flex-1">
        <ReservationsHeader
          selectedTab={selectedTab}
          onTabChange={setSelectedTab}
        />

        <VStack space="sm" className="mt-2 bg-white">
          <ReservationsCategories
            selected={selectedCategory}
            onChange={setSelectedCategory}
            counts={counts}
          />
          <VStack className="mt-4 bg-white">
            <SearchInput
              placeholder="Search"
              searchTerm={searchTerm}
              onSearch={setSearchTerm}
              showFilter={false}
            />
          </VStack>
        </VStack>

        <ReservationsList
          reservations={reservations}
          searchTerm={searchTerm}
          isLoading={isLoading}
          isRefreshing={isRefetching}
          onRefresh={refetch}
          onClearSearch={clearSearch}
          selectedTab={selectedTab}
        />
      </VStack>
    </SafeAreaView>
  );
};

export default ReservationsScreen;
