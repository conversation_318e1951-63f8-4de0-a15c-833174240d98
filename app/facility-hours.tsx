import React, { useMemo, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { HorizontalDatePicker } from '@/components/shared/horizontal-date-picker';
import { SearchWithFilter } from '@/components/shared/search-with-filter';
import { FacilityFilter } from '@/components/shared/facility-filter';
import { FlatList } from 'react-native';
import { Box } from '@/components/ui/box';
import { format, getDay } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';
import { useFacilities } from '@/data/screens/facility-hours/queries/useFacilities';
import type { Facility } from '@/data/api-client/facilities';
import { ArrowLeft } from 'lucide-react-native';
import { Clock, Setting4 } from 'iconsax-react-nativejs';
import { router } from 'expo-router';
import { CardSkeletonLoader } from '@/components/shared/card-skeleton';

const FacilityHours = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFacilities, setSelectedFacilities] = useState<string[]>([]);

  const {
    data: facilities = [],
    isLoading,
    isRefetching,
    refetch,
  } = useFacilities(format(selectedDate, DATE_FORMAT.YEAR_MONTH_DAY));

  const selectedDateStr = useMemo(
    () => format(selectedDate, DATE_FORMAT.YEAR_MONTH_DAY),
    [selectedDate]
  );

  const filteredFacilities = useMemo(() => {
    const term = searchTerm.trim().toLowerCase();
    return facilities
      .filter(
        facility =>
          selectedFacilities.length === 0 ||
          selectedFacilities.includes(String(facility.id))
      )
      .filter(facility => (term ? facility.name.toLowerCase().includes(term) : true));
  }, [facilities, selectedFacilities, searchTerm]);

  const getDisplayForFacility = (facility: Facility) => {
    const slots = facility?.slots ?? [];
    const slot = slots.find(s => s?.date === selectedDateStr);

    if (!slot) {
      return { label: "Today's hours", value: 'Closed all day', isClosed: true };
    }

    const hasClosure = Boolean(slot.has_closure);
    const closedAllDay = Boolean(slot.closed_allday);
    const start = slot.start_time?.trim();
    const end = slot.end_time?.trim();

    if (!hasClosure && !closedAllDay) {
      const value = start && end ? `${start} - ${end}` : 'Closed all day';
      return { label: "Today's hours", value };
    }

    if (hasClosure && closedAllDay) {
      const reason = slot.closure_reason?.trim();
      return { label: "Today's hours", value: reason || 'Closed all day', isClosed: true };
    }

    if (hasClosure && !closedAllDay) {
      const value = start && end ? `${start} - ${end}` : 'Closed all day';
      return { label: 'Updated Hours', value };
    }

    return { label: "Today's hours", value: 'Closed all day', isClosed: true };
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <HStack className="px-4 py-4 bg-white items-center" space="md">
          <Pressable
            onPress={() => router.back()}
            className="w-10 h-10 items-center justify-center rounded-full bg-background-100"
          >
            <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
          </Pressable>
          <Text className="text-lg font-dm-sans-bold text-typography-900">
            Facility hours
          </Text>
        </HStack>

        <VStack>
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            config={{ mode: 'days', showCalendarButton: true }}
          />
          <SearchWithFilter
            onSearch={setSearchTerm}
            searchTerm={searchTerm}
            placeholder="Search facilities"
          />
          <Box className="px-4">
            <FacilityFilter
              value={selectedFacilities}
              onChange={setSelectedFacilities}
            />
          </Box>
        </VStack>

        <VStack className="px-4" space="md">
          {isLoading ? (
            <CardSkeletonLoader />
          ) : (
            <FlatList
              data={filteredFacilities}
              keyExtractor={item => String(item.id)}
              refreshing={isRefetching}
              onRefresh={refetch}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 600, paddingTop: 8 }}
              renderItem={({ item }) => (
                <Box className="bg-white rounded-2xl px-4 py-3 mb-3 border border-background-200">
                  <VStack space="xs">
                    <Text className="text-base font-dm-sans-bold text-typography-900 text-cyan-600">
                      {item.name}
                    </Text>
                    <HStack className="items-center justify-between">
                      {(() => {
                        const { label, value, isClosed } = getDisplayForFacility(item as Facility);
                        return (
                          <>
                            <Text className="text-xs text-typography-600">{label}</Text>
                            <HStack className="items-center">
                            {!isClosed && <Clock size={14} color='gray' className='mt-2' /> }  
                            <Text className={`text-xs text-typography-700 ${isClosed ? 'text-red-600 font-bold bg-red-100 px-1 py-1 rounded-md' : ''}`}> {value}</Text>
                            </HStack>
                           
                          </>
                        );
                      })()}
                    </HStack>
                  </VStack>
                </Box>
              )}
            />
          )}
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default FacilityHours;


