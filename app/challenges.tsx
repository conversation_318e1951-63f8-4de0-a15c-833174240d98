import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { SearchWithFilter } from '@/components/shared/search-with-filter';
import { ChallengesHeader } from '@/components/screens/challenges/challenges-header';
import { ChallengesList } from '@/components/screens/challenges/challenges-list';
import { useChallengesWithFilter } from '@/hooks/useChallengesWithFilter';

const Challenges = () => {
  const {
    challenges,
    isLoading,
    isRefetching,
    selectedTab,
    setSelectedTab,
    searchTerm,
    setSearchTerm,
    clearSearch,
    refetch,
  } = useChallengesWithFilter();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ChallengesHeader
          selectedTab={selectedTab}
          onTabChange={setSelectedTab}
        />
        <SearchWithFilter
          onSearch={setSearchTerm}
          searchTerm={searchTerm}
          placeholder="Search by name"
        />
        <ChallengesList
          challenges={challenges}
          searchTerm={searchTerm}
          isLoading={isLoading}
          isRefreshing={isRefetching}
          onRefresh={refetch}
          onClearSearch={clearSearch}
          selectedTab={selectedTab}
        />
      </VStack>
    </SafeAreaView>
  );
};

export default Challenges;
