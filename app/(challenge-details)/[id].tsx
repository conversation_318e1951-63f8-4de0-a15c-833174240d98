import { useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { ChallengeDetails } from '@/components/screens/challenges/details/challenge-details';
import { Spinner } from '@/components/ui/spinner';
import { useChallengeByIdQuery } from '@/data/screens/challenges/queries/useChallengesQuery';

const ChallengeDetailsScreen = () => {
  const { id, tab } = useLocalSearchParams<{
    id: string;
    tab?: 'active' | 'history';
  }>();
  const { data, isPending } = useChallengeByIdQuery(id);

  if (isPending) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Spinner />
        </VStack>
      </SafeAreaView>
    );
  }

  if (!data) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Challenge Not Found
          </Text>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <ChallengeDetails
      item={data}
      isLoading={isPending}
      selectedTab={tab === 'history' ? 'history' : 'active'}
    />
  );
};

export default ChallengeDetailsScreen;
