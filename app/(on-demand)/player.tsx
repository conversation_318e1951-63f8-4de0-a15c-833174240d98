import React, { useMemo } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronLeft } from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { WebView } from 'react-native-webview';
import { VideoView, useVideoPlayer } from 'expo-video';
import { getPlayerType } from '@/utils/video-utils';

const Player = () => {
  const { url, title } = useLocalSearchParams<{
    url: string;
    title?: string;
  }>();

  const playerType = useMemo(
    () => getPlayerType(url as string),
    [url]
  );

  const player = useVideoPlayer((url as string) ?? null, p => {
    if (!url || playerType === 'webview') return; // handled by WebView path
    p.play();
  });

  return (
    <SafeAreaView className="flex-1 bg-black">
      <VStack className="flex-1">
        <VStack className="px-4 pt-4 pb-2">
          <HStack className="items-center" space="md">
            <Pressable
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-900 rounded-full items-center justify-center"
            >
              <Icon as={ChevronLeft} size="lg" className="text-white" />
            </Pressable>
            <Text
              className="text-white font-dm-sans-bold text-base"
              numberOfLines={1}
            >
              {title || 'Player'}
            </Text>
          </HStack>
        </VStack>

        <View className="flex-1">
          {playerType === 'webview' ? (
            <WebView
              source={{ uri: url as string }}
              style={{ flex: 1 }}
              allowsFullscreenVideo
            />
          ) : (
            <VideoView
              style={{ flex: 1 }}
              player={player}
              allowsFullscreen
              allowsPictureInPicture
              nativeControls
            />
          )}
        </View>
      </VStack>
    </SafeAreaView>
  );
};

export default Player;
