import React, { useMemo } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronLeft } from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { router, useLocalSearchParams } from 'expo-router';
import { FlatList, View } from 'react-native';
import { SearchInput } from '@/components/shared/search';
import { useOnDemandVideosByCategory } from '@/data/screens/on-demand/queries/useOnDemandVideosByCategory';
import { VideoCard } from '@/components/screens/on-demand/video-card';
import { EmptySearchState } from '@/components/shared/empty-search';
import { EmptyState } from '@/components/screens/classes/empty-state';
import { EmptyVideoIcon } from '@/components/shared/icon/empty-video';
import { matchSorter } from 'match-sorter';

const CategoryVideos = () => {
  const { categoryId, name } = useLocalSearchParams<{
    categoryId: string;
    name?: string;
  }>();
  const id = Number(categoryId);
  const {
    data = [],
    isLoading,
    isFetching,
    refetch,
  } = useOnDemandVideosByCategory(id);
  const [searchTerm, setSearchTerm] = React.useState('');

  const filtered = useMemo(() => {
    if (!searchTerm) return data;
    return matchSorter(data, searchTerm, {
      keys: ['name', 'description'],
    });
  }, [data, searchTerm]);

  const handlePlay = (video: {
    id: number;
    name: string;
    video_url: string;
  }) => {
    router.push({
      pathname: '/(on-demand)/player',
      params: { url: video.video_url, title: video.name },
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <VStack space="md" className="px-4 pt-4 pb-2">
          <HStack className="items-center" space="md">
            <Pressable
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
            >
              <Icon
                as={ChevronLeft}
                size="lg"
                className="text-typography-900"
              />
            </Pressable>
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              {name || 'Videos'}
            </Text>
          </HStack>
        </VStack>

        <SearchInput
          onSearch={setSearchTerm}
          searchTerm={searchTerm}
          placeholder="Search by name"
        />

        {isLoading ? (
          <View className="px-4 py-8">
            <Text className="text-typography-600">Loading videos…</Text>
          </View>
        ) : filtered.length === 0 && searchTerm ? (
          <EmptySearchState 
            searchTerm={searchTerm} 
            onClearSearch={() => setSearchTerm('')} 
          />
        ) : filtered.length === 0 ? (
          <EmptyState
            icon={<EmptyVideoIcon />}
            title="No videos available"
            subtitle="There are no videos available in this category at the moment. Please check back later."
          />
        ) : (
          <FlatList
            data={filtered}
            keyExtractor={item => String(item.id)}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 12,
              paddingBottom: 24,
            }}
            refreshing={isFetching}
            onRefresh={refetch}
            renderItem={({ item }) => (
              <VideoCard video={item} onPlay={handlePlay} />
            )}
          />
        )}
      </VStack>
    </SafeAreaView>
  );
};

export default CategoryVideos;
