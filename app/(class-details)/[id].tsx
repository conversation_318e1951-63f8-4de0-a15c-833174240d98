import React, { useMemo } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';

import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { ClassDetails } from '@/components/screens/classes/class-details/class-details';
import { ClassDetailsSkeleton } from '@/components/screens/classes/class-details/class-details-skeleton';
import { useClassesQuery } from '@/data/screens/classes/queries/useClassesQuery';

const ClassDetailsScreen = () => {
  const { id, date, gym_id } = useLocalSearchParams<{
    id: string;
    date: string;
    gym_id: string;
  }>();

  const {
    data: classData,
    isLoading,
    error,
  } = useClassesQuery({ date, gym_id });

  const foundData = useMemo(
    () => classData?.find(item => item.id === Number(id)),
    [classData, id]
  );

  if (isLoading) {
    return <ClassDetailsSkeleton />;
  }

  if (error || !foundData) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Class Not Found
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 text-center mb-6">
            The class you&#39;re looking for doesn&#39;t exist or has been
            removed.
          </Text>
          <Pressable
            onPress={() => router.back()}
            className="bg-primary-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-dm-sans-medium">Go Back</Text>
          </Pressable>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <ClassDetails
      classItem={foundData}
      isLoading={isLoading}
      selectedDate={date}
    />
  );
};

export default ClassDetailsScreen;
