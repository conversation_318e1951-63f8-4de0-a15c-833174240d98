import React, { useState } from 'react';
import { <PERSON><PERSON>View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { AIChatHeader } from '@/components/ai/ai-chat-header';
import { AIAvatar } from '@/components/ai/ai-avatar';
import { ChatInput } from '@/components/ai/chat-input';
import { useWorkoutPlanForm } from '@/hooks/useWorkoutPlanForm';
import { useRouter } from 'expo-router';
import { AI_RESPONSES } from '@/constants/ai-mock-data';

const WorkoutPlanScreen = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<'goal' | 'frequency' | 'complete'>('goal');
  
  const {
    form,
    fitnessGoals,
    workoutFrequencyOptions,
    setFieldValue,
    getFieldValue,
  } = useWorkoutPlanForm();

  const handleBackPress = () => {
    router.back();
  };

  const handleGoalSelect = (goal: string) => {
    setFieldValue('fitnessGoal', goal);
    setCurrentStep('frequency');
  };

  const handleFrequencySelect = (frequency: string) => {
    setFieldValue('workoutFrequency', frequency);
    // Submit the form
    form.handleSubmit();
    setCurrentStep('complete');
    // Navigate to chat to see the AI response
    setTimeout(() => {
      router.push('/ai/chat');
    }, 1000);
  };

  const handleSendMessage = (message: string) => {
    // Handle additional questions or messages
    router.push('/ai/chat');
  };

  const renderGoalSelection = () => (
    <VStack space="lg">
      <HStack className="px-4" space="sm">
        <AIAvatar size="md" />
        <VStack className="flex-1 bg-background-100 rounded-2xl rounded-bl-md p-4">
          <Text size="sm" className="text-typography-900 mb-2">
            Hey! 👋
          </Text>
          <Text size="sm" className="text-typography-900 mb-4">
            I can help you build a workout plan that fits your goals and schedule.
          </Text>
          <Text size="sm" className="text-typography-900">
            Let's start with this
          </Text>
          <Text size="sm" className="text-typography-900 font-dm-sans-medium mt-2">
            What's your main fitness goal right now?
          </Text>
        </VStack>
      </HStack>

      <VStack className="px-4" space="xs">
        {fitnessGoals.map((goal) => (
          <Pressable
            key={goal}
            onPress={() => handleGoalSelect(goal)}
            className={`p-4 rounded-xl border ${
              getFieldValue('fitnessGoal') === goal
                ? 'bg-white border-primary-500'
                : 'bg-background-50 border-background-200'
            }`}
          >
            <HStack className="items-center">
              <Box className="w-2 h-2 rounded-full bg-typography-900 mr-3" />
              <Text size="sm" className="text-typography-900">
                {goal}
              </Text>
            </HStack>
          </Pressable>
        ))}
      </VStack>

      {getFieldValue('fitnessGoal') && (
        <Box className="px-4">
          <Box className="bg-primary-500 rounded-2xl rounded-br-md p-4 self-end max-w-[80%]">
            <Text size="sm" className="text-white">
              {getFieldValue('fitnessGoal')}
            </Text>
          </Box>
        </Box>
      )}
    </VStack>
  );

  const renderFrequencySelection = () => (
    <VStack space="lg">
      <HStack className="px-4" space="sm">
        <AIAvatar size="md" />
        <VStack className="flex-1 bg-background-100 rounded-2xl rounded-bl-md p-4">
          <Text size="sm" className="text-typography-900 mb-2">
            Got it. 💪
          </Text>
          <Text size="sm" className="text-typography-900">
            And how many days a week do you want to work out?
          </Text>
        </VStack>
      </HStack>

      <VStack className="px-4" space="xs">
        {workoutFrequencyOptions.map((option) => (
          <Pressable
            key={option.value}
            onPress={() => handleFrequencySelect(option.label)}
            className="p-4 rounded-xl bg-background-50 border border-background-200"
          >
            <Text size="sm" className="text-typography-900">
              {option.label}
            </Text>
          </Pressable>
        ))}
      </VStack>
    </VStack>
  );

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <AIChatHeader 
        title="Build a workout plan"
        showBackButton
        onBackPress={handleBackPress}
      />
      
      <ScrollView 
        className="flex-1" 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        {currentStep === 'goal' && renderGoalSelection()}
        {currentStep === 'frequency' && renderFrequencySelection()}
      </ScrollView>

      {currentStep !== 'complete' && (
        <ChatInput
          onSendMessage={handleSendMessage}
          placeholder="Start typing..."
          showDisclaimer={false}
        />
      )}
    </SafeAreaView>
  );
};

export default WorkoutPlanScreen;
