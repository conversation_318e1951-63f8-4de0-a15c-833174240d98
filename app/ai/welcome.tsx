import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { Sparkles } from 'lucide-react-native';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { useRouter } from 'expo-router';

const AIWelcomeScreen = () => {
  const router = useRouter();

  const handleStartChatting = () => {
    router.push('/ai');
  };

  return (
    <SafeAreaView 
      className="flex-1"
      style={{ backgroundColor: '#E5F9FC' }}
    >
      <VStack className="flex-1 justify-center items-center px-6" space="lg">
        {/* AI Avatar */}
        <Box className="w-16 h-16 rounded-full overflow-hidden mb-4">
          <LinearGradient
            colors={['#9CF6FF', '#00D8E6', '#00A8CC']}
            start={{ x: 0.15, y: 0.05 }}
            end={{ x: 0.9, y: 0.9 }}
            className="w-full h-full items-center justify-center"
          >
            <Icon 
              as={Sparkles} 
              size="xl" 
              color="white" 
            />
          </LinearGradient>
        </Box>

        {/* Welcome Message */}
        <VStack className="items-center" space="md">
          <Text 
            size="2xl" 
            className="font-dm-sans-bold text-typography-900 text-center"
          >
            Hey, how can I help?
          </Text>
        </VStack>

        {/* Description */}
        <VStack className="bg-white rounded-2xl p-6 mx-4" space="sm">
          <Text 
            size="md" 
            className="font-dm-sans-bold text-typography-900"
          >
            Meet {'{{AI}}'}
          </Text>
          <Text 
            size="sm" 
            className="text-typography-600 leading-5"
          >
            Your personal guide to getting the most out of your wellness experience. Ask about class schedules, book activities, track your progress, join challenges, or get tips on staying active.
          </Text>
        </VStack>

        {/* Start Button */}
        <Box className="w-full px-4 mt-8">
          <Button
            onPress={handleStartChatting}
            className="bg-primary-500 rounded-2xl h-14"
          >
            <ButtonText 
              size="md" 
              className="font-dm-sans-medium text-white"
            >
              Start chatting
            </ButtonText>
          </Button>
        </Box>
      </VStack>
    </SafeAreaView>
  );
};

export default AIWelcomeScreen;
