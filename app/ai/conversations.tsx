import React from 'react';
import { FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { MessageCircle, Trash2 } from 'lucide-react-native';
import { AIChatHeader } from '@/components/ai/ai-chat-header';
import { useAIChat } from '@/hooks/useAIChat';
import { useAIStore } from '@/stores/ai-store';
import { Conversation } from '@/stores/ai-store';
import { useRouter } from 'expo-router';

const ConversationsScreen = () => {
  const router = useRouter();
  const { selectConversation } = useAIChat();
  const { conversations, deleteConversation } = useAIStore();

  const handleBackPress = () => {
    router.back();
  };

  const handleConversationPress = (conversation: Conversation) => {
    selectConversation(conversation);
    router.push('/ai/chat');
  };

  const handleDeleteConversation = (conversationId: string) => {
    deleteConversation(conversationId);
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const renderConversation = ({ item }: { item: Conversation }) => {
    const lastMessage = item.messages[item.messages.length - 1];
    const preview = lastMessage?.content || 'No messages yet';
    
    return (
      <Pressable
        onPress={() => handleConversationPress(item)}
        className="bg-white rounded-xl p-4 mx-4 mb-3 border border-background-200"
      >
        <HStack className="items-start justify-between">
          <HStack className="flex-1 items-start" space="sm">
            <Box 
              className="w-10 h-10 rounded-full items-center justify-center mt-1"
              style={{ backgroundColor: '#E5F9FC' }}
            >
              <Icon 
                as={MessageCircle} 
                size="sm" 
                style={{ color: '#00BFE6' }}
              />
            </Box>
            
            <VStack className="flex-1" space="xs">
              <HStack className="items-center justify-between">
                <Text 
                  size="sm" 
                  className="font-dm-sans-medium text-typography-900 flex-1"
                  numberOfLines={1}
                >
                  {item.title}
                </Text>
                <Text 
                  size="xs" 
                  className="text-typography-500 ml-2"
                >
                  {formatDate(item.updatedAt)}
                </Text>
              </HStack>
              
              <Text 
                size="xs" 
                className="text-typography-500"
                numberOfLines={2}
              >
                {preview}
              </Text>
            </VStack>
          </HStack>
          
          <Pressable
            onPress={() => handleDeleteConversation(item.id)}
            className="w-8 h-8 items-center justify-center ml-2"
          >
            <Icon 
              as={Trash2} 
              size="sm" 
              className="text-typography-400" 
            />
          </Pressable>
        </HStack>
      </Pressable>
    );
  };

  const renderEmptyState = () => (
    <VStack className="flex-1 justify-center items-center px-6" space="md">
      <Box 
        className="w-16 h-16 rounded-full items-center justify-center"
        style={{ backgroundColor: '#E5F9FC' }}
      >
        <Icon 
          as={MessageCircle} 
          size="xl" 
          style={{ color: '#00BFE6' }}
        />
      </Box>
      <VStack className="items-center" space="xs">
        <Text 
          size="md" 
          className="font-dm-sans-medium text-typography-900"
        >
          No conversations yet
        </Text>
        <Text 
          size="sm" 
          className="text-typography-500 text-center"
        >
          Start a conversation with AI to see your chat history here
        </Text>
      </VStack>
    </VStack>
  );

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <AIChatHeader 
        title="All Conversations"
        showBackButton
        onBackPress={handleBackPress}
      />
      
      {conversations.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={conversations}
          renderItem={renderConversation}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 16,
            paddingBottom: 20,
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default ConversationsScreen;
