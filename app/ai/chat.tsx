import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { AIChatHeader } from '@/components/ai/ai-chat-header';
import { ConversationView } from '@/components/ai/conversation-view';
import { ChatInput } from '@/components/ai/chat-input';
import { useAIChat } from '@/hooks/useAIChat';
import { useRouter } from 'expo-router';

const ChatScreen = () => {
  const router = useRouter();
  const {
    currentConversation,
    isTyping,
    sendMessage,
  } = useAIChat();

  const handleSendMessage = (message: string) => {
    sendMessage(message);
  };

  const handleBackPress = () => {
    router.back();
  };

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <AIChatHeader 
        title={currentConversation?.title || 'AI Chat'}
        showBackButton
        onBackPress={handleBackPress}
      />
      
      <VStack className="flex-1">
        <ConversationView
          messages={currentConversation?.messages || []}
          isTyping={isTyping}
        />
        
        <ChatInput
          onSendMessage={handleSendMessage}
          placeholder="Type your message..."
          disabled={isTyping}
        />
      </VStack>
    </SafeAreaView>
  );
};

export default ChatScreen;
