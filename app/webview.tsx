import React from 'react';
import { WebView } from 'react-native-webview';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Pressable } from '@/components/ui/pressable';

import { Box } from '@/components/ui/box';
import { BackIcon } from '@/components/shared/icon/back-icon';
import { Text } from '@/components/ui/text';

const WebviewScreen = () => {
  const { url } = useLocalSearchParams<{ url: string }>();
  const navigation = useNavigation();

  return (
    <SafeAreaView className="flex-1">
      <Box className="p-2">
        <Pressable onPress={() => navigation.goBack()}>
          <BackIcon />
        </Pressable>
      </Box>
      <WebView
        onError={() => <Text>Sorry something went wrong</Text>}
        source={{ uri: url }}
        style={{ flex: 1 }}
        className="p-10"
      />
    </SafeAreaView>
  );
};

export default WebviewScreen;
