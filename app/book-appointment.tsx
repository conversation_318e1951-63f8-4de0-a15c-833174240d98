import React, { useState } from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import {
  ArrowLeft,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useForm } from '@tanstack/react-form';
import {
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from '@/components/ui/avatar';
import { getInitials } from '@/data/common/common.utils';
import { useTrainers } from '@/data/screens/trainers/queries/useTrainers';
import { useSession } from '@/modules/login/auth-provider';
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from '@/components/ui/select';
import {
  useBookAppointment,
  useUserPasses as useBookingPasses,
} from '@/hooks/useBooking';

const BookAppointmentHeader = () => {
  return (
    <HStack className="items-center px-4 py-4" space="md">
      <Pressable
        onPress={() => router.back()}
        className="w-10 h-10 items-center justify-center"
      >
        <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
      </Pressable>

      <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
        Book appointment
      </Text>
    </HStack>
  );
};

const BookAppointment = () => {
  const { trainerId, sessionId, appointmentName } = useLocalSearchParams<{
    trainerId?: string;
    sessionId?: string;
    appointmentName?: string;
  }>();
  const { data: sessionData } = useSession();
  const { data: trainers } = useTrainers(sessionId || '');
  const { data: userPasses, isLoading: passesLoading } = useBookingPasses();
  const bookingMutation = useBookAppointment();

  // Current date for calendar
  const [currentDate, setCurrentDate] = useState(new Date());

  const form = useForm({
    defaultValues: {
      selectedTrainer: trainerId || 'anyone',
      selectedDate: '',
      selectedTime: '',
      selectedPass: '',
    },
    onSubmit: async ({ value }) => {
      console.log('Booking submitted:', value);

      // Basic validation
      if (!value.selectedDate) {
        alert('Please select a date');
        return;
      }
      if (!value.selectedTime) {
        alert('Please select a time');
        return;
      }
      if (!value.selectedPass) {
        alert('Please select a pass');
        return;
      }

      try {
        const bookingData = {
          trainerId: value.selectedTrainer,
          date: value.selectedDate,
          time: value.selectedTime,
          passId: value.selectedPass,
        };

        await bookingMutation.mutateAsync(bookingData);
        alert('Appointment booked successfully!');
        router.back();
      } catch (error) {
        console.error('Booking error:', error);
        alert('Failed to book appointment. Please try again.');
      }
    },
  });

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1);
    } else {
      newDate.setMonth(currentDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  const timeSlots = [
    '6:00AM',
    '7:00AM',
    '8:00AM',
    '9:00AM',
    '10:00AM',
    '11:00AM',
    '12:00PM',
  ];

  // Prepare trainer options including "Anyone"
  const trainerOptions = [
    { id: 'anyone', name: 'Anyone', profile_image: null },
    ...(trainers || []).map(trainer => ({
      ...trainer,
      name: `${trainer.first_name} ${trainer.last_name}`,
    })),
  ];

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <BookAppointmentHeader />

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="px-4 pb-6" space="lg">
          {/* Description */}
          <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
            Please fill out the information below
          </Text>

          {/* Service Type */}
          <VStack space="sm">
            <Text className="text-[#00BFE0] font-dm-sans-bold text-lg">
              {appointmentName || 'CUP-Personal Training'}
            </Text>
          </VStack>

          {/* Trainer Selection */}
          <VStack space="md">
            <HStack className="justify-between items-center">
              <Text className="text-base font-dm-sans-medium text-typography-900">
                Select a trainer
              </Text>
              <Pressable>
                <Text className="text-[#00BFE0] font-dm-sans-medium text-sm">
                  See all
                </Text>
              </Pressable>
            </HStack>

            <form.Field name="selectedTrainer">
              {field => (
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <HStack space="md" className="px-1">
                    {trainerOptions.slice(0, 6).map(trainer => (
                      <Pressable
                        key={trainer.id}
                        onPress={() => field.handleChange(String(trainer.id))}
                        className="items-center"
                      >
                        <VStack className="items-center" space="xs">
                          <Avatar
                            size="lg"
                            className={`border-2 ${
                              field.state.value === String(trainer.id)
                                ? 'border-[#00BFE0]'
                                : 'border-transparent'
                            }`}
                          >
                            {trainer.profile_image ? (
                              <AvatarImage
                                source={{ uri: trainer.profile_image }}
                                alt={trainer.name}
                              />
                            ) : (
                              <AvatarFallbackText className="bg-background-300 text-typography-700">
                                {trainer.id === 'anyone'
                                  ? 'A'
                                  : getInitials(trainer.name)}
                              </AvatarFallbackText>
                            )}
                          </Avatar>
                          <Text
                            className={`text-xs font-dm-sans-medium ${
                              field.state.value === String(trainer.id)
                                ? 'text-[#00BFE0]'
                                : 'text-typography-700'
                            }`}
                          >
                            {trainer.name}
                          </Text>
                        </VStack>
                      </Pressable>
                    ))}
                  </HStack>
                </ScrollView>
              )}
            </form.Field>
          </VStack>

          {/* Date Picker */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Pick a date
            </Text>

            <VStack space="sm">
              {/* Month Navigation */}
              <HStack className="justify-between items-center">
                <Text className="text-base font-dm-sans-medium text-typography-900">
                  {formatMonthYear(currentDate)}
                </Text>
                <HStack space="sm">
                  <Pressable
                    onPress={() => navigateMonth('prev')}
                    className="w-8 h-8 items-center justify-center"
                  >
                    <Icon
                      as={ChevronLeft}
                      size="sm"
                      className="text-typography-600"
                    />
                  </Pressable>
                  <Pressable
                    onPress={() => navigateMonth('next')}
                    className="w-8 h-8 items-center justify-center"
                  >
                    <Icon
                      as={ChevronRight}
                      size="sm"
                      className="text-typography-600"
                    />
                  </Pressable>
                </HStack>
              </HStack>

              {/* Calendar Header */}
              <HStack className="justify-between">
                {['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'].map(day => (
                  <Text
                    key={day}
                    className="text-xs font-dm-sans-medium text-typography-500 w-10 text-center"
                  >
                    {day}
                  </Text>
                ))}
              </HStack>

              {/* Calendar Grid */}
              <form.Field name="selectedDate">
                {field => (
                  <VStack space="xs">
                    {Array.from(
                      { length: Math.ceil(generateCalendarDays().length / 7) },
                      (_, weekIndex) => (
                        <HStack key={weekIndex} className="justify-between">
                          {generateCalendarDays()
                            .slice(weekIndex * 7, (weekIndex + 1) * 7)
                            .map((day, dayIndex) => {
                              if (!day) {
                                return (
                                  <VStack
                                    key={dayIndex}
                                    className="w-10 h-10"
                                  />
                                );
                              }

                              const dateString = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                              const isSelected =
                                field.state.value === dateString;
                              const isToday =
                                new Date().toDateString() ===
                                new Date(
                                  currentDate.getFullYear(),
                                  currentDate.getMonth(),
                                  day
                                ).toDateString();

                              return (
                                <Pressable
                                  key={dayIndex}
                                  onPress={() => field.handleChange(dateString)}
                                  className={`w-10 h-10 items-center justify-center rounded-full ${
                                    isSelected
                                      ? 'bg-[#00BFE0]'
                                      : isToday
                                        ? 'bg-[#E6F9FC]'
                                        : ''
                                  }`}
                                >
                                  <Text
                                    className={`text-sm font-dm-sans-medium ${
                                      isSelected
                                        ? 'text-white'
                                        : isToday
                                          ? 'text-[#00BFE0]'
                                          : 'text-typography-900'
                                    }`}
                                  >
                                    {day}
                                  </Text>
                                </Pressable>
                              );
                            })}
                        </HStack>
                      )
                    )}
                  </VStack>
                )}
              </form.Field>
            </VStack>
          </VStack>

          {/* Time Selection */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Select a time
            </Text>

            <form.Field name="selectedTime">
              {field => (
                <VStack space="sm">
                  <HStack space="sm" className="flex-wrap">
                    {timeSlots.slice(0, 3).map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0] bg-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                      >
                        <Text
                          className={`font-dm-sans-medium ${
                            field.state.value === time
                              ? 'text-white'
                              : 'text-typography-700'
                          }`}
                        >
                          {time}
                        </Text>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {timeSlots.slice(3, 6).map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0] bg-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                      >
                        <Text
                          className={`font-dm-sans-medium ${
                            field.state.value === time
                              ? 'text-white'
                              : 'text-typography-700'
                          }`}
                        >
                          {time}
                        </Text>
                      </Pressable>
                    ))}
                  </HStack>

                  <Pressable
                    onPress={() => field.handleChange(timeSlots[6])}
                    className={`px-4 py-3 rounded-full border self-start ${
                      field.state.value === timeSlots[6]
                        ? 'border-[#00BFE0] bg-[#00BFE0]'
                        : 'bg-background-50 border-background-300'
                    }`}
                  >
                    <Text
                      className={`font-dm-sans-medium ${
                        field.state.value === timeSlots[6]
                          ? 'text-white'
                          : 'text-typography-700'
                      }`}
                    >
                      {timeSlots[6]}
                    </Text>
                  </Pressable>
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Pass Selection */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Pass
            </Text>

            <form.Field name="selectedPass">
              {field => (
                <Select
                  selectedValue={field.state.value}
                  onValueChange={field.handleChange}
                >
                  <SelectTrigger variant="outline" size="md">
                    <SelectInput
                      placeholder="Select a pass"
                      className="text-typography-600"
                    />
                    <SelectIcon className="mr-3" as={ChevronDown} />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      {(userPasses || []).map(pass => (
                        <SelectItem
                          key={pass.id}
                          label={`${pass.name} (${pass.remaining} remaining)`}
                          value={String(pass.id)}
                        />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
              )}
            </form.Field>
          </VStack>
        </VStack>
      </ScrollView>

      {/* Submit Button */}
      <VStack className="bg-background-0 px-4 py-4 border-t border-background-200">
        <Button
          variant="solid"
          size="lg"
          className="rounded-full"
          style={{ backgroundColor: '#00BFE0' }}
          onPress={() => form.handleSubmit()}
          disabled={bookingMutation.isPending}
        >
          <ButtonText className="text-white font-dm-sans-bold text-base">
            {bookingMutation.isPending ? 'Booking...' : 'Request appointment'}
          </ButtonText>
        </Button>
      </VStack>
    </SafeAreaView>
  );
};

export default BookAppointment;
