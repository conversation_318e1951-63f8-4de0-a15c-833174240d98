import React, { useState } from 'react';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ScrollView, Platform, ImageBackground } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useSession } from '@/modules/login/auth-provider';
import { Box } from '@/components/ui/box';
import {
  ProfileField,
  DropdownField,
  ProfileAvatar,
} from '@/components/screens/profile';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useProfileForm } from '@/hooks/useProfileForm';
import { Button, ButtonText } from '@/components/ui/button';

const Profile = () => {
  const insets = useSafeAreaInsets();
  const { data: session } = useSession();
  const {
    profileData,
    updateProfileData,
    getInitials,
    getFullName,
    isLoading,
    saveProfile,
  } = useUserProfile();

  const { form } = useProfileForm();

  // Calculate bottom padding for tab bar
  const tabBarHeight = 78 + 16 + (Platform.OS === 'ios' ? insets.bottom : 16);
  const contentBottomPadding = tabBarHeight + 16;

  const handleGenderPress = () => {
    // TODO: Implement gender selection modal
    console.log('Gender selection pressed');
  };

  const handleAgePress = () => {
    // TODO: Implement age selection modal
    console.log('Age selection pressed');
  };

  const handleHeightPress = () => {
    // TODO: Implement height selection modal
    console.log('Height selection pressed');
  };

  const handleWeightPress = () => {
    // TODO: Implement weight selection modal
    console.log('Weight selection pressed');
  };

  const handleFullNameChange = (text: string) => {
    const names = text.split(' ');
    updateProfileData({
      firstName: names[0] || '',
      lastName: names.slice(1).join(' ') || '',
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white items-center">
          <Pressable onPress={() => router.back()} className="mr-4">
            <Icon as={ArrowLeft} size="lg" className="text-typography-700" />
          </Pressable>
          <Text className="text-2xl font-dm-sans-bold text-typography-900">
            My profile
          </Text>
        </HStack>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingBottom: contentBottomPadding,
          }}
        >
          {/* Profile Header with Background */}
          <Box className="relative">
            <ImageBackground
              source={{
                uri: 'https://via.placeholder.com/400x200/E5F9FC/E5F9FC',
              }}
              className="h-48 justify-center items-center"
              style={{ backgroundColor: '#E5F9FC' }}
            >
              <ProfileAvatar
                imageUri={profileData.profileImage}
                initials={getInitials()}
                onEditPress={() => console.log('Edit avatar pressed')}
              />
            </ImageBackground>
          </Box>

          {/* Profile Form */}
          <VStack className="px-4 pt-6">
            {/* Basic Information */}
            <VStack className="bg-white rounded-2xl p-4 mb-6 shadow-sm">
              <ProfileField
                label="Full name"
                value={getFullName()}
                onChangeText={handleFullNameChange}
                placeholder="Enter your full name"
              />

              <ProfileField
                label="Email"
                value={profileData.email}
                onChangeText={text => updateProfileData({ email: text })}
                placeholder="Enter your email"
              />

              <ProfileField
                label="Phone"
                value={profileData.phone || 'XXX-XXX-XXX'}
                onChangeText={text => updateProfileData({ phone: text })}
                placeholder="Enter your phone number"
              />
            </VStack>

            {/* Additional Information */}
            <VStack className="bg-white rounded-2xl p-4 mb-6 shadow-sm">
              <DropdownField
                label="Gender"
                value={profileData.gender}
                onPress={handleGenderPress}
              />

              <DropdownField
                label="Age"
                value={profileData.age}
                onPress={handleAgePress}
              />

              <DropdownField
                label="Height"
                value={profileData.height}
                onPress={handleHeightPress}
              />

              <DropdownField
                label="Weight"
                value={profileData.weight}
                onPress={handleWeightPress}
              />
            </VStack>

            {/* Save Button */}
            <VStack className="px-4 mt-6">
              <Button
                onPress={() => form.handleSubmit()}
                className="w-full bg-primary-500 rounded-full"
              >
                <ButtonText className="text-white font-dm-sans-medium">
                  Save Changes
                </ButtonText>
              </Button>
            </VStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default Profile;
