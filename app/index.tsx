import { useSession } from '@/modules/login/auth-provider';
import { Redirect } from 'expo-router';
import { Text, View } from 'react-native';
import { useEffect, useState } from 'react';

const IndexScreen = () => {
  const { data, isStarting, showTerms, biometricState, signInWithBiometric } =
    useSession();
  const [checkingBiometric, setCheckingBiometric] = useState(false);

  useEffect(() => {
    // Auto-authenticate with biometrics if enabled and no user is logged in
    const tryBiometricAuth = async () => {
      if (
        !data &&
        !isStarting &&
        biometricState.isSupported &&
        biometricState.isEnrolled &&
        biometricState.isEnabled &&
        !checkingBiometric
      ) {
        setCheckingBiometric(true);
        try {
          await signInWithBiometric();
        } catch {
          // Handle error silently or implement user notification here if needed
        } finally {
          setCheckingBiometric(false);
        }
      }
    };

    tryBiometricAuth();
  }, [
    data,
    isStarting,
    biometricState,
    signInWithBiometric,
    checkingBiometric,
  ]);

  if (isStarting || checkingBiometric) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading....</Text>
      </View>
    );
  }

  if (data && showTerms) return <Redirect href="/terms" />;

  if (data) return <Redirect href="/(tabs)/(home)" />;

  return <Redirect href="/(auth)/sign-in" />;
};

IndexScreen.displayName = 'IndexScreen';

export default IndexScreen;
