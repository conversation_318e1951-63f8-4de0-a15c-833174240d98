# Upace Mobile v2

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
```

## Code Quality & Formatting

This project uses Prettier and ESLint to maintain consistent code quality and formatting.

### Available Commands

- **`npm run format`** - Format all files with Prettier
- **`npm run format:check`** - Check formatting without making changes
- **`npm run lint`** - Run ESLint to check code quality
- **`npm run lint:fix`** - Auto-fix ESLint issues where possible
- **`npm run format:fix`** - Format and fix all issues in one command

### Pre-commit Hooks

The project includes pre-commit hooks that automatically:
- Format code with Prettier before each commit
- Run ESLint with auto-fix before each commit
- Prevent commits with formatting or linting issues

**Setup**: The hooks are automatically configured when you run `npm install`

### VS Code Setup

The project includes VS Code workspace settings that automatically:
- Format code on save using Prettier
- Fix ESLint issues on save
- Use Prettier as the default formatter

**Recommended Extensions:**
- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense
- TypeScript Importer

### Configuration Files

- **`.prettierrc`** - Prettier formatting rules
- **`.prettierignore`** - Files to exclude from formatting
- **`.vscode/settings.json`** - VS Code workspace settings
- **`.vscode/extensions.json`** - Recommended extensions
- **`eslint.config.mjs`** - ESLint configuration with Prettier integration

### Code Style

- **Quotes**: Single quotes preferred
- **Line Length**: 80 characters
- **Indentation**: 2 spaces
- **Semicolons**: Required
- **Trailing Commas**: ES5 style
