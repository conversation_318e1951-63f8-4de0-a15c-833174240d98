import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { useUserPasses, useCanBookTrainer } from '@/hooks/useUserPasses';
import { useSession } from '@/modules/login/auth-provider';

export const UserPassesTest = () => {
  const { data: session } = useSession();
  const { hasAvailablePasses, totalPasses, memberships } = useUserPasses();
  const { canBook, reason } = useCanBookTrainer();

  return (
    <VStack space="md" className="p-4 border border-gray-300 rounded-lg">
      <Text className="text-lg font-dm-sans-bold">User Passes Test</Text>
      
      <VStack space="sm">
        <Text className="text-sm font-dm-sans-medium">Session Info:</Text>
        <Text className="text-xs text-gray-600">
          User ID: {session?.id || 'Not logged in'}
        </Text>
        <Text className="text-xs text-gray-600">
          Name: {session?.first_name} {session?.last_name}
        </Text>
      </VStack>

      <VStack space="sm">
        <Text className="text-sm font-dm-sans-medium">Pass Information:</Text>
        <Text className="text-xs text-gray-600">
          Has Available Passes: {hasAvailablePasses ? 'Yes' : 'No'}
        </Text>
        <Text className="text-xs text-gray-600">
          Total Passes: {totalPasses}
        </Text>
        <Text className="text-xs text-gray-600">
          Memberships: {JSON.stringify(memberships)}
        </Text>
      </VStack>

      <VStack space="sm">
        <Text className="text-sm font-dm-sans-medium">Booking Eligibility:</Text>
        <Text className="text-xs text-gray-600">
          Can Book Trainer: {canBook ? 'Yes' : 'No'}
        </Text>
        {reason && (
          <Text className="text-xs text-red-600">
            Reason: {reason}
          </Text>
        )}
      </VStack>

      <VStack space="sm">
        <Text className="text-sm font-dm-sans-medium">Expected Button Text:</Text>
        <Button
          variant="solid"
          size="sm"
          className="rounded-full bg-[#E6F9FC] px-4 py-2"
          disabled
        >
          <ButtonText className="text-[#00697B] font-dm-sans-medium text-xs">
            {canBook ? 'Book' : 'Request Info'}
          </ButtonText>
        </Button>
      </VStack>
    </VStack>
  );
};
