import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useMockBookAppointment } from '@/data/screens/appointments/mutations/useBookAppointment';
import { useAppointmentsQuery } from '@/data/screens/appointments/queries/useAppointmentsQuery';
import { formatDate } from '@/data/common/common.utils';

export const AppointmentTest = () => {
  const { mutate: bookAppointment, isPending } = useMockBookAppointment(() => {
    console.log('Appointment booked successfully!');
  });

  const {
    data: appointments,
    isLoading,
    refetch,
  } = useAppointmentsQuery({
    date: formatDate(new Date()),
  });

  const handleBookAppointment = () => {
    bookAppointment({
      appointment_type_id: 1,
      trainer_id: 1,
      date: new Date().toISOString(),
      time: '10:00',
      duration: 60,
      notes: 'Test appointment booking',
    });
  };

  const handleForceError = () => {
    // This will trigger an error since we're using a non-existent appointment type
    bookAppointment({
      appointment_type_id: 999,
      trainer_id: 999,
      date: new Date().toISOString(),
      time: '25:00', // Invalid time to force error
      duration: 60,
      notes: 'This should fail',
    });
  };

  return (
    <VStack space="md" className="p-4">
      <Text className="text-lg font-bold">Appointment System Test</Text>

      <VStack space="sm">
        <Text className="text-sm text-gray-600">
          Appointments loaded: {appointments?.length || 0}
        </Text>
        <Text className="text-sm text-gray-600">
          Loading: {isLoading ? 'Yes' : 'No'}
        </Text>
      </VStack>

      <Button onPress={handleBookAppointment} disabled={isPending}>
        <ButtonText>
          {isPending ? 'Booking...' : 'Test Book Appointment'}
        </ButtonText>
      </Button>

      <Button onPress={handleForceError} disabled={isPending} variant="outline">
        <ButtonText>Test Appointment Error</ButtonText>
      </Button>

      <Button onPress={() => refetch()} variant="outline">
        <ButtonText>Refresh Appointments</ButtonText>
      </Button>
    </VStack>
  );
};
