import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { AIAvatar } from './ai-avatar';

export const TypingIndicator: React.FC = () => {
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createDotAnimation = (animValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animValue, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animation = Animated.parallel([
      createDotAnimation(dot1Anim, 0),
      createDotAnimation(dot2Anim, 200),
      createDotAnimation(dot3Anim, 400),
    ]);

    animation.start();

    return () => animation.stop();
  }, [dot1Anim, dot2Anim, dot3Anim]);

  const getDotStyle = (animValue: Animated.Value) => ({
    opacity: animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 1],
    }),
    transform: [
      {
        scale: animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1.2],
        }),
      },
    ],
  });

  return (
    <HStack className="px-4 py-2 justify-start" space="sm">
      <AIAvatar size="sm" />
      
      <Box className="bg-background-100 rounded-2xl rounded-bl-md px-4 py-3">
        <HStack className="items-center" space="xs">
          <Animated.View
            style={[
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: '#9CA3AF',
              },
              getDotStyle(dot1Anim),
            ]}
          />
          <Animated.View
            style={[
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: '#9CA3AF',
              },
              getDotStyle(dot2Anim),
            ]}
          />
          <Animated.View
            style={[
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: '#9CA3AF',
              },
              getDotStyle(dot3Anim),
            ]}
          />
        </HStack>
      </Box>
    </HStack>
  );
};
