import React, { useRef } from 'react';
import { Animated, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';

interface AnimatedButtonProps {
  onPress: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  onPress,
  children,
  className = '',
  disabled = false
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (!disabled) {
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (!disabled) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  return (
    <Pressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}
    >
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
        }}
        className={className}
      >
        {children}
      </Animated.View>
    </Pressable>
  );
};
