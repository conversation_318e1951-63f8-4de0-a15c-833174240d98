import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { Box } from '@/components/ui/box';
import { ChevronRight, Dumbbell, User, Calendar } from 'lucide-react-native';
import { PlanOption } from '@/stores/ai-store';
import { useRouter } from 'expo-router';

interface PlanWithAIProps {
  planOptions: PlanOption[];
  onPlanPress: (option: PlanOption) => void;
}

const getIconComponent = (iconName: string) => {
  switch (iconName) {
    case 'dumbbell':
      return Dumbbell;
    case 'user':
      return User;
    case 'calendar':
      return Calendar;
    default:
      return Dumbbell;
  }
};

const getIconColor = (iconName: string) => {
  switch (iconName) {
    case 'dumbbell':
      return '#00BFE6'; // Primary cyan
    case 'user':
      return '#00BFE6'; // Primary cyan
    case 'calendar':
      return '#00BFE6'; // Primary cyan
    default:
      return '#00BFE6';
  }
};

export const PlanWithAI: React.FC<PlanWithAIProps> = ({
  planOptions,
  onPlanPress
}) => {
  const router = useRouter();

  const handlePlanPress = (option: PlanOption) => {
    onPlanPress(option);
    if (option.route) {
      router.push(option.route as any);
    }
  };

  return (
    <VStack className="px-4" space="sm">
      <Text size="md" className="font-dm-sans-medium text-typography-900">
        Plan with AI
      </Text>
      
      <VStack space="xs">
        {planOptions.map((option) => {
          const IconComponent = getIconComponent(option.icon);
          const iconColor = getIconColor(option.icon);
          
          return (
            <Pressable
              key={option.id}
              onPress={() => handlePlanPress(option)}
              className="bg-white rounded-xl p-4 border border-background-200"
            >
              <HStack className="items-center" space="md">
                <Box 
                  className="w-10 h-10 rounded-full items-center justify-center"
                  style={{ backgroundColor: '#E5F9FC' }}
                >
                  <Icon 
                    as={IconComponent} 
                    size="md" 
                    style={{ color: iconColor }}
                  />
                </Box>
                
                <Text 
                  size="sm" 
                  className="text-typography-900 font-dm-sans-medium flex-1"
                >
                  {option.title}
                </Text>
                
                <Icon 
                  as={ChevronRight} 
                  size="sm" 
                  className="text-typography-500" 
                />
              </HStack>
            </Pressable>
          );
        })}
      </VStack>
    </VStack>
  );
};
