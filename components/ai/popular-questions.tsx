import React, { useState } from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { ChevronRight } from 'lucide-react-native';
import { AnimatedButton } from './animated-button';
import { PopularQuestion } from '@/stores/ai-store';

interface PopularQuestionsProps {
  questions: PopularQuestion[];
  onQuestionPress: (question: PopularQuestion) => void;
  showMore?: boolean;
  onShowMorePress?: () => void;
}

export const PopularQuestions: React.FC<PopularQuestionsProps> = ({
  questions,
  onQuestionPress,
  showMore = true,
  onShowMorePress,
}) => {
  const [expandedQuestions, setExpandedQuestions] = useState<string[]>([]);

  const toggleQuestion = (questionId: string) => {
    setExpandedQuestions(prev =>
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const handleQuestionPress = (question: PopularQuestion) => {
    onQuestionPress(question);
  };

  return (
    <VStack className="px-4" space="sm">
      <Text size="md" className="font-dm-sans-medium text-typography-900">
        Popular questions
      </Text>

      <VStack space="xs">
        {questions.map(question => (
          <AnimatedButton
            key={question.id}
            onPress={() => handleQuestionPress(question)}
            className="bg-white rounded-xl p-4 border border-background-200"
          >
            <HStack className="items-center justify-between">
              <Text
                size="sm"
                className="text-typography-700 flex-1 mr-2"
                numberOfLines={2}
              >
                {question.question}
              </Text>
              <Icon
                as={ChevronRight}
                size="sm"
                className="text-typography-500"
              />
            </HStack>
          </AnimatedButton>
        ))}
      </VStack>

      {showMore && onShowMorePress && (
        <Pressable onPress={onShowMorePress} className="py-2">
          <Text size="sm" className="text-primary-500 font-dm-sans-medium">
            Show more
          </Text>
        </Pressable>
      )}
    </VStack>
  );
};
