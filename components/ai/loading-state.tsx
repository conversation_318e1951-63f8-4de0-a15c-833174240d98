import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { AIAvatar } from './ai-avatar';

interface LoadingStateProps {
  message?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = "AI is thinking..."
}) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 0.7,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [pulseAnim]);

  return (
    <VStack className="items-center py-8" space="md">
      <Animated.View
        style={{
          opacity: pulseAnim,
        }}
      >
        <AIAvatar size="lg" />
      </Animated.View>
      
      <Text 
        size="sm" 
        className="text-typography-500 text-center"
      >
        {message}
      </Text>
    </VStack>
  );
};
