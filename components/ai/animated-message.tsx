import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import { MessageBubble } from './message-bubble';
import { Message } from '@/stores/ai-store';

interface AnimatedMessageProps {
  message: Message;
  showAvatar?: boolean;
  delay?: number;
}

export const AnimatedMessage: React.FC<AnimatedMessageProps> = ({
  message,
  showAvatar = true,
  delay = 0
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    const animation = Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
    ]);

    animation.start();
  }, [fadeAnim, slideAnim, delay]);

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <MessageBubble message={message} showAvatar={showAvatar} />
    </Animated.View>
  );
};
