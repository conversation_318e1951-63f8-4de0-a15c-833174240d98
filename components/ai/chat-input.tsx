import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Input, InputField } from '@/components/ui/input';
import { Icon } from '@/components/ui/icon';
import { Box } from '@/components/ui/box';
import { Send, Sparkles } from 'lucide-react-native';
import { useForm } from '@tanstack/react-form';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  showDisclaimer?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  placeholder = 'Start typing...',
  disabled = false,
  showDisclaimer = true,
}) => {
  const insets = useSafeAreaInsets();

  const form = useForm({
    defaultValues: {
      message: '',
    },
    onSubmit: async ({ value }) => {
      if (value.message.trim()) {
        onSendMessage(value.message.trim());
        form.reset();
      }
    },
  });

  const handleSend = () => {
    form.handleSubmit();
  };

  return (
    <VStack
      className="bg-background-0 border-t border-background-200 px-4 pt-3"
      style={{ paddingBottom: Math.max(insets.bottom, 16) }}
    >
      {showDisclaimer && (
        <Text size="xs" className="text-typography-500 text-center mb-3">
          Answers may contain inaccurate data. Learn more
        </Text>
      )}

      <form.Field name="message">
        {field => (
          <HStack className="items-end" space="sm">
            <Box className="flex-1">
              <Input
                variant="outline"
                size="md"
                className="bg-background-50 rounded-2xl border border-background-200 px-4 py-3"
              >
                <Icon
                  as={Sparkles}
                  size="md"
                  className="text-typography-400 mr-2"
                />
                <InputField
                  value={field.state.value}
                  onChangeText={field.handleChange}
                  placeholder={placeholder}
                  multiline
                  maxLength={500}
                  className="flex-1 text-sm text-typography-900 min-h-[20px] max-h-[100px]"
                  style={{
                    textAlignVertical: 'center',
                    paddingVertical: 0,
                  }}
                  editable={!disabled}
                />
              </Input>
            </Box>

            <Pressable
              onPress={handleSend}
              disabled={disabled || !field.state.value.trim()}
              className={`w-10 h-10 rounded-full items-center justify-center ${
                disabled || !field.state.value.trim()
                  ? 'bg-background-200'
                  : 'bg-primary-500'
              }`}
            >
              <Icon
                as={Send}
                size="sm"
                className={
                  disabled || !field.state.value.trim()
                    ? 'text-typography-400'
                    : 'text-white'
                }
              />
            </Pressable>
          </HStack>
        )}
      </form.Field>
    </VStack>
  );
};
