import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { useRouter } from 'expo-router';

interface AIChatHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
}

export const AIChatHeader: React.FC<AIChatHeaderProps> = ({
  title,
  showBackButton = false,
  onBackPress
}) => {
  const router = useRouter();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <VStack className="bg-background-0 pt-2 pb-4">
      <HStack className="items-center justify-between px-4">
        {showBackButton ? (
          <Pressable
            onPress={handleBackPress}
            className="w-10 h-10 items-center justify-center rounded-full"
          >
            <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
          </Pressable>
        ) : (
          <VStack className="w-10" />
        )}
        
        <Text 
          size="lg" 
          className="font-dm-sans-bold text-typography-900 text-center flex-1"
        >
          {title}
        </Text>
        
        <VStack className="w-10" />
      </HStack>
    </VStack>
  );
};
