import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { AIAvatar } from './ai-avatar';
import { Message } from '@/stores/ai-store';

interface MessageBubbleProps {
  message: Message;
  showAvatar?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  showAvatar = true
}) => {
  const isUser = message.role === 'user';
  const isSystem = message.role === 'system';

  if (isSystem) {
    return (
      <VStack className="items-center py-2">
        <Text size="xs" className="text-typography-500 text-center">
          {message.content}
        </Text>
      </VStack>
    );
  }

  return (
    <HStack 
      className={`px-4 py-2 ${isUser ? 'justify-end' : 'justify-start'}`}
      space="sm"
    >
      {!isUser && showAvatar && (
        <AIAvatar size="sm" />
      )}
      
      <Box 
        className={`max-w-[80%] rounded-2xl px-4 py-3 ${
          isUser 
            ? 'bg-primary-500 rounded-br-md' 
            : 'bg-background-100 rounded-bl-md'
        }`}
      >
        <Text 
          size="sm" 
          className={`${
            isUser 
              ? 'text-white' 
              : 'text-typography-900'
          }`}
        >
          {message.content}
        </Text>
      </Box>
      
      {isUser && showAvatar && (
        <Box className="w-8 h-8 rounded-full bg-primary-100 items-center justify-center">
          <Text size="xs" className="text-primary-700 font-dm-sans-medium">
            U
          </Text>
        </Box>
      )}
    </HStack>
  );
};
