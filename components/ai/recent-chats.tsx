import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { MessageCircle } from 'lucide-react-native';
import { Conversation } from '@/stores/ai-store';

interface RecentChatsProps {
  conversations: Conversation[];
  onChatPress: (conversation: Conversation) => void;
  onViewAllPress?: () => void;
}

const EmptyState: React.FC = () => (
  <VStack className="items-center py-8" space="sm">
    <Box 
      className="w-16 h-16 rounded-full items-center justify-center"
      style={{ backgroundColor: '#E5F9FC' }}
    >
      <Icon 
        as={MessageCircle} 
        size="xl" 
        style={{ color: '#00BFE6' }}
      />
    </Box>
    <VStack className="items-center" space="xs">
      <Text 
        size="md" 
        className="font-dm-sans-medium text-typography-900"
      >
        No recent chats yet
      </Text>
      <Text 
        size="sm" 
        className="text-typography-500 text-center"
      >
        Ask me anything to get started!
      </Text>
    </VStack>
  </VStack>
);

export const RecentChats: React.FC<RecentChatsProps> = ({
  conversations,
  onChatPress,
  onViewAllPress
}) => {
  const hasConversations = conversations.length > 0;
  const displayConversations = conversations.slice(0, 3); // Show max 3 recent chats

  return (
    <VStack className="px-4" space="sm">
      <HStack className="items-center justify-between">
        <Text size="md" className="font-dm-sans-medium text-typography-900">
          Recent chats
        </Text>
        {hasConversations && onViewAllPress && (
          <Pressable onPress={onViewAllPress}>
            <Text 
              size="sm" 
              className="text-primary-500 font-dm-sans-medium"
            >
              View all
            </Text>
          </Pressable>
        )}
      </HStack>
      
      {!hasConversations ? (
        <EmptyState />
      ) : (
        <VStack space="xs">
          {displayConversations.map((conversation) => {
            const lastMessage = conversation.messages[conversation.messages.length - 1];
            const preview = lastMessage?.content || 'No messages yet';
            
            return (
              <Pressable
                key={conversation.id}
                onPress={() => onChatPress(conversation)}
                className="bg-white rounded-xl p-4 border border-background-200"
              >
                <VStack space="xs">
                  <Text 
                    size="sm" 
                    className="font-dm-sans-medium text-typography-900"
                    numberOfLines={1}
                  >
                    {conversation.title}
                  </Text>
                  <Text 
                    size="xs" 
                    className="text-typography-500"
                    numberOfLines={2}
                  >
                    {preview}
                  </Text>
                </VStack>
              </Pressable>
            );
          })}
        </VStack>
      )}
    </VStack>
  );
};
