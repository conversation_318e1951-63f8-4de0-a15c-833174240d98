import React from 'react';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { Sparkles } from 'lucide-react-native';

interface AIAvatarProps {
  size?: 'sm' | 'md' | 'lg';
}

export const AIAvatar: React.FC<AIAvatarProps> = ({ size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10', 
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 'xs' as const,
    md: 'sm' as const,
    lg: 'md' as const
  };

  return (
    <Box className={`${sizeClasses[size]} rounded-full overflow-hidden`}>
      <LinearGradient
        colors={['#9CF6FF', '#00D8E6', '#00A8CC']}
        start={{ x: 0.15, y: 0.05 }}
        end={{ x: 0.9, y: 0.9 }}
        className="w-full h-full items-center justify-center"
      >
        <Icon 
          as={Sparkles} 
          size={iconSizes[size]} 
          color="white" 
        />
      </LinearGradient>
    </Box>
  );
};
