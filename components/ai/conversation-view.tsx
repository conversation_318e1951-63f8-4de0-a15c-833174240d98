import React, { useEffect, useRef } from 'react';
import { ScrollView, FlatList } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { AnimatedMessage } from './animated-message';
import { TypingIndicator } from './typing-indicator';
import { LoadingState } from './loading-state';
import { Message } from '@/stores/ai-store';

interface ConversationViewProps {
  messages: Message[];
  isTyping?: boolean;
  showScrollToBottom?: boolean;
}

export const ConversationView: React.FC<ConversationViewProps> = ({
  messages,
  isTyping = false,
  showScrollToBottom = true,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive or typing starts
    if (showScrollToBottom) {
      setTimeout(() => {
        if (messages.length > 0) {
          flatListRef.current?.scrollToEnd({ animated: true });
        }
      }, 100);
    }
  }, [messages.length, isTyping, showScrollToBottom]);

  const renderMessage = ({ item, index }: { item: Message; index: number }) => {
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const showAvatar = !previousMessage || previousMessage.role !== item.role;

    return (
      <AnimatedMessage
        message={item}
        showAvatar={showAvatar}
        delay={index * 50} // Stagger animation
      />
    );
  };

  const renderFooter = () => {
    if (isTyping) {
      return <TypingIndicator />;
    }
    return null;
  };

  if (messages.length === 0 && !isTyping) {
    return (
      <VStack className="flex-1 justify-center items-center px-4">
        {/* Empty state can be added here if needed */}
      </VStack>
    );
  }

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderMessage}
      keyExtractor={item => item.id}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingVertical: 8,
        flexGrow: 1,
      }}
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
        autoscrollToTopThreshold: 10,
      }}
    />
  );
};
