'use client';
import React from 'react';
import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { cssInterop } from 'nativewind';

cssInterop(ExpoLinearGradient, {
  className: 'style',
});

const linearGradientStyle = tva({
  base: '',
});

export const LinearGradient = React.forwardRef<
  React.ElementRef<typeof ExpoLinearGradient>,
  Omit<React.ComponentPropsWithoutRef<typeof ExpoLinearGradient>, 'style'> & {
    className?: string;
  }
>(({ className, ...props }, ref) => {
  return (
    <ExpoLinearGradient
      {...props}
      className={linearGradientStyle({ class: className })}
      ref={ref}
    />
  );
});
