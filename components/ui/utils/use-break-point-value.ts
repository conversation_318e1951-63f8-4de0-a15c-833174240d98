import { Dimensions, useWindowDimensions } from 'react-native';
import { useEffect, useState } from 'react';

import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from 'tailwind.config';

const TailwindTheme = resolveConfig(
  tailwindConfig as unknown as Record<string, unknown>
);
const screenSize = (
  TailwindTheme as { theme: { screens: Record<string, string | number> } }
).theme.screens;

type breakpoints = keyof typeof screenSize | 'default';

type BreakPointValue<T = unknown> = Partial<Record<breakpoints, T>> | T;

const resolveScreenWidth: Record<string, number> = {
  default: 0,
};

Object.entries(screenSize).forEach(([key, value]) => {
  if (typeof value === 'string') {
    resolveScreenWidth[key] = parseInt(value.replace('px', ''), 10);
  }
});

export const getBreakPointValue = <T>(
  values: BreakPointValue<T>,
  width: number
): T | BreakPointValue<T> => {
  if (typeof values !== 'object') return values;

  let finalBreakPointResolvedValue: T | undefined;
  const mediaQueriesBreakpoints: Array<{
    key: string;
    breakpoint: number;
    isValid: boolean;
    value?: T;
  }> = [
    {
      key: 'default',
      breakpoint: 0,
      isValid: true,
    },
  ];
  Object.keys(resolveScreenWidth).forEach(key => {
    const isValid = isValidBreakpoint(resolveScreenWidth[key], width);

    mediaQueriesBreakpoints.push({
      key,
      breakpoint: resolveScreenWidth[key],
      isValid,
    });
  });

  mediaQueriesBreakpoints.sort((a, b) => a.breakpoint - b.breakpoint);

  mediaQueriesBreakpoints.forEach((breakpoint, index) => {
    const key = breakpoint.key as keyof typeof values;
    const previous =
      mediaQueriesBreakpoints[index - 1]?.value ??
      mediaQueriesBreakpoints[0]?.value;
     
    // @ts-expect-error index access on union type
    const candidate: T | undefined = (
      typeof values === 'object' && values && key in values
        ? (values as Record<string, T>)[String(key)]
        : previous
    ) as T | undefined;
    breakpoint.value = candidate;
  });

  const lastValidObject = getLastValidObject(mediaQueriesBreakpoints);

  if (!lastValidObject) {
    return values;
  } else {
    finalBreakPointResolvedValue = lastValidObject?.value;
  }
  return finalBreakPointResolvedValue as T;
};

export function useBreakpointValue<T>(
  values: BreakPointValue<T>
): T | BreakPointValue<T> {
  const { width } = useWindowDimensions();

  const [currentBreakPointValue, setCurrentBreakPointValue] = useState<
    T | BreakPointValue<T>
  >(getBreakPointValue<T>(values, width));

  useEffect(() => {
    if (typeof values === 'object') {
      const finalBreakPointResolvedValue = getBreakPointValue<T>(values, width);
      setCurrentBreakPointValue(finalBreakPointResolvedValue);
    }
  }, [values, width]);

  if (typeof values !== 'object') return values;

  return currentBreakPointValue;
}

export function isValidBreakpoint(
  breakPointWidth: number,
  width: number = Dimensions.get('window')?.width
) {
  const windowWidth = width;

  if (windowWidth >= breakPointWidth) {
    return true;
  }
  return false;
}

function getLastValidObject<T>(
  mediaQueries: Array<{ isValid: boolean; value?: T }>
) {
  for (let i = mediaQueries.length - 1; i >= 0; i--) {
    if (mediaQueries[i].isValid) {
      return mediaQueries[i];
    }
  }
  return null; // No valid object found
}
