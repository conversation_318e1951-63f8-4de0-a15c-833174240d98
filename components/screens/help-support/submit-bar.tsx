import React from 'react';
import { View } from 'react-native';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>pinner, ButtonText } from '@/components/ui/button';

type Props = {
  disabled: boolean;
  isSubmitting: boolean;
  onSubmit: () => void;
};

export const SubmitBar = ({ disabled, isSubmitting, onSubmit }: Props) => {
  const isDisabled = disabled || isSubmitting;
  return (
    <View className="bg-background-0 px-4 py-4 border-t border-background-200">
      <Button
        variant="solid"
        size="lg"
        className={`rounded-full ${isDisabled ? 'bg-background-200' : ''}`}
        style={!isDisabled ? { backgroundColor: '#00BFE0' } : undefined}
        disabled={isDisabled}
        onPress={onSubmit}
      >
        <ButtonText className="text-white font-dm-sans-bold text-base">
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </ButtonText>
        {isSubmitting && <ButtonSpinner color="white" className="ml-2" />}
      </Button>
    </View>
  );
};

export default SubmitBar;


