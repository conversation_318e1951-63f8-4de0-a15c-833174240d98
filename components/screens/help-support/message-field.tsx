import React from 'react';
import { Textarea, TextareaInput } from '@/components/ui/textarea';

type Props = {
  value: string;
  onChange: (val: string) => void;
};

export const MessageField = ({ value, onChange }: Props) => {
  return (
    <Textarea className="bg-background-50 border border-outline-200 rounded-xl min-h-[140px] p-2">
      <TextareaInput
        placeholder="Enter your message here"
        multiline
        numberOfLines={6}
        value={value}
        onChangeText={text => onChange(text)}
      />
    </Textarea>
  );
};

export default MessageField;


