import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';


export const HelpSupportHeader = () => {
  return (
    <VStack className="bg-white">
      <HStack className="items-center px-4 py-4" space="md">
        <Pressable onPress={() => router.back()} className="w-10 h-10 items-center justify-center">
          <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
        </Pressable>
        <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">Help & support</Text>
      </HStack>

    </VStack>
  );
};

export default HelpSupportHeader;


