import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { useSubmitSupport } from '@/data/screens/help-support/queries/useSupport';
import { ToastManager } from '@/components/shared/toast/ToastManager';

const formSchema = z.object({
  supportTypeId: z.string().min(1, 'Select a support type'),
  message: z.string().trim().min(1, 'Message is required'),
});

export const useHelpSupportForm = () => {
  const submitSupport = useSubmitSupport();

  const form = useForm({
    defaultValues: {
      supportTypeId: '' as string,
      message: '' as string,
    },
    onSubmit: async ({ value }) => {
      const resp = await submitSupport.mutateAsync({
        support_type_id: Number(value.supportTypeId),
        message: value.message,
      });
      if (resp?.success) {
        form.reset()
        ToastManager.show('Thanks! Your message has been sent.', 'success');
      }
    },
    validators: {
      onChange: formSchema,
    }
  });

  return {
    form,
    isSubmitting: submitSupport.isPending,
    onSubmit: () => form.handleSubmit(),
  };
};

export default useHelpSupportForm;


