import { Safe<PERSON>reaView } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import HelpSupportHeader from './header';
import SupportTypeSelect from './support-type-select';
import MessageField from './message-field';
import SubmitBar from './submit-bar';
import { useSupportTypes } from '@/data/screens/help-support/queries/useSupport';
import useHelpSupportForm from './useHelpSupportForm';
 


export const HelpSupportScreen = () => {
  const { data: supportTypes = [], isLoading: isLoadingTypes } = useSupportTypes();
  const { form, onSubmit } = useHelpSupportForm();

 

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <HelpSupportHeader />
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="px-4 pt-3 pb-6" space="lg">
          <VStack className="bg-white rounded-xl p-4 border border-background-200" space="xs">
            <Text className="text-typography-900 font-dm-sans-medium">Have a comment or suggestion for us?</Text>
            <Text className="text-typography-600">Use the form below.</Text>
          </VStack>

          <VStack space="sm">
            <form.Field name="supportTypeId">
              {field => (
                <SupportTypeSelect
                  supportTypes={supportTypes}
                  isLoading={isLoadingTypes}
                  value={field.state.value}
                  onChange={field.handleChange}
                />
              )}
            </form.Field>
          </VStack>

          <VStack space="sm">
            <form.Field name="message">
              {field => <MessageField value={field.state.value} onChange={field.handleChange} />}
            </form.Field>
          </VStack>
        </VStack>
      </ScrollView>

      <form.Subscribe selector={s => ({ values: s.values, isSubmitting: s.isSubmitting })}>
        {({ values, isSubmitting: submitting }) => {
          const disabled = !(values.supportTypeId && values.message?.trim().length > 0);
          return (
            <SubmitBar disabled={disabled} isSubmitting={submitting} onSubmit={onSubmit} />
          );
        }}
      </form.Subscribe>
    </SafeAreaView>
  );
};

export default HelpSupportScreen;


