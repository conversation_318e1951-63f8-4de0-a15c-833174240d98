import React from 'react';
import { Icon } from '@/components/ui/icon';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '@/components/ui/select';
import { Text } from '@/components/ui/text';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import type { SupportType } from '@/data/api-client/support';

type Props = {
  supportTypes: SupportType[];
  isLoading: boolean;
  value: string;
  onChange: (val: string) => void;
};

export const SupportTypeSelect = ({ supportTypes, isLoading, value, onChange }: Props) => {
  const selected = supportTypes.find(st => String(st.id) === value)?.name ?? '';

  return (
    <>
      <Text className="text-typography-700 font-dm-sans-medium text-base">Support type</Text>
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14">
          <SelectInput
            placeholder={isLoading ? 'Loading...' : 'Choose a support type'}
            className="text-typography-700 flex-1 text-base"
            value={selected}
          />
          <Icon as={() => <ArrowDown2 size={20} color="#9CA3AF" />} />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView className="max-h-[400px]">
              {supportTypes.map(st => (
                <SelectItem key={String(st.id)} label={st.name} value={String(st.id)} />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </>
  );
};

export default SupportTypeSelect;


