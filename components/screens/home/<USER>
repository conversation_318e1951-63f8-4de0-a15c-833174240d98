import React, { useMemo } from 'react';
import { View } from '@/components/ui/view';
import { ImageBackground } from '@/components/ui/image-background';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { obtainDateFrame } from '@/data/common/common.utils';
import {
  Clock,
  Users,
  MapPin,
  Timer,
  Flame,
  User as UserIcon,
} from 'lucide-react-native';
import type { HomeExperience } from '@/data/screens/home/<USER>';

export const HomeExperienceCard = ({ data }: { data: HomeExperience }) => {
  const durationHours = useMemo(() => {
    try {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      const diffMs = Math.max(0, end.getTime() - start.getTime());
      const hours = diffMs / (1000 * 60 * 60);
      return hours >= 1
        ? `${hours.toFixed(hours % 1 ? 1 : 0)}h`
        : `${Math.round(hours * 60)}m`;
    } catch {
      return undefined;
    }
  }, [data.startTime, data.endTime]);

  return (
    <View className="w-[312px] h-[172px] rounded-2xl overflow-hidden bg-background-200 mr-3">
      <ImageBackground
        source={{ uri: data.imageUrl }}
        className="w-full h-full"
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.2)', 'rgba(0,0,0,0.7)']}
          start={[0, 0]}
          end={[0, 1]}
          className="absolute inset-0"
        />

        <VStack className="absolute inset-0 p-4 justify-end" space="xs">
          {/* Title */}
          <Text
            className="text-white text-[14px] font-dm-sans-bold"
            numberOfLines={1}
          >
            {data.title}
          </Text>

          {/* Meta row 1: intensity • time • spots */}
          <HStack className="items-center" space="sm">
            {data.intensity && (
              <HStack className="items-center" space="xs">
                <Icon as={Flame} size="xs" className="text-white/90" />
                <Text className="text-white/90 text-[11px]">
                  {data.intensity}
                </Text>
              </HStack>
            )}
            <Text className="text-white/50 text-[11px]">•</Text>
            <HStack className="items-center" space="xs">
              <Icon as={Clock} size="xs" className="text-white/90" />
              <Text className="text-white/90 text-[11px]">
                {obtainDateFrame(data.startTime, data.endTime)}
              </Text>
            </HStack>
            <Text className="text-white/50 text-[11px]">•</Text>
            <HStack className="items-center" space="xs">
              <Icon as={Users} size="xs" className="text-white/90" />
              <Text className="text-white/90 text-[11px]">
                {data.spotsLeft} spots
              </Text>
            </HStack>
          </HStack>

          {/* Meta row 2: location • instructor • duration */}
          <HStack className="items-center" space="sm">
            {data.location && (
              <HStack className="items-center" space="xs">
                <Icon as={MapPin} size="xs" className="text-white/90" />
                <Text className="text-white/90 text-[11px]" numberOfLines={1}>
                  {data.location}
                </Text>
              </HStack>
            )}
            {data.instructor && (
              <>
                <Text className="text-white/50 text-[11px]">•</Text>
                <HStack className="items-center" space="xs">
                  <Icon as={UserIcon} size="xs" className="text-white/90" />
                  <Text className="text-white/90 text-[11px]" numberOfLines={1}>
                    {data.instructor}
                  </Text>
                </HStack>
              </>
            )}
            {durationHours && (
              <>
                <Text className="text-white/50 text-[11px]">•</Text>
                <HStack className="items-center" space="xs">
                  <Icon as={Timer} size="xs" className="text-white/90" />
                  <Text className="text-white/90 text-[11px]">
                    {durationHours}
                  </Text>
                </HStack>
              </>
            )}
          </HStack>
        </VStack>
      </ImageBackground>
    </View>
  );
};

export default HomeExperienceCard;
