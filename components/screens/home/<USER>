import React from 'react';
import { View } from '@/components/ui/view';
import { ImageBackground } from '@/components/ui/image-background';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Users } from 'lucide-react-native';
import type { ChallengeSummary } from '@/data/screens/home/<USER>';

export const HomeChallengeCard = ({ item }: { item: ChallengeSummary }) => {
  return (
    <View className="w-[240px] h-[110px] rounded-2xl overflow-hidden bg-background-200 mr-3">
      <ImageBackground
        source={{ uri: item.imageUrl }}
        className="w-full h-full"
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.2)', 'rgba(0,0,0,0.7)']}
          start={[0, 0]}
          end={[0, 1]}
          className="absolute inset-0"
        />
        <VStack className="absolute inset-0 p-3 justify-between">
          <Text
            className="text-white text-[13px] font-dm-sans-bold"
            numberOfLines={2}
          >
            {item.title}
          </Text>
          <HStack className="items-center justify-between">
            <HStack className="items-center" space="xs">
              <Icon as={Users} size="xs" className="text-white/90" />
              <Text className="text-white/90 text-[11px]">
                {item.participants ?? 0}
              </Text>
            </HStack>
            <HStack className="items-center" space="xs">
              <Text className="text-white text-[12px] font-dm-sans-bold">
                {Math.round(item.progress)}%
              </Text>
              <View className="w-16 h-2 bg-white/30 rounded-full">
                <View
                  className="h-2 bg-info-400 rounded-full"
                  style={{ width: `${item.progress}%` }}
                />
              </View>
            </HStack>
          </HStack>
        </VStack>
      </ImageBackground>
    </View>
  );
};

export default HomeChallengeCard;
