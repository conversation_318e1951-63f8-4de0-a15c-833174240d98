import React from 'react';
import { View } from '@/components/ui/view';
import { ImageBackground } from '@/components/ui/image-background';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Icon } from '@/components/ui/icon';
import { Pressable } from '@/components/ui/pressable';
import type { HomeHeaderData, QuickNavItem } from '@/data/screens/home/<USER>';
import {
  Bell,
  Dumbbell,
  ActivitySquare,
  Users,
  CalendarDays,
} from 'lucide-react-native';

const quickNavIconMap: Record<string, React.ElementType> = {
  classes: Dumbbell,
  challenges: ActivitySquare,
  groups: Users,
  activities: CalendarDays,
};

export const HomeHeader = ({
  header,
  quickNav,
  onNavPress,
}: {
  header: HomeHeaderData;
  quickNav: QuickNavItem[];
  onNavPress?: (id: string) => void;
}) => {
  return (
    <View className="px-4 pt-2">
      <View className="h-[398px] w-full rounded-3xl overflow-hidden">
        <ImageBackground
          source={{ uri: header.bannerUrl }}
          className="w-full h-full"
          imageStyle={{ resizeMode: 'cover' }}
        >
          <LinearGradient
            colors={['rgba(0,0,0,0.2)', 'rgba(0,0,0,0.65)']}
            start={[0, 0]}
            end={[0, 1]}
            className="absolute inset-0"
          />
          {/* Top bar: gym name and notification */}
          <HStack className="absolute top-0 left-0 right-0 p-4 items-center justify-between">
            <Text className="text-xs text-white/80">{header.gymName}</Text>
            <View className="relative">
              <Pressable className="w-9 h-9 rounded-full bg-white/15 items-center justify-center border border-white/20">
                <Icon as={Bell} size="lg" className="text-white" />
              </Pressable>
              <View className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-error-600 items-center justify-center">
                <Text className="text-[9px] text-white">3</Text>
              </View>
            </View>
          </HStack>
          {/* Welcome + Check-in */}
          <VStack className="absolute left-0 right-0 bottom-24 px-4">
            <HStack className="items-center justify-between">
              <VStack>
                <Text className="text-white text-xl font-dm-sans-bold">
                  Welcome {header.firstName}
                </Text>
              </VStack>
              <Button size="sm" className="rounded-full bg-white/90 px-3 py-1">
                <ButtonText className="text-typography-900">
                  Check-in
                </ButtonText>
              </Button>
            </HStack>
          </VStack>
          {/* Quick nav overlay */}
          <HStack className="absolute left-0 right-0 bottom-4 px-4" space="lg">
            {quickNav.map(item => (
              <Pressable
                key={item.id}
                className="flex-1"
                onPress={() => onNavPress?.(item.id)}
              >
                <VStack className="items-center">
                  <View className="w-[64px] h-[64px] rounded-2xl bg-white/12 border border-white/20 items-center justify-center mb-1">
                    <Icon
                      as={quickNavIconMap[item.id] ?? Dumbbell}
                      size="lg"
                      className="text-white"
                    />
                  </View>
                  <Text className="text-[11px] text-white/90">
                    {item.label}
                  </Text>
                </VStack>
              </Pressable>
            ))}
          </HStack>
        </ImageBackground>
      </View>
    </View>
  );
};
