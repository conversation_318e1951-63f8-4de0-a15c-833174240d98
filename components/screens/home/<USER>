import React, { useMemo } from 'react';
import { View } from '@/components/ui/view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Dumbbell, CalendarDays, Target } from 'lucide-react-native';
import type { GoalItem } from '@/data/screens/home/<USER>';

const goalIconMap: Record<string, React.ElementType> = {
  workout: Dumbbell,
  classes: CalendarDays,
};

export const HomeGoalCard = ({ item }: { item: GoalItem }) => {
  const IconComp = useMemo(
    () => goalIconMap[item.iconId || ''] || Target,
    [item.iconId]
  );

  return (
    <View className="w-[260px] rounded-2xl bg-white border border-background-200 p-4 mr-3">
      <HStack className="items-start justify-between" space="sm">
        <HStack className="items-center" space="sm">
          <View className="w-9 h-9 rounded-full bg-primary-50 items-center justify-center">
            <Icon as={IconComp} size="sm" className="text-primary-700" />
          </View>
          <Text
            className="text-[12px] font-dm-sans-bold text-typography-900 max-w-[160px]"
            numberOfLines={2}
          >
            {item.name}
          </Text>
        </HStack>
        <Text className="text-[11px] text-typography-600">
          {item.progressLabel}
        </Text>
      </HStack>
      <View className="h-2 bg-background-200 rounded-full mt-3">
        <View
          className="h-2 bg-primary-500 rounded-full"
          style={{ width: `${item.progress}%` }}
        />
      </View>
    </View>
  );
};

export default HomeGoalCard;
