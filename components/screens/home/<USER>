import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';

export const Section = ({
  title,
  onSeeAll,
  rightLabel = 'See all',
}: {
  title: string;
  onSeeAll?: () => void;
  rightLabel?: string;
}) => {
  return (
    <HStack className="items-center justify-between px-4 py-2">
      <Text className="text-base font-dm-sans-bold text-typography-900">
        {title}
      </Text>
      {onSeeAll ? (
        <Pressable onPress={onSeeAll} className="px-2 py-1 rounded">
          <Text className="text-xs text-primary-600 font-dm-sans-medium">
            {rightLabel}
          </Text>
        </Pressable>
      ) : null}
    </HStack>
  );
};
