import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView, Alert } from 'react-native';
import { router } from 'expo-router';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { HomeHeader } from './home-header';
import { Section } from './section';
import { useHomeQuery } from '@/data/screens/home/<USER>/useHomeQuery';
import { HomeReservationItem } from './home-reservation-item';
import { HomeHoursCard } from './home-hours-card';
import { HomeQuickActionCard } from './home-quick-action-card';
import { HomeExperienceCard } from './home-experience-card';
import { HomeChallengeCard } from './home-challenge-card';
import { HomeGoalCard } from './home-goal-card';
import { HomeBadge } from './home-badge';

export const Homepage = () => {
  const { data } = useHomeQuery();

  if (!data) return null;

  const onQuickNav = (id: string) => {
    switch (id) {
      case 'classes':
        router.push('/(tabs)/classes');
        break;
      case 'challenges':
        router.push('/challenges');
        break;
      case 'groups':
        router.push('/(tabs)/more');
        break;
      case 'activities':
        router.push('/activity');
        break;
      default:
        Alert.alert('Navigation', 'Coming soon');
    }
  };

  const onQuickAction = (id: string) => {
    switch (id) {
      case 'add-pt':
        router.push('/trainers');
        break;
      case 'classes':
        router.push('/(tabs)/classes');
        break;
      case 'checkins':
        router.push('/activity-log');
        break;
      case 'log-activity':
        router.push('/activity-log');
        break;
      case 'goals':
        router.push('/activity');
        break;
      default:
        Alert.alert('Action', 'Coming soon');
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 160 }}
        >
          <HomeHeader
            header={data.header}
            quickNav={data.quickNav}
            onNavPress={onQuickNav}
          />

          <Section
            title="My reservations"
            onSeeAll={() => router.push('/(tabs)/reservation')}
          />
          <VStack className="px-4" space="sm">
            {data.reservations.map(r => (
              <HomeReservationItem key={r.id} {...(r as any)} />
            ))}
          </VStack>

          <Section
            title="Hours of operations"
            onSeeAll={() => router.push('/facility-hours')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {data.hours.map(h => (
              <HomeHoursCard key={h.id} item={h} />
            ))}
          </ScrollView>

          <Section title="Quick actions" />
          <HStack className="px-4 flex-row flex-wrap justify-between">
            {data.quickActions.map(a => (
              <HomeQuickActionCard
                key={a.id}
                action={a}
                onPress={onQuickAction}
              />
            ))}
          </HStack>

          <Section
            title="Experiences for you"
            onSeeAll={() => router.push('/events')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {data.experiences.map(card => (
              <HomeExperienceCard key={card.id} data={card} />
            ))}
          </ScrollView>

          <Section
            title="Challenges"
            onSeeAll={() => router.push('/challenges')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {data.challenges.map(ch => (
              <HomeChallengeCard key={ch.id} item={ch} />
            ))}
          </ScrollView>

          <Section
            title="Your goals"
            onSeeAll={() => router.push('/activity')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {data.goals.map(g => (
              <HomeGoalCard key={g.id} item={g} />
            ))}
          </ScrollView>

          <Section
            title="Recommended groups"
            onSeeAll={() => router.push('/(tabs)/more')}
          />
          <HStack className="px-4" space="sm">
            {data.groups.map(grp => (
              <Box
                key={grp.id}
                className="flex-1 bg-white rounded-2xl border border-background-200 overflow-hidden"
              >
                {grp.imageUrl ? (
                  <Image
                    source={{ uri: grp.imageUrl }}
                    className="w-full h-24"
                  />
                ) : null}
                <VStack className="p-3">
                  <Text className="text-sm font-dm-sans-bold text-typography-900">
                    {grp.name}
                  </Text>
                </VStack>
              </Box>
            ))}
          </HStack>

          <Section
            title="Your badges"
            onSeeAll={() => router.push('/activity-log')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {data.badges.map(b => (
              <HomeBadge key={b.id} item={b} />
            ))}
          </ScrollView>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};
