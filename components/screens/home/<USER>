import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { VStack } from '@/components/ui/vstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import type { QuickAction } from '@/data/screens/home/<USER>';
import {
  Plus,
  Dumbbell,
  Clock,
  TicketPercent,
  ActivitySquare,
  Medal,
} from 'lucide-react-native';

const actionIconMap: Record<string, React.ElementType> = {
  'add-pt': Plus,
  classes: Dumbbell,
  checkins: Clock,
  coupon: TicketPercent,
  'log-activity': ActivitySquare,
  goals: Medal,
};

export const HomeQuickActionCard = ({
  action,
  onPress,
}: {
  action: QuickAction;
  onPress?: (id: string) => void;
}) => {
  return (
    <Pressable
      onPress={() => onPress?.(action.id)}
      className="w-[29%] h-[88px] bg-white rounded-2xl border border-background-200 items-center justify-between px-3 py-3 mr-[3%] mb-3"
      style={{
        shadowColor: '#000',
        shadowOpacity: 0.05,
        shadowRadius: 6,
        shadowOffset: { width: 0, height: 2 },
        elevation: 1,
      }}
    >
      <Box className="w-8 h-8 rounded-lg bg-primary-50 items-center justify-center">
        <Icon
          as={actionIconMap[action.id] ?? Plus}
          size="md"
          className="text-primary-700"
        />
      </Box>
      <Text className="text-[10px] text-typography-900" numberOfLines={1}>
        {action.label}
      </Text>
    </Pressable>
  );
};

export default HomeQuickActionCard;
