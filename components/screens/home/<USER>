import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { View } from '@/components/ui/view';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Award } from 'lucide-react-native';
import type { BadgeItem } from '@/data/screens/home/<USER>';

export const HomeBadge = ({ item }: { item: BadgeItem }) => {
  return (
    <VStack className="items-center mr-4">
      <View
        className="w-[64px] h-[64px] rounded-full items-center justify-center border"
        style={{
          backgroundColor: item.color || '#E6F9FC',
          borderColor: 'rgba(0,0,0,0.06)',
        }}
      >
        <View className="w-9 h-9 rounded-full bg-white/95 items-center justify-center shadow-sm">
          <Icon as={Award} size="sm" className="text-warning-600" />
        </View>
      </View>
      <Text className="text-[10px] mt-1 text-typography-800" numberOfLines={1}>
        {item.name}
      </Text>
    </VStack>
  );
};

export default HomeBadge;
