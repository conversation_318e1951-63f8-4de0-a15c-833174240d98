import React from 'react';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { ProgressRing } from '@/components/shared/progress-ring';
import type { HoursItem } from '@/data/screens/home/<USER>';

export const HomeHoursCard = ({ item }: { item: HoursItem }) => {
  const ringColor = item.status === 'open' ? '#12B886' : '#CBD5E1';
  return (
    <Box className="w-[140px] mr-3 bg-white rounded-2xl p-3 border border-background-200">
      <VStack className="items-center">
        <ProgressRing value={item.percent} size={64} trackColor="#EEF2F7" progressColor={ringColor}>
          <Text className="text-xs font-dm-sans-bold text-typography-900">{Math.round(item.percent)}%</Text>
        </ProgressRing>
      </VStack>
      <VStack className="mt-2 items-center">
        <Text className="text-[12px] font-dm-sans-bold text-typography-900" numberOfLines={1}>{item.title}</Text>
        <Text className="text-[10px] text-typography-600" numberOfLines={1}>{item.subtitle}</Text>
      </VStack>
    </Box>
  );
};

export default HomeHoursCard;

