import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import type { Reservation } from '@/data/screens/reservations/types';

export type HomeReservationItemProps = Reservation & { isPast?: boolean };

export const HomeReservationItem = ({ title, imageUrl, timeLabel, subtitle }: HomeReservationItemProps) => {
  return (
    <HStack className="items-center bg-white rounded-2xl px-3 py-3 border border-background-200" space="md">
      <Box className="w-9 h-9 rounded-md overflow-hidden bg-background-200">
        {imageUrl ? (
          <Image source={{ uri: imageUrl }} className="w-full h-full" contentFit="cover" />
        ) : null}
      </Box>
      <VStack className="flex-1">
        <Text className="text-[13px] font-dm-sans-bold text-typography-900" numberOfLines={1}>{title}</Text>
        <HStack className="items-center" space="sm">
          <Text className="text-[11px] text-typography-700">{timeLabel}</Text>
          <Text className="text-[11px] text-typography-400">•</Text>
          <Text className="text-[11px] text-typography-600" numberOfLines={1}>{subtitle}</Text>
        </HStack>
      </VStack>
    </HStack>
  );
};

export default HomeReservationItem;

