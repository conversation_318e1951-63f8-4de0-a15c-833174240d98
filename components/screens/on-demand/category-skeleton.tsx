import React from 'react';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Skeleton, SkeletonText } from '@/components/ui/skeleton';

export const CategoriesGridSkeleton = () => {
  const items = Array.from({ length: 6 }, (_, i) => i);
  return (
    <VStack className="px-4 pt-3 pb-6">
      <HStack className="flex-wrap" space='xs'>
        {items.map(i => (
          <View key={i} style={{ width: '48%' }} className="mr-3 mb-4">
            <VStack className="rounded-xl overflow-hidden bg-white border border-background-200">
              <Skeleton className="h-[120px] w-full" />
              <VStack className="px-3 py-2">
                <SkeletonText className="h-4 w-32 rounded" />
              </VStack>
            </VStack>
          </View>
        ))}
      </HStack>
    </VStack>
  );
};

