import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Image } from '@/components/ui/image';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { Heart, Play } from 'lucide-react-native';
import {
  getInitials,
  getRandomColorForInitials,
} from '@/data/common/common.utils';
import type { OnDemandVideo } from '@/data/api-client/on-demand';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';

export type VideoCardProps = {
  video: OnDemandVideo;
  onPlay?: (video: OnDemandVideo) => void;
};

const formatDuration = (val?: string) => {
  if (!val) return '';
  const parts = val.split(':').map(Number);
  if (parts.some(n => Number.isNaN(n))) return val;
  if (parts.length === 3) {
    const [h, m, s] = parts;
    if (h) return `${h}h ${m}m`;
    if (m) return `${m}m`;
    return `${s}s`;
  }
  if (parts.length === 2) {
    const [m, s] = parts;
    if (m) return `${m}m`;
    return `${s}s`;
  }
  return val;
};

export const VideoCard: React.FC<VideoCardProps> = ({ video, onPlay }) => {
  const name = video.name || 'Untitled';
  const imageUrl = video.video_thumbnail || undefined;
  const isNew = video.is_new;
  const instructor = [video.instructor_first_name, video.instructor_last_name]
    .filter(Boolean)
    .join(' ');

  return (
    <VStack className="mb-4 rounded-2xl overflow-hidden border border-background-200 bg-white">
      <View className="w-full h-[160px]">
        {imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            className="w-full h-full"
            size="full"
            alt={name}
          />
        ) : (
          <Box
            className="w-full h-full items-center justify-center"
            style={{ backgroundColor: getRandomColorForInitials(name) }}
          >
            <Text className="text-white font-dm-sans-bold text-xl">
              {getInitials(name)}
            </Text>
          </Box>
        )}

        <View className="absolute top-2 right-2 w-9 h-9 rounded-full bg-white items-center justify-center">
          <Icon as={Heart} size="sm" className="text-typography-500" />
        </View>

        {/* bottom-left metadata bar over image */}
        <HStack className="absolute bottom-2 left-2 right-2 items-center justify-between">
          <HStack space="md" className="items-center">
            {isNew && (
              <Badge
                action="success"
                variant="solid"
                size="sm"
                className="rounded-full"
              >
                <BadgeText className="text-[10px]">New</BadgeText>
              </Badge>
            )}
          </HStack>
        </HStack>
      </View>

      {/* Title */}
      <VStack className="px-4 py-2">
        <Text
          className="text-typography-900 font-dm-sans-bold"
          numberOfLines={1}
        >
          {name}
        </Text>
      </VStack>

      {/* Details row */}
      <HStack className="px-4  items-center justify-between">
        <HStack className="items-center" space="md">
          {/* level */}
          {video.level && (
            <HStack className="items-center" space="xs">
              <View className="w-1.5 h-1.5 rounded-full bg-primary-600" />
              <Text className="text-typography-700 text-xs capitalize">
                {video.level}
              </Text>
            </HStack>
          )}
          {/* duration */}
          {video.duration_minutes && (
            <HStack className="items-center" space="xs">
              <View className="w-1.5 h-1.5 rounded-full bg-typography-500" />
              <Text className="text-typography-700 text-xs">
                {formatDuration(video.duration_minutes)}
              </Text>
            </HStack>
          )}
        </HStack>

        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => onPlay?.(video)}
          className="px-4 py-2 rounded-full bg-primary-50 border border-primary-300"
        >
          <HStack className="items-center" space="xs">
            <Icon as={Play} size="sm" className="text-primary-600" />
            <Text className="text-primary-700 font-dm-sans-bold">Play</Text>
          </HStack>
        </TouchableOpacity>
      </HStack>

      {/* Footer meta */}
      {(video.created || instructor) && (
        <HStack className="px-4 pb-4 items-center" space="md">
          {video.created && (
            <Text className="text-typography-400 text-xs">
              {format(new Date(video.created), DATE_FORMAT.DAY_MONTH_YEAR)}
            </Text>
          )}
          {instructor ? (
            <Text className="text-typography-400 text-xs">• {instructor}</Text>
          ) : null}
        </HStack>
      )}
    </VStack>
  );
};
