import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Image } from '@/components/ui/image';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Badge, BadgeText } from '@/components/ui/badge';
import { Box } from '@/components/ui/box';
import { getInitials, getRandomColorForInitials } from '@/data/common/common.utils';

export type CategoryCardProps = {
  id: number;
  name: string;
  imageUrl?: string | null;
  videosCount?: number;
  isNew?: boolean;
  onPress?: (id: number) => void;
};

export const OnDemandCategoryCard: React.FC<CategoryCardProps> = ({
  id,
  name,
  imageUrl,
  videosCount = 0,
  isNew = false,
  onPress,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={() => onPress?.(id)}
      className="flex-1 mr-3 mb-4"
    >
      <VStack className="rounded-xl overflow-hidden bg-white border border-background-200">
        <View className="h-[120px] w-full overflow-hidden">
          {imageUrl ? (
            <Image source={{ uri: imageUrl }} className="w-full h-full" size="full" alt={name} />
          ) : (
            <Box className="w-full h-full items-center justify-center" style={{ backgroundColor: getRandomColorForInitials(name) }}>
              <Text className="text-white font-dm-sans-bold text-xl">{getInitials(name)}</Text>
            </Box>
          )}

          {/* Badges overlay */}
          <View className="absolute top-2 left-2">
            {isNew && (
              <Badge action="success" variant="solid" size="sm" className="rounded-full">
                <BadgeText className="text-[10px]">New</BadgeText>
              </Badge>
            )}
          </View>

          <View className="absolute top-2 right-2">
            <Badge action="muted" variant="solid" size="sm" className="rounded-full bg-white">
              <BadgeText className="text-black bg-white text-[10px]">{videosCount} {videosCount === 1 ? 'video' : 'videos'}</BadgeText>
            </Badge>
          </View>
        </View>

        <VStack className="px-3 py-2">
          <Text numberOfLines={1} className="text-typography-900 font-dm-sans-bold">
            {name}
          </Text>
        </VStack>
      </VStack>
    </TouchableOpacity>
  );
};

