import React from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import type { Challenge } from '@/data/screens/challenges/types';
import { router } from 'expo-router';
import { ProgressRing } from '@/components/shared/progress-ring';
import { ChallengesTab } from '@/hooks/useChallengesWithFilter';
import {
  getParticipationProgress,
  isCurrentlyParticipating,
} from '@/data/screens/challenges/utils';

type Props = {
  challenge: Challenge;
  onJoin?: (challenge: Challenge) => void;
  selectedTab: ChallengesTab;
};

export const ChallengeCard = ({ challenge, onJoin, selectedTab }: Props) => {
  const joined = isCurrentlyParticipating(
    challenge.has_challenge_participation
  );
  const progress = getParticipationProgress(
    challenge.has_challenge_participation
  );
  return (
    <TouchableOpacity
      onPress={() =>
        router.push({
          pathname: '/(challenge-details)/[id]',
          params: { id: String(challenge.id), tab: selectedTab },
        })
      }
    >
      <HStack className="bg-white rounded-2xl overflow-hidden mb-3 items-center border border-background-200 p-0">
        <View className="w-[72px] h-[72px] rounded-xl overflow-hidden bg-background-200">
          {challenge.default_image ? (
            <Image
              source={{ uri: challenge.default_image }}
              className="w-full h-full"
              resizeMode="cover"
            />
          ) : null}
        </View>

        <VStack className="flex-1 ml-3 mr-3 py-3" space="xs">
          <HStack className="items-center justify-between pr-2">
            <Text className="text-primary-700 font-dm-sans-medium">
              {challenge.title}
            </Text>
            {joined && (
              <HStack className="items-center" space="xs">
                <Text className="text-typography-500 text-xs mr-2">
                  {progress}%
                </Text>
                <ProgressRing value={progress} />
              </HStack>
            )}
          </HStack>

          <HStack className="items-center justify-between pr-2">
            <HStack className="items-center" space="sm">
              {/* Participant count pill */}
              <View className="h-6 rounded-full bg-secondary-200 px-2 items-center justify-center">
                <Text className="text-background-light text-xs">
                  +{challenge.challenge_participations_count}
                </Text>
              </View>
              <Text className="text-typography-500 text-xs">
                {challenge.days_left} days left
              </Text>
            </HStack>

            {!joined && selectedTab === 'active' && (
              <Button
                variant="outline"
                size="sm"
                action="secondary"
                className="px-4 rounded-full h-[36px] border-secondary-200"
                onPress={() => onJoin?.(challenge)}
              >
                <ButtonText className="text-primary-600">Join</ButtonText>
              </Button>
            )}
          </HStack>
        </VStack>
      </HStack>
    </TouchableOpacity>
  );
};
