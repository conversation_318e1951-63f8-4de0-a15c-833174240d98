import React from 'react';
import { parseISO, format } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';
import { BaseDetailsView } from '@/components/shared/details-view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import {
  CalendarDays,
  Timer,
  ClipboardList,
  MapPin,
  View,
  User,
  Users,
} from 'lucide-react-native';
import type { Challenge } from '@/data/screens/challenges/types';
import { Button, ButtonText } from '@/components/ui/button';
import { Progress, ProgressFilledTrack } from '@/components/ui/progress';
import type { BaseDetailsItem } from '@/components/shared/details-view/types';
import {
  useJoinChallengeMutation,
  useLeaveChallengeMutation,
} from '@/data/screens/challenges/queries/useChallengesQuery';
import { ToastManager } from '@/components/shared/toast/ToastManager';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { useState } from 'react';
import {
  getParticipationProgress,
  isCurrentlyParticipating,
} from '@/data/screens/challenges/utils';

type Props = {
  item: Challenge;
  isLoading?: boolean;
  selectedTab?: 'active' | 'history';
};

export const ChallengeDetails = ({ item, selectedTab = 'active' }: Props) => {
  const start = item.start_date
    ? format(parseISO(item.start_date), DATE_FORMAT.DAY_MONTH_DAY_YEAR)
    : '';
  const end = item.end_date
    ? format(parseISO(item.end_date), DATE_FORMAT.DAY_MONTH_DAY_YEAR)
    : '';
  const joined = isCurrentlyParticipating(item.has_challenge_participation);

  const { mutateAsync: joinChallenge, isPending } = useJoinChallengeMutation();
  const { mutateAsync: leaveChallenge, isPending: isLeaving } =
    useLeaveChallengeMutation();
  const [showLeaveSheet, setShowLeaveSheet] = useState(false);

  const baseItem: BaseDetailsItem = {
    id: item.id,
    name: item.title,
    description: item.description ?? '',
    start_time: item.start_date ?? '',
    end_time: item.end_date ?? '',
    gym_name: '',
    room_name: '',
    is_favourite: false,
  };

  return (
    <BaseDetailsView
      item={baseItem}
      heroImageUrl={item.default_image ?? undefined}
      renderActionButton={() =>
        selectedTab === 'history' ? null : joined ? (
          <>
            <Button
              size="lg"
              variant="outline"
              action="negative"
              className="rounded-full bg-error-100 border-error-200"
              onPress={() => setShowLeaveSheet(true)}
            >
              <ButtonText className="text-error-600">
                Leave challenge
              </ButtonText>
            </Button>

            <Actionsheet
              isOpen={showLeaveSheet}
              onClose={() => setShowLeaveSheet(false)}
            >
              <ActionsheetBackdrop />
              <ActionsheetContent>
                <ActionsheetDragIndicatorWrapper>
                  <ActionsheetDragIndicator />
                </ActionsheetDragIndicatorWrapper>
                <VStack space="sm" className="w-full mt-2">
                  <Text className="text-2xl font-dm-sans-bold text-typography-900">
                    Leave challenge
                  </Text>
                  <Text className="text-typography-600">
                    Are you sure you want to leave this challenge ?
                  </Text>
                  <HStack space="md" className="mt-4">
                    <Button
                      className="flex-1 rounded-full"
                      variant="outline"
                      action="secondary"
                      onPress={() => setShowLeaveSheet(false)}
                    >
                      <ButtonText>No, stay</ButtonText>
                    </Button>
                    <Button
                      className="flex-1 rounded-full"
                      action="negative"
                      onPress={async () => {
                        try {
                          await leaveChallenge(item.id);
                          setShowLeaveSheet(false);
                          ToastManager.show(
                            'You left the challenge',
                            'success'
                          );
                        } catch {}
                      }}
                      disabled={isLeaving}
                    >
                      <ButtonText>Yes, leave</ButtonText>
                    </Button>
                  </HStack>
                </VStack>
              </ActionsheetContent>
            </Actionsheet>
          </>
        ) : (
          <Button
            size="lg"
            className="rounded-full"
            variant="solid"
            action="primary"
            disabled={isPending}
            onPress={() => joinChallenge(item.id)}
          >
            <ButtonText>Join challenge</ButtonText>
          </Button>
        )
      }
    >
      {/* Progress section */}
      {joined ? (
        <VStack space="xs">
          <HStack className="items-center justify-between">
            <Text className="text-typography-900 font-dm-sans-bold">
              Your progress
            </Text>
            <Text className="text-typography-900 font-dm-sans-bold">{`${getParticipationProgress(item.has_challenge_participation)}%`}</Text>
          </HStack>
          <Progress
            size="sm"
            value={getParticipationProgress(item.has_challenge_participation)}
            max={100}
          >
            <ProgressFilledTrack />
          </Progress>
        </VStack>
      ) : null}

      {/* Tabs mimic - static for now: Info selected */}
      <HStack className="bg-background-100 p-1 rounded-xl">
        <HStack className="flex-1 h-[44px] bg-secondary-200 rounded-xl items-center justify-center">
          <Text className="text-background-light">Info</Text>
        </HStack>
        <HStack className="flex-1 h-[44px] rounded-xl items-center justify-center">
          <Text className="text-typography-500">Leaderboard</Text>
        </HStack>
      </HStack>

      {/* Avatars group placeholder and count */}
      <HStack className="items-center" space="sm">
        <HStack className="h-6 rounded-full  items-center justify-center">
          <Icon as={Users} size="sm" />
        </HStack>
        <Text className="text-typography-900">
          +{`${item.challenge_participations_count} participants`}
        </Text>
      </HStack>

      <VStack space="md">
        <HStack space="md" className="items-center">
          <Icon as={CalendarDays} size="sm" className="text-typography-600" />
          <Text className="text-typography-700">
            {start} - {end}
          </Text>
        </HStack>
        <HStack space="md" className="items-center">
          <Icon as={Timer} size="sm" className="text-typography-600" />
          <Text className="text-typography-700">
            {item.days_left} days left
          </Text>
        </HStack>
        <HStack space="md" className="items-center">
          <Icon as={ClipboardList} size="sm" className="text-typography-600" />
          <Text className="text-typography-700">{item.title}</Text>
        </HStack>
        <HStack space="md" className="items-center">
          <Icon as={MapPin} size="sm" className="text-typography-600" />
          <Text className="text-typography-700">Pt session</Text>
        </HStack>
      </VStack>

      <VStack space="xs">
        <Text className="text-typography-900 font-dm-sans-medium">
          Description
        </Text>
        <Text className="text-typography-600">{item.description ?? ''}</Text>
      </VStack>
    </BaseDetailsView>
  );
};

export default ChallengeDetails;
