import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronLeft } from 'lucide-react-native';
import { router } from 'expo-router';

type Props = {
  selectedTab: 'active' | 'history';
  onTabChange: (tab: 'active' | 'history') => void;
};

export const ChallengesHeader = ({ selectedTab, onTabChange }: Props) => {
  return (
    <VStack className="bg-white pt-4 pb-1" space="md">
      <HStack className="px-4 justify-between items-center">
        <HStack className="items-center" space="md">
          <Pressable
            onPress={() => router.back()}
            className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
          >
            <Icon as={ChevronLeft} size="lg" className="text-typography-900" />
          </Pressable>
          <Text className="text-2xl font-dm-sans-bold text-typography-900">
            Challenges
          </Text>
        </HStack>
      </HStack>

      {/* Tabs: full-width split with underline indicator */}
      <VStack className="border-b border-background-200">
        <HStack className="px-4 mb-4 mt-4">
          {(['active', 'history'] as const).map(tab => (
            <Pressable
              key={tab}
              onPress={() => onTabChange(tab)}
              className="flex-1"
            >
              <VStack className="items-center">
                <Text
                  className={`text-base ${
                    selectedTab === tab
                      ? 'text-typography-900 font-dm-sans-medium'
                      : 'text-typography-500 font-dm-sans-regular'
                  }`}
                >
                  {tab === 'active' ? 'Active' : 'History'}
                </Text>
                <HStack
                  className={`${
                    selectedTab === tab ? 'bg-secondary-400' : 'bg-transparent'
                  } h-1 w-full rounded-full mt-2`}
                />
              </VStack>
            </Pressable>
          ))}
        </HStack>
      </VStack>
    </VStack>
  );
};
