import React from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { Challenge } from '@/data/screens/challenges/types';
import { ChallengeCard } from './challenge-card';
import { CardSkeletonLoader } from '@/components/shared/card-skeleton';
import { EmptySearchState } from '@/components/shared/empty-search';
import { EmptyState } from '@/components/screens/classes/empty-state';
import { useJoinChallengeMutation } from '@/data/screens/challenges/queries/useChallengesQuery';
import { ChallengesTab } from '@/hooks/useChallengesWithFilter';

type Props = {
  challenges: Challenge[];
  searchTerm: string;
  isLoading: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
  onClearSearch: () => void;
  selectedTab: ChallengesTab;
};

export const ChallengesList = ({
  challenges,
  searchTerm,
  isLoading,
  isRefreshing,
  onRefresh,
  onClearSearch,
  selectedTab,
}: Props) => {
  const { mutate: joinChallenge } = useJoinChallengeMutation();

  if (isLoading) return <CardSkeletonLoader />;

  if (searchTerm && challenges.length === 0) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (!searchTerm && challenges.length === 0) {
    return (
      <EmptyState
        title="No challenges"
        subtitle="There are no challenges available right now."
      />
    );
  }

  return (
    <FlatList
      data={challenges}
      keyExtractor={item => String(item.id)}
      renderItem={({ item }) => (
        <ChallengeCard
          selectedTab={selectedTab}
          challenge={item}
          onJoin={() => joinChallenge(item.id)}
        />
      )}
      ItemSeparatorComponent={() => <></>}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 12,
        paddingBottom: 200,
        paddingHorizontal: 12,
      }}
    />
  );
};
