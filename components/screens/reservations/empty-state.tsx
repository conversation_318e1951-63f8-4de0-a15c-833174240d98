import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { View } from 'react-native';
import { <PERSON>, <PERSON><PERSON>oin, CardSend, EmptyWalletTick } from 'iconsax-react-nativejs';

type Props = {
  selectedTab: 'upcoming' | 'past';
};

export const ReservationsEmptyState = ({ selectedTab }: Props) => {
  if (selectedTab === 'upcoming') {
    return (
      <VStack className="items-center justify-center px-6 ">
        <View className="w-36 h-36  bg-[#F1FBFD] items-center justify-center">
           <Card size={80} color="#2FD3EB" variant="Bulk" />
        </View>
        <Text className="text-lg font-dm-sans-bold text-typography-900 mb-1">
          No upcoming reservation!
        </Text>
        <Text className="text-sm font-dm-sans-regular text-typography-600 text-center mb-6">
          Lets get active and make our next reservation!
        </Text>

        <VStack className="w-full px-6" space="sm">
          <Button
            variant="outline"
            action="secondary"
            className="h-11 rounded-full bg-[#E6F9FC] border-[#2FD3EB]"
          >
            <ButtonText className="text-primary-700">
              Reserve a class
            </ButtonText>
          </Button>
          <Button
            variant="outline"
            action="secondary"
            className="h-11 rounded-full border-background-300"
          >
            <ButtonText className="text-typography-900">
              Reserve equipment
            </ButtonText>
          </Button>
          <Button
            variant="outline"
            action="secondary"
            className="h-11 rounded-full border-background-300"
          >
            <ButtonText className="text-typography-900">
              Schedule an appointment
            </ButtonText>
          </Button>
        </VStack>
      </VStack>
    );
  }

  return (
    <VStack className="items-center justify-center px-6 py-12">
      <Text className="text-lg font-dm-sans-medium text-typography-900 mb-1">
        No past reservations
      </Text>
      <Text className="text-sm font-dm-sans-regular text-typography-600 text-center">
        Make a reservation and it will show up here
      </Text>
    </VStack>
  );
};
