import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import type { ReservationCategory } from '@/data/screens/reservations/types';

type Props = {
  selected: ReservationCategory;
  counts?: Partial<Record<ReservationCategory, number>>;
  onChange: (category: ReservationCategory) => void;
};

const CategoryPill = ({
  active,
  label,
  count,
  onPress,
}: {
  active: boolean;
  label: string;
  count?: number;
  onPress: () => void;
}) => (
  <Pressable
    onPress={onPress}
    className={`px-3 py-2 rounded-lg border ${active ? 'bg-primary-500 border-primary-500' : 'bg-transparent border-background-300'}`}
  >
    <HStack className="items-center" space="xs">
      <Text
        className={`${active ? 'text-black font-bold' : 'text-typography-700'} text-xs`}
      >
        {label}
      </Text>
      {typeof count === 'number' && (
        <HStack
          className={`h-5 px-1 rounded-full items-center justify-center ml-2 ${active ? 'bg-white' : 'bg-gray-100'}`}
        >
          <Text
            className={`${active ? 'text-primary-700' : 'text-black'} text-xs`}
          >
            {count}
          </Text>
        </HStack>
      )}
    </HStack>
  </Pressable>
);

export const ReservationsCategories = ({
  selected,
  onChange,
  counts = {},
}: Props) => {
  return (
    <HStack className="px-4" space='md'>
      <CategoryPill
        active={selected === 'class'}
        label="Class"
        count={counts.class}
        onPress={() => onChange('class')}
      />
      <CategoryPill
        active={selected === 'appointment'}
        label="Appointment"
        count={counts.appointment}
        onPress={() => onChange('appointment')}
      />
      <CategoryPill
        active={selected === 'equipment'}
        label="Equipment"
        count={counts.equipment}
        onPress={() => onChange('equipment')}
      />
    </HStack>
  );
};
