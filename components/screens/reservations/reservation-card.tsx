import React from 'react';
import { TouchableOpacity } from 'react-native';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import type { Reservation } from '@/data/screens/reservations/types';
import { ImageAvatars } from '@/components/shared/overlapping-avatars';
import { router } from 'expo-router';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';

export const ReservationCard = ({
  id,
  title,
  subtitle,
  dateLabel,
  timeLabel,
  imageUrl,
  room,
  isPast,
}: Reservation & { isPast?: boolean }) => {

  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelReservation();

  const handleCancelReservation = () => {
    cancelReservation(id);
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() =>
        router.push({ pathname: '/(reservation-details)/[id]', params: { id } })
      }
    >
      <Box className="bg-white mx-3 mb-3 rounded-xl shadow-sm border border-gray-100 px-4 py-4">
        <HStack space="md" className="items-start">
          <ImageAvatars title={title} imageUrl={imageUrl} size="md" />
          <VStack
            className="flex-1 justify-between"
            space="xs"
            style={{ minHeight: 48 }}
          >
            <Text className="text-primary-700 font-dm-sans-bold text-base leading-5 mt-1">
              {title}
            </Text>
            <HStack className="items-center">
              {subtitle && (
                <Text className="text-typography-600 font-dm-sans-regular text-sm leading-4">
                  {subtitle}
                </Text>
              )}
              <Box className="w-1 h-1 bg-[#00697B] rounded-full mx-1" />
              <Text className="text-typography-600 font-dm-sans-regular text-sm leading-4">
                {room}
              </Text>
            </HStack>
          </VStack>
        </HStack>
        <HStack className="items-center justify-between mt-1 pl-2">
          <HStack className="items-center">
            <Text className="text-typography-500 font-dm-sans-regular text-sm leading-4">
              {dateLabel}
            </Text>
            <Box className="w-[2px] h-3 bg-gray-200 mx-2" />
            <Text className="text-typography-500 font-dm-sans-regular text-sm leading-4">
              {timeLabel}
            </Text>
          </HStack>

          {!isPast && (
            <Button
              variant="outline"
              action="negative"
              size="sm"
              onPress={handleCancelReservation}
              isDisabled={isCancelling}
              className="border border-error-300 bg-[#FFF7F7] rounded-full font-bold"
            >
              {isCancelling ? (
                <ButtonSpinner size="small" />
              ) : (
                <ButtonText className="text-error-500 font-dm-sans-medium text-sm">
                  Cancel
                </ButtonText>
              )}
            </Button>
          )}
        </HStack>
      </Box>
    </TouchableOpacity>
  );
};
