import React from 'react';
import { FlatList, RefreshControl } from 'react-native';
import type { Reservation } from '@/data/screens/reservations/types';
import { CardSkeletonLoader } from '@/components/shared/card-skeleton';
import { EmptySearchState } from '@/components/shared/empty-search';
import { ReservationsEmptyState } from '@/components/screens/reservations/empty-state';
import { ReservationCard } from './reservation-card';

type Props = {
  reservations: Reservation[];
  searchTerm: string;
  isLoading: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
  onClearSearch: () => void;
  selectedTab: 'upcoming' | 'past';
};

export const ReservationsList = ({
  reservations,
  searchTerm,
  isLoading,
  isRefreshing,
  onRefresh,
  onClearSearch,
  selectedTab,
}: Props) => {
  if (isLoading) return <CardSkeletonLoader />;

  if (searchTerm && reservations.length === 0) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (!searchTerm && reservations.length === 0) {
    return <ReservationsEmptyState selectedTab={selectedTab} />;
  }

  const isPast = selectedTab === 'past';

  return (
    <FlatList
      data={reservations}
      keyExtractor={item => String(item.id)}
      renderItem={({ item }) => <ReservationCard {...item} isPast={isPast} />}
      ItemSeparatorComponent={() => <></>}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingTop: 16, paddingBottom: 200 }}
    />
  );
};
