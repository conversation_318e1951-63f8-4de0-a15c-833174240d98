import React from 'react';
import { View, StatusBar } from 'react-native';
import { ScrollView } from '@/components/ui/scroll-view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Skeleton, SkeletonText } from '@/components/ui/skeleton';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';

export const ReservationDetailsSkeleton = () => {
  const statusBarHeight = StatusBar.currentHeight || 44;

  return (
    <View className="flex-1 bg-background-0">
      <StatusBar barStyle="light-content" />

      {/* Hero Image Skeleton */}
      <View className="h-[300]">
        <Skeleton className="h-full w-full bg-background-300" />

        {/* Back button */}
        <Pressable
          onPress={() => router.back()}
          style={{ position: 'absolute', top: statusBarHeight + 16, left: 16, zIndex: 10 }}
          className="p-2 bg-slate-500 rounded-full"
        >
          <Icon as={ArrowLeft} size="sm" color="white" className="text-typography-900" />
        </Pressable>

        {/* Title overlay skeleton */}
        <Box className="absolute bottom-0 left-0 right-0 p-6 pb-8">
          <SkeletonText className="h-10 w-64 rounded bg-background-400" />
        </Box>
      </View>

      {/* Content card skeleton */}
      <Box className="bg-background-0 rounded-t-3xl -mt-5 flex-1">
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <VStack space="lg" className="px-4 pt-6 pb-24">
            {/* Metrics card skeleton - duration only */}
            <Box className="bg-white rounded-xl border border-background-200 pt-4 pb-4 mb-4">
              <HStack className="flex justify-center">
                <VStack className="items-center">
                  <Skeleton className="h-12 w-12 rounded-lg" />
                  <SkeletonText className="h-5 w-20 rounded mt-2" />
                  <SkeletonText className="h-3 w-20 rounded mt-1" />
                </VStack>
              </HStack>
            </Box>

            {/* Info items skeleton */}
            <VStack space="md">
              {/* Date */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-48 rounded" />
              </HStack>

              {/* Time */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-32 rounded" />
              </HStack>

              {/* Location */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-40 rounded" />
              </HStack>

              {/* Room */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-28 rounded" />
              </HStack>
            </VStack>

            {/* Instructor row skeleton */}
            <HStack space="sm" className="items-center border-t border-b border-background-200 py-4 mt-2">
              <Skeleton className="w-10 h-10 rounded-full" />
              <VStack className="flex-1" space="xs">
                <SkeletonText className="h-3 w-16 rounded" />
                <SkeletonText className="h-5 w-32 rounded" />
              </VStack>
              <SkeletonText className="h-4 w-20 rounded" />
            </HStack>
          </VStack>
        </ScrollView>

        {/* Action button section skeleton */}
        <Box className="px-6 pt-2 pb-6 bg-white border-t border-background-200">
          <HStack className="items-center justify-between">
            <Skeleton className="h-12 flex-1 rounded-full mr-4" />
            <Skeleton className="h-12 w-12 rounded-full" />
          </HStack>
        </Box>
      </Box>
    </View>
  );
};

export default ReservationDetailsSkeleton;

