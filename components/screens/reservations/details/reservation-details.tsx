import React, { useMemo } from 'react';
import { differenceInMinutes, parse, format, parseISO } from 'date-fns';
import { Reservation } from '@/data/screens/reservations/types';
import {
  BaseDetailsView,
  MetricsSection,
  InfoSection,
  InstructorSection,
} from '@/components/shared/details-view';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import { AddToCalendarButton } from '@/components/shared/add-to-calendar';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';
import { DATE_FORMAT } from '@/constants/date-formats';
import { router } from 'expo-router';

interface ReservationDetailsProps {
  item: Reservation;
}

export const ReservationDetails: React.FC<ReservationDetailsProps> = ({
  item,
}) => {
  const duration = useMemo(() => {
    const start = parse(item.startTime || '00:00:00', 'HH:mm:ss', new Date());
    const end = parse(item.endTime || '00:00:00', 'HH:mm:ss', new Date());
    const minutes = Math.max(0, differenceInMinutes(end, start));
    return `${minutes} MINS`;
  }, [item.startTime, item.endTime]);

  const { mutate: cancelReservation, isPending: isCancellingGeneral } =
    useCancelReservation(() => {
      return router.push('/reservation');
    });

  const isPastReservation = item.status === 'past';

  const isPending = isCancellingGeneral;

  // Calendar button handled by the shared AddToCalendarButton component

  const baseItem = {
    id: item.id,
    name: item.title,
    description: '',
    start_time: item.startTime || '00:00:00',
    end_time: item.endTime || '00:00:00',
    gym_name: item.subtitle || '',
    room_name: item.room || '',
  } as const;

  const handleCancel = () => {
    cancelReservation(item.id);
  };

  return (
    <BaseDetailsView
      item={baseItem}
      heroImageUrl={item.imageUrl || undefined}
      renderActionButton={() => (
        <>
          {!isPastReservation && (
            <HStack className="items-center justify-between">
              <>
                <Button
                  size="lg"
                  variant="outline"
                  className="flex-1 mr-4 rounded-full border-error-300 bg-[#FFF7F7]"
                  onPress={handleCancel}
                  disabled={isPending}
                >
                  {isPending ? (
                    <ButtonSpinner size="small" />
                  ) : (
                    <ButtonText className="text-error-500 font-dm-sans-medium">
                      Cancel
                    </ButtonText>
                  )}
                </Button>

                <AddToCalendarButton
                  name={item.title}
                  description={item.subtitle || ''}
                  startTime={item.startTime || '00:00:00'}
                  endTime={item.endTime || '00:00:00'}
                  location={[item.subtitle, item.room]
                    .filter(Boolean)
                    .join(', ')}
                  instructor={item.instructor || ''}
                  reservationId={item.id}
                  selectedDate={item.startDate || new Date().toISOString()}
                />
              </>
            </HStack>
          )}
        </>
      )}
    >
      <MetricsSection
        intensity={undefined}
        duration={duration}
        showIntensity={false}
      />

      <InfoSection
        date={
          item.startDate
            ? format(parseISO(item.startDate), DATE_FORMAT.DAY_MONTH_DAY_YEAR)
            : item.dateLabel
        }
        timeFrame={item.timeLabel}
        location={item.subtitle || ''}
        room={item.room || ''}
      />

      {item.instructor ? (
        <InstructorSection
          instructorName={item.instructor}
          showReadAbout={false}
        />
      ) : null}
    </BaseDetailsView>
  );
};

export default ReservationDetails;
