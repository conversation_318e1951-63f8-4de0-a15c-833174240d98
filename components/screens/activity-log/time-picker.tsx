import React, { useState } from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { Clock } from 'lucide-react-native';
import { format } from 'date-fns';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { ScrollView } from '@/components/ui/scroll-view';

interface TimePickerProps {
  value: string;
  onChange: (value: string) => void;
  label: string;
}

const TimePicker: React.FC<TimePickerProps> = ({ value, onChange, label }) => {
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Generate time slots from 00:00 to 23:59 in 5-minute intervals for finer selection
  const generateTimeSlots = () => {
    const slots: string[] = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute
          .toString()
          .padStart(2, '0')}`;
        slots.push(timeString);
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  const handleTimeSelect = (time: string) => {
    onChange(time);
    setShowTimePicker(false);
  };

  const formatDisplayTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return format(date, 'h:mma');
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {label}
      </Text>

      <Pressable
        onPress={() => setShowTimePicker(true)}
        className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14 flex-row items-center justify-between"
      >
        <Text
          className={`flex-1 text-base ${value ? 'text-typography-900' : 'text-typography-500'}`}
        >
          {value ? formatDisplayTime(value) : 'Select time'}
        </Text>
        <Icon as={Clock} size="sm" className="text-typography-500" />
      </Pressable>

      {showTimePicker && (
        <Actionsheet
          isOpen={showTimePicker}
          onClose={() => setShowTimePicker(false)}
        >
          <ActionsheetBackdrop />
          <ActionsheetContent>
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>
            <VStack className="w-full max-h-96">
              <Text className="text-lg font-dm-sans-bold text-center py-4">
                Select Time
              </Text>
              <ScrollView className="flex-1">
                <VStack space="xs" className="px-4 pb-4">
                  {timeSlots.map(time => (
                    <Pressable
                      key={time}
                      onPress={() => handleTimeSelect(time)}
                      className={`p-3 rounded-lg border ${
                        value === time
                          ? 'border-[#00AECC] bg-[#00AECC]'
                          : 'border-background-200 bg-white'
                      }`}
                    >
                      <Text
                        className={`text-center font-dm-sans-medium ${
                          value === time ? 'text-white' : 'text-typography-900'
                        }`}
                      >
                        {formatDisplayTime(time)}
                      </Text>
                    </Pressable>
                  ))}
                </VStack>
              </ScrollView>
            </VStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </VStack>
  );
};

export default TimePicker;


