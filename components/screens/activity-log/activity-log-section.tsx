import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { ActivityLogItem, ActivityLogItemData } from './activity-log-item';

export interface ActivityLogSectionData {
  id: string;
  dateLabel: string; // e.g., 'Tue, Jan 16, 2024'
  countLabel: string; // e.g., '3 activities'
  items: ActivityLogItemData[];
}

export const ActivityLogSection = ({
  section,
}: {
  section: ActivityLogSectionData;
}) => {
  return (
    <VStack className="px-4">
      <HStack className="items-center justify-between my-2">
        <Box className="px-3 py-1 rounded-full bg-white border border-[#00AECC]">
          <Text className="text-[#00AECC] text-xs font-dm-sans-medium">
            {section.dateLabel}
          </Text>
        </Box>
        <Text className="text-typography-500 text-xs">
          {section.countLabel}
        </Text>
      </HStack>

      <VStack className="ml-2" space="sm">
        {section.items.map((item, idx) => (
          <HStack key={item.id} space="md" className="items-stretch">
            {/* timeline dot/line */}
            <VStack className="items-center">
              <Box className="w-2 h-2 rounded-full bg-background-400 mt-3" />
              {idx !== section.items.length - 1 ? (
                <Box className="w-px flex-1 bg-background-200" />
              ) : (
                <Box className="w-px flex-1 bg-transparent" />
              )}
            </VStack>

            <Box className="flex-1 bg-white rounded-xl p-3 border border-background-200">
              <ActivityLogItem item={item} />
            </Box>
          </HStack>
        ))}
      </VStack>
    </VStack>
  );
};

export default ActivityLogSection;
