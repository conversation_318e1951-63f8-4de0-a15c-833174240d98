import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '@/components/ui/select';
import { Icon } from '@/components/ui/icon';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import type { ActivityType } from '@/data/api-client/activity-types';

interface ActivityTypeSelectorProps {
  activityTypes: ActivityType[];
  isLoading: boolean;
  value: string;
  onChange: (value: string) => void;
}

const ActivityTypeSelector: React.FC<ActivityTypeSelectorProps> = ({
  activityTypes,
  isLoading,
  value,
  onChange,
}) => {
  const selected = activityTypes.find(at => String(at.id) === value);

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        Type
      </Text>
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14">
          <SelectInput
            placeholder={isLoading ? 'Loading...' : 'Choose an activity type'}
            className="text-typography-700 flex-1 text-base"
            value={selected?.name || ''}
          />
          <Icon as={() => <ArrowDown2 size={20} color="#9CA3AF" />} />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView className="max-h-[400px]">
              {activityTypes.map(at => (
                <SelectItem key={String(at.id)} label={at.name} value={String(at.id)} />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

export default ActivityTypeSelector;


