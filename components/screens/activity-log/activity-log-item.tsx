import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { Clock } from 'lucide-react-native';

export interface ActivityLogItemData {
  id: string;
  title: string;
  note?: string;
  timeLabel: string; // e.g., '9:00PM'
  metaBadge?: string; // e.g., '20 mins', '8 sets'
}

export const ActivityLogItem = ({ item }: { item: ActivityLogItemData }) => {
  return (
    <VStack className="flex-1">
      <HStack className="items-center justify-between">
        <Text className="text-typography-900 font-dm-sans-bold">
          {item.title}
        </Text>
        <HStack className="items-center" space="xs">
          <Icon as={Clock} size="sm" className="text-typography-500" />
          <Text className="text-typography-600 text-xs">{item.timeLabel}</Text>
          {item.metaBadge && (
            <Box className="px-2 py-1 rounded-md bg-[#E9F7EF] border border-[#27AE60]">
              <Text className="text-[#27AE60] text-xs">{item.metaBadge}</Text>
            </Box>
          )}
        </HStack>
      </HStack>
      {item.note ? (
        <Text className="text-typography-600 text-xs mt-1">{item.note}</Text>
      ) : null}
    </VStack>
  );
};

export default ActivityLogItem;
