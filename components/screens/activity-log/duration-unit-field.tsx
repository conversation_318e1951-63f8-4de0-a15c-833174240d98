import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Input, InputField } from '@/components/ui/input';
import type { ActivityType } from '@/data/api-client/activity-types';

interface DurationUnitFieldProps {
  value: string;
  onChange: (value: string) => void;
  selectedActivityType: ActivityType | undefined;
}

const DurationUnitField: React.FC<DurationUnitFieldProps> = ({
  value,
  onChange,
  selectedActivityType,
}) => {
  const unit = selectedActivityType?.unit || '';

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {unit ? `${unit}` : 'Duration/Unit'}
      </Text>
      <Input className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14">
        <InputField
          placeholder={unit ? `E.g. 30 ${unit}` : 'E.g. 30 mins, 5km, 10 reps'}
          value={value}
          onChangeText={onChange}
          className="text-typography-700 flex-1 text-base"
        />
      </Input>
    </VStack>
  );
};

export default DurationUnitField;
