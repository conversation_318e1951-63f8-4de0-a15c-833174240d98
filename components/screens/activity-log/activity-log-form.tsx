import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { X } from 'lucide-react-native';
import { ScrollView } from '@/components/ui/scroll-view';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import { useActivityLogForm } from './useActivityLogForm';
import { useActivityTypes } from '@/data/screens/activity-log/queries/useActivityTypes';
import ActivityTypeSelector from './activity-type-selector';
import TimerSelect from './timer-select';
import DurationUnitField from './duration-unit-field';
import DateFieldComponent from '@/components/shared/filter/date-field';

interface ActivityLogFormProps {
  onClose: () => void;
}

const ActivityLogForm: React.FC<ActivityLogFormProps> = ({ onClose }) => {
  const { data: activityTypes = [], isLoading: isLoadingTypes } =
    useActivityTypes();
  const { form, onSubmit } = useActivityLogForm(onClose);

  const selectedActivityType = activityTypes.find(
    at => String(at.id) === form.state.values.activityTypeId
  );

  const handleSubmit = async () => {
    await onSubmit();
  };

  return (
    <SafeAreaView className="absolute inset-0 z-50 flex-1 bg-white">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 items-center" space="md">
          <Pressable
            onPress={onClose}
            className="w-10 h-10 items-center justify-center rounded-full bg-background-100"
          >
            <Icon as={X} size="lg" className="text-typography-900" />
          </Pressable>
          <Text className="text-lg font-dm-sans-bold text-typography-900">
            Add new activity
          </Text>
        </HStack>

        {/* Form */}
        <ScrollView
          className="flex-1 px-4"
          showsVerticalScrollIndicator={false}
        >
          <VStack space="lg" className="pb-6">
            {/* Activity Type */}
            <form.Field name="activityTypeId">
              {field => (
                <ActivityTypeSelector
                  activityTypes={activityTypes}
                  isLoading={isLoadingTypes}
                  value={field.state.value}
                  onChange={field.handleChange}
                />
              )}
            </form.Field>

            {/* Date */}
            <form.Field name="date">
              {field => (
                <DateFieldComponent
                  field={{ type: 'date', key: 'date', label: 'Date' }}
                  value={field.state.value}
                  onChange={(value) => field.handleChange(value || new Date())}
                />
              )}
            </form.Field>

            {/* Time */}
            <form.Field name="time">
              {field => (
                <TimerSelect
                  value={field.state.value}
                  onChange={field.handleChange}
                  label="Time"
                />
              )}
            </form.Field>

            {/* Duration/Unit */}
            <form.Field name="quantity">
              {field => (
                <DurationUnitField
                  value={field.state.value}
                  onChange={field.handleChange}
                  selectedActivityType={selectedActivityType}
                />
              )}
            </form.Field>

            {/* Comment/Notes */}
            <form.Field name="notes">
              {field => (
                <VStack space="sm" className="w-full">
                  <Text className="text-typography-700 font-dm-sans-medium text-base">
                    Comment
                  </Text>
                  <Input className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4 min-h-20">
                    <InputField
                      placeholder="Add notes about your activity"
                      value={field.state.value}
                      onChangeText={field.handleChange}
                      className="text-typography-700 flex-1 text-base"
                      multiline
                      textAlignVertical="top"
                    />
                  </Input>
                </VStack>
              )}
            </form.Field>
          </VStack>
        </ScrollView>

        {/* Submit Button */}
        <VStack className="px-4 pb-6">
          <form.Subscribe
            selector={s => ({ values: s.values, isSubmitting: s.isSubmitting })}
          >
            {({ values, isSubmitting: submitting }) => {
              const disabled = !(
                values.activityTypeId && values.quantity?.trim().length > 0
              );
              return (
                <Button
                  variant="solid"
                  size="lg"
                  className={`w-full rounded-full py-4 ${disabled || submitting ? 'bg-background-200' : 'bg-[#00BFE0]'}`}
                  disabled={disabled || submitting}
                  onPress={handleSubmit}
                >
                  <ButtonText
                    className={`${disabled || submitting ? 'text-typography-600' : 'text-white'} font-dm-sans-bold text-base`}
                  >
                    {submitting ? 'Adding activity…' : 'Add activity'}
                  </ButtonText>
                </Button>
              );
            }}
          </form.Subscribe>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default ActivityLogForm;
