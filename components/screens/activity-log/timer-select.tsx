import React, { useMemo } from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '@/components/ui/select';
import { Icon } from '@/components/ui/icon';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import { format } from 'date-fns';

export type TimerSelectProps = {
  value: string; // 'HH:mm'
  onChange: (value: string) => void;
  label?: string;
  stepMinutes?: 5 | 10 | 15;
};

const TimerSelect: React.FC<TimerSelectProps> = ({
  value,
  onChange,
  label = 'Time',
  stepMinutes = 5,
}) => {
  const timeSlots = useMemo(() => {
    const list: string[] = [];
    const step = stepMinutes ?? 5;
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += step) {
        const hh = hour.toString().padStart(2, '0');
        const mm = minute.toString().padStart(2, '0');
        list.push(`${hh}:${mm}`);
      }
    }
    return list;
  }, [stepMinutes]);

  const display = useMemo(() => {
    if (!value) return '';
    const [h, m] = value.split(':');
    const d = new Date();
    d.setHours(Number(h), Number(m));
    return format(d, 'h:mma');
  }, [value]);

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {label}
      </Text>
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14">
          <SelectInput
            placeholder={'Select time'}
            className="text-typography-700 flex-1 text-base"
            value={display}
          />
          <Icon as={() => <ArrowDown2 size={20} color="#9CA3AF" />} />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView className="max-h-[400px]">
              {timeSlots.map(t => (
                <SelectItem
                  key={t}
                  label={(() => {
                    const [h, m] = t.split(':');
                    const d = new Date();
                    d.setHours(Number(h), Number(m));
                    return format(d, 'h:mma');
                  })()}
                  value={t}
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

export default TimerSelect;
