import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronLeft, SlidersHorizontal } from 'lucide-react-native';
import { router } from 'expo-router';

export const ActivityLogHeader = () => {
  return (
    <HStack className="items-center justify-between px-4 py-3 bg-white border-b border-background-100">
      <HStack className="items-center" space="md">
        <Pressable onPress={() => router.back()} className="w-10 h-10 rounded-full bg-gray-100 items-center justify-center">
          <Icon as={ChevronLeft} size="lg" className="text-typography-900" />
        </Pressable>
        <Text className="text-xl font-dm-sans-bold text-typography-900">Activity log</Text>
      </HStack>
      <Pressable className="w-10 h-10 rounded-full bg-gray-100 items-center justify-center">
        <Icon as={SlidersHorizontal} size="lg" className="text-typography-900" />
      </Pressable>
    </HStack>
  );
};

export default ActivityLogHeader;

