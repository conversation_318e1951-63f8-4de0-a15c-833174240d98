import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { useCreateActivityLog } from '@/data/screens/activity-log/queries/useCreateActivityLog';
import { ToastManager } from '@/components/shared/toast/ToastManager';
import { format } from 'date-fns';
import { router } from 'expo-router';

const formSchema = z
  .object({
    activityTypeId: z.string().min(1, 'Select an activity type'),
    date: z.date({
      required_error: 'Date is required',
    }),
    time: z.string().min(1, 'Time is required'),
    quantity: z.string().trim().min(1, 'Duration/Unit is required'),
    notes: z.string().optional(),
  })
  .refine(
    data => {
      // Validate that quantity can be converted to a number
      const quantityNum = Number(data.quantity);
      return !isNaN(quantityNum) && quantityNum > 0;
    },
    {
      message: 'Quantity must be a valid number',
      path: ['quantity'],
    }
  );

export const useActivityLogForm = (onSuccessClose?: () => void) => {
  const createActivityLog = useCreateActivityLog(() => {
    onSuccessClose?.();
  });

  const form = useForm({
    defaultValues: {
      activityTypeId: '',
      date: new Date(),
      time: format(new Date(), 'HH:mm'),
      quantity: '',
      notes: '',
    },
    onSubmit: async ({ value }) => {
      try {
        // Merge selected date and time into a single entry_date string (YYYY-MM-DD HH:mm:ss)
        const [hours, minutes] = value.time.split(':');
        const entryDate = new Date(value.date);
        entryDate.setHours(Number(hours), Number(minutes), 0, 0);
        const entry_date = format(entryDate, 'yyyy-MM-dd HH:mm:ss');

        const resp = await createActivityLog.mutateAsync({
          activity_type_id: Number(value.activityTypeId),
          quantity: Number(value.quantity),
          notes: value.notes || undefined,
          entry_date,
        });

        if (resp?.success) {
          form.reset();
          ToastManager.show('Activity logged successfully!', 'success');
          router.replace('/activity-log');
        } else {
          ToastManager.show(resp?.message || 'Failed to log activity', 'error');
        }
      } catch (error) {
        ToastManager.show('An error occurred while logging activity', 'error');
      }
    },
    validators: {
      onChange: formSchema,
    },
  });

  return {
    form,
    isSubmitting: createActivityLog.isPending,
    onSubmit: () => form.handleSubmit(),
  };
};
