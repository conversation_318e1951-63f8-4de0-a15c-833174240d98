import React from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { ActivityEquipment } from '@/data/screens/activities/types';
import { CardSkeletonLoader } from '../../shared/card-skeleton';
import { EmptySearchState } from '@/components/shared/empty-search';
import { EmptyState } from '../classes/empty-state';
import { uniqueId } from 'lodash/fp';
import { useFavoriteMutation } from '@/data/screens/common/queries/useFavoriteMutation';
import { ActivityEquipmentCard } from './activity-equipment-card';

interface ActivityListProps {
  activities: ActivityEquipment[];
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  selectedDate: Date;
  onRefresh: () => void;
  onClearSearch: () => void;
}

export const ActivityList = ({
  activities,
  isLoading,
  isRefreshing,
  searchTerm,
  selectedDate,
  onRefresh,
  onClearSearch,
}: ActivityListProps) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();

  if (isLoading) {
    return <CardSkeletonLoader />;
  }

  if (searchTerm && activities.length === 0) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (!searchTerm && activities.length === 0) {
    return (
      <EmptyState
        title="No activities available"
        subtitle="There are no activities available for the selected date and location."
      />
    );
  }

  return (
    <FlatList
      data={activities}
      keyExtractor={item => uniqueId(String(item.id))}
      renderItem={({ item }) => (
        <ActivityEquipmentCard
          equipment={item}
          selectedDate={selectedDate}
          onFavoritePress={() =>
            favoriteMutation({
              type: 'equipment',
              item_id: item.id,
            })
          }
        />
      )}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 12,
        paddingBottom: 400,
      }}
    />
  );
};
