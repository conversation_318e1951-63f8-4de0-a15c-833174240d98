import { ActionButton } from "@/components/shared/status-actionsheet";
import { ActivityEquipment } from "@/data/screens/activities/types";

export const getEquipmentStatus = (equipment: ActivityEquipment) => {
 
    const hasAvailableSlots =
      equipment.available_time_slots && equipment.available_time_slots.length > 0;
  
    const isReserved = !!equipment.current_user_reservation;
    const isStationary = equipment.current_status === 'Full';
  
    if (isReserved) {
      return {
        type: 'reserved',
        statusText: 'Reserved',
        statusColor: 'text-green-600',
        statusBg: 'bg-green-100',
        statusBadgeText: 'Reserved',
        statusBadgeColor: 'text-green-600',
        statusBadgeBg: 'bg-green-100',
        availabilityText: null,
        availabilityColor: null,
        buttonVariant: 'cancel_reservation' as const,
        buttonText: 'Cancel reservation',
        buttonDisabled: false,
        showExpandButton: false,
      };
    }
  
    if (hasAvailableSlots) {
      const nextSlot = equipment.available_time_slots[0];
      return {
        type: 'available',
        statusText: null,
        statusColor: null,
        statusBg: null,
        statusBadgeText: `Available @ ${nextSlot.time_label}`,
        statusBadgeColor: 'text-[#00BFE0]',
        statusBadgeBg: 'bg-[#E5F9FD]',
        availabilityText: `Available @ ${nextSlot.time_label}`,
        availabilityColor: 'text-[#00BFE0]',
        buttonVariant: 'reserve' as const,
        buttonText: 'Reserve',
        buttonDisabled: false,
        showExpandButton: true,
      };
    }
  
    if (equipment.current_status === 'Full' || isStationary) {
      return {
        type: 'full',
        statusText: 'Full',
        statusColor: 'text-gray-600',
        statusBg: 'bg-gray-100',
        statusBadgeText: 'Full',
        statusBadgeColor: 'text-gray-600',
        statusBadgeBg: 'bg-gray-100',
        availabilityText: null,
        availabilityColor: null,
        buttonVariant: 'event_full' as const,
        buttonText: 'Full',
        buttonDisabled: true,
        showExpandButton: false,
      };
    }
  
    // For equipment with next available time
    if (equipment.next_available_time_slot) {
      return {
        type: 'next_available',
        statusText: null,
        statusColor: null,
        statusBg: null,
        statusBadgeText: `Next available: ${equipment.next_available_time_slot.time_label}`,
        statusBadgeColor: 'text-[#00BFE0]',
        statusBadgeBg: 'bg-[#E5F9FD]',
        availabilityText: `Next available: ${equipment.next_available_time_slot.time_label}`,
        availabilityColor: 'text-[#00BFE0]',
        buttonVariant: 'event_cancelled' as const,
        buttonText: 'Not available',
        buttonDisabled: true,
        showExpandButton: false,
      };
    }
  
    return {
      type: 'unavailable',
      statusText: null,
      statusColor: null,
      statusBg: null,
      statusBadgeText: null,
      statusBadgeColor: null,
      statusBadgeBg: null,
      availabilityText: null,
      availabilityColor: null,
      buttonVariant: 'event_cancelled' as const,
      buttonText: 'Not available',
      buttonDisabled: true,
      showExpandButton: false,
    };
  };
  
  export const getActivityActions = (equipment: ActivityEquipment): ActionButton[] => [
    {
      label: 'Make another reservation',
      onPress: () => {},
      variant: 'primary',
    },
    {
      label: 'Add to calendar',
      onPress: () => {},
      variant: 'secondary',
    },
    {
      label: 'Share with friends',
      onPress: () => {},
      variant: 'outline',
    },
  ];