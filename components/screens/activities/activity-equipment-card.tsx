import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Heart, ChevronDown, ChevronUp } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';
// Image removed per design
import { truncateText } from '@/utils/common';
// Removed initials fallback per design
import { ActivityEquipment } from '@/data/screens/activities/types';
import { StatusButton } from '@/components/shared/status-button';
import { parseTime } from '@/utils/time-utils';
import { format } from 'date-fns';

import {
  StatusActionSheet,
} from '@/components/shared/status-actionsheet';
import { ActivityEquipmentForm } from '@/components/screens/activities/activity-equipment-form';
import { getActivityActions, getEquipmentStatus } from './activity-card.utils';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';

interface ActivityEquipmentCardProps {
  equipment: ActivityEquipment;
  selectedDate: Date;
  onFavoritePress?: () => void;
}

const formatReservationTime = (startTime: string, endTime: string): string => {
  const start = parseTime(startTime);
  const end = parseTime(endTime);
  
  if (!start || !end) return '';
  
  const startFormatted = format(start, 'h:mm a');
  const endFormatted = format(end, 'h:mm a');
  
  return `${startFormatted} - ${endFormatted}`;
};


export const ActivityEquipmentCard = ({
  equipment,
  selectedDate,
  onFavoritePress,
}: ActivityEquipmentCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [actionSheetError, setActionSheetError] = useState<Error | null>(null);


  const { mutate: cancelReservation, isPending } =
    useCancelReservation();

  const statusConfig = getEquipmentStatus(equipment);


  const handleCardPress = () => {
    if (statusConfig.showExpandButton) setIsExpanded(prev => !prev);
  };

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPress={handleCardPress}
      className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
    >
      <VStack space="sm">
        <VStack className="flex-1" space="xs">
          <HStack className="items-center justify-between">
            <HStack className="items-center flex-1" space="sm">
              <Text
                className="text-[#00697B] font-dm-sans-bold text-base shrink"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {equipment.name}
              </Text>
               {statusConfig.statusBadgeText ? (
                 <Box className={`px-2 py-1 rounded-full ${statusConfig.statusBadgeBg} shrink-0`}>
                   <Text
                     className={`text-xs font-dm-sans-medium ${statusConfig.statusBadgeColor}`}
                   >
                     {statusConfig.statusBadgeText}
                   </Text>
                 </Box>
               ) : null}
            </HStack>
            <HStack space="sm" className="items-center">
              <TouchableOpacity
                className="ml-3"
                onPress={e => {
                  e.stopPropagation();
                  onFavoritePress?.();
                }}
              >
                <Icon
                  as={Heart}
                  size="sm"
                  className={
                    equipment.is_favourite
                      ? 'text-error-500 fill-error-500'
                      : 'text-typography-400'
                  }
                />
              </TouchableOpacity>
            </HStack>
          </HStack>
          <HStack className="items-center justify-between">
            <VStack space="xs" className="flex-1">
              <Text className="text-xs font-dm-sans-regular text-typography-600">
                {truncateText(equipment.gym_name, 25)}
              </Text>
              {statusConfig.type === 'reserved' && equipment.current_user_reservation && (
                <Text className="text-xs font-dm-sans-medium text-green-600">
                  Reserved for: {formatReservationTime(
                    equipment.current_user_reservation.start_time,
                    equipment.current_user_reservation.end_time
                  )}
                </Text>
              )}
            </VStack>
            {statusConfig.showExpandButton && (
              <TouchableOpacity onPress={() => setIsExpanded(!isExpanded)}>
                <Icon
                  as={isExpanded ? ChevronUp : ChevronDown}
                  size="sm"
                  className="text-typography-600"
                />
              </TouchableOpacity>
            )}
          </HStack>
        </VStack>

        {statusConfig.type === 'reserved' && (
          <HStack className="justify-end items-center">
            <StatusButton
              variant={statusConfig.buttonVariant}
              text={statusConfig.buttonText}
              disabled={statusConfig.buttonDisabled}
              isLoading={isPending}
              size="sm"
              onPress={() => cancelReservation(equipment.current_user_reservation?.id as number)}
            />
          </HStack>
        )}

        {statusConfig.showExpandButton && isExpanded && (
          <VStack
            space="md"
            className="mt-4 pt-4 border-t border-background-200"
          >
            <ActivityEquipmentForm
              equipment={equipment}
              selectedDate={selectedDate}
              onSuccess={() => {
                setActionSheetError(null);
                setShowActionSheet(true);
              }}
              onError={(error: Error) => {
                setActionSheetError(error);
                setShowActionSheet(true);
              }}
            />
          </VStack>
        )}

        <StatusActionSheet
          isOpen={showActionSheet}
          onClose={() => setShowActionSheet(false)}
          status={actionSheetError ? 'error' : 'success'}
          title={actionSheetError ? 'Error occurred' : 'Reservation made'}
          description={
            actionSheetError?.message ??
            'Your activity reservation was successful!'
          }
          actions={actionSheetError ? [] : getActivityActions(equipment)}
        />
      </VStack>
    </TouchableOpacity>
  );
};
