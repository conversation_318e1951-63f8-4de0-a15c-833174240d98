import React, { useEffect, useState } from 'react';
import { VStack } from '@/components/ui/vstack';
import { TimeSlotSelector } from '@/components/shared/time-slot-selector';
import { DurationSelector } from '@/components/shared/duration-selector';
import { NumberAttendingSelector } from '../../shared/number-attending-selector';
import { StatusButton } from '@/components/shared/status-button';
import { useForm } from '@tanstack/react-form';
import { useActivitiesDuration } from '@/data/screens/activities/queries/useActivitiesDuration';
import { ActivityEquipment } from '@/data/screens/activities/types';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';
import { useReserveMutation } from '@/data/screens/common/mutations/useReserveMutation';

interface ActivityEquipmentFormProps {
  equipment: ActivityEquipment;
  selectedDate?: Date;
  onSuccess: () => void;
  onError: (error: Error) => void;
}

export const ActivityEquipmentForm = ({
  equipment,
  selectedDate,
  onSuccess,
  onError,
}: ActivityEquipmentFormProps) => {
  const { mutate: reserveActivity, isPending: isReserving } =
    useReserveMutation();

  const form = useForm({
    defaultValues: {
      duration: '',
      startTime: '',
      attendingPersons: '',
    },
    onSubmit: async ({ value }) => {
      if (!value.duration || !value.startTime) return;

      const dateStr = format(
        selectedDate ?? new Date(),
        DATE_FORMAT.YEAR_MONTH_DAY
      );
      reserveActivity(
        {
          equipment_id: equipment.id,
          date: dateStr,
          time: value.startTime,
          duration: parseInt(value.duration),
          type: 'equipment',
          attending_persons: value.attendingPersons
            ? parseInt(value.attendingPersons)
            : 1,
        },
        {
          onSuccess: () => {
            onSuccess();
            form.reset();
          },
          onError: error => {
            onError(error as Error);
          },
        }
      );
    },
  });

  const [selectedStartTime, setSelectedStartTime] = useState('');

  useEffect(() => {
    if (selectedStartTime) {
      form.setFieldValue('duration', '');
    }
  }, [selectedStartTime]);

  const shouldShowAttendingSelector =
    (equipment?.max_attending_persons ?? 1) > 1;

  const { data: dynamicDurations = [], isFetching: isFetchingDurations } =
    useActivitiesDuration(equipment.id, selectedStartTime);

  return (
    <VStack space="md">
      <form.Field name="startTime">
        {field => (
          <TimeSlotSelector
            value={field.state.value}
            onChange={val => {
              field.handleChange(val);
              setSelectedStartTime(val);
            }}
            timeSlots={equipment.available_time_slots || []}
            placeholder="02:00PM"
          />
        )}
      </form.Field>

      <form.Field name="duration">
        {field => (
          <DurationSelector
            value={field.state.value}
            onChange={field.handleChange}
            durations={selectedStartTime ? dynamicDurations : []}
            placeholder="30 minutes"
            disabled={!selectedStartTime || isFetchingDurations}
            isLoading={isFetchingDurations}
          />
        )}
      </form.Field>

      {shouldShowAttendingSelector ? (
        <form.Field name="attendingPersons">
          {field => (
            <NumberAttendingSelector
              value={field.state.value}
              onChange={field.handleChange}
              options={equipment.attending_persons || []}
              placeholder="Number attending"
            />
          )}
        </form.Field>
      ) : null}

      <form.Subscribe
        selector={state => [
          state.values.duration,
          state.values.startTime,
          state.values.attendingPersons,
        ]}
      >
        {([duration, startTime, attendingPersons]) => (
          <StatusButton
            variant="reserve"
            text="Reserve"
            disabled={
              !duration ||
              !startTime ||
              (shouldShowAttendingSelector && !attendingPersons)
            }
            isLoading={isReserving}
            size="sm"
            onPress={form.handleSubmit}
          />
        )}
      </form.Subscribe>
    </VStack>
  );
};
