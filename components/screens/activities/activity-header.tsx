import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';

export const ActivityHeader = () => {
  return (
    <HStack className="items-center justify-between px-4 py-3 bg-white">
      <HStack className="items-center" space="md">
        <Pressable onPress={() => router.back()} className="p-2 -ml-2">
          <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
        </Pressable>
        <Text className="text-xl font-dm-sans-bold text-typography-900">
          Activity
        </Text>
      </HStack>
    </HStack>
  );
};
