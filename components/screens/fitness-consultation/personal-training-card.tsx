import React from 'react';
import { Box } from '@/components/ui/box';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { MapPinIcon } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';
import { AppointmentType } from '@/data/screens/appointments/types';
import { router } from 'expo-router';
import { truncateText } from '@/utils/common';
import { useCanBookTrainer } from '@/hooks/useUserPasses';

export const AppointmentCard = ({
  name,
  room_name,
  gym_name,
  id,
  duration,
}: AppointmentType) => {
  const { canBook } = useCanBookTrainer();

  const handlePressTrainer = () => {
    router.push({
      pathname: '/trainers',
      params: { sessionId: id },
    });
  };

  const handleRequestInfoPress = () => {
    router.push('/request-info');
  };

  const handleBookPress = () => {
    router.push({
      pathname: '/book-appointment',
      params: { sessionId: id, appointmentName: name },
    });
  };

  return (
    <Box className="bg-background-0 rounded-2xl p-4 mx-4 mb-3 shadow-soft-1 border border-outline-100">
      <VStack className="gap-4">
        <VStack className="gap-2">
          <HStack className="items-center gap-3">
            <Text className="text-sm font-semibold text-primary-400">
              {truncateText(name, 25)}
            </Text>
            <Text className="text-typography-500 text-sm font-medium">
              {duration}
            </Text>
          </HStack>
          <HStack className="items-center gap-1">
            <Icon as={MapPinIcon} size="sm" className="text-typography-400" />
            <Text className="text-typography-500 text-xs">
              {truncateText(`${room_name}, ${gym_name}`, 40)}
            </Text>
          </HStack>
        </VStack>
        <HStack space="xs">
          <Button
            variant="outline"
            size="xs"
            className="flex-1 rounded-full border-black"
            onPress={handlePressTrainer}
          >
            <ButtonText className="font-dm-sans-medium text-black">
              View trainers
            </ButtonText>
          </Button>

          <Button
            variant="outline"
            size="xs"
            className="flex-1 rounded-full border-black"
            onPress={handleRequestInfoPress}
          >
            <ButtonText className="text-black font-dm-sans-medium">
              Request info
            </ButtonText>
          </Button>

          <Button
            disabled={!canBook}
            variant="solid"
            size="xs"
            className="flex-1 rounded-full bg-[#E6F9FC]"
            onPress={handleBookPress}
          >
            <ButtonText className="text-[#00697B] font-dm-sans-medium font-bold">
              Book
            </ButtonText>
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};
