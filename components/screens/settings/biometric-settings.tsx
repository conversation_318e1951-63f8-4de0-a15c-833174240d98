import React, { useState } from 'react';
import { Alert } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Switch } from '@/components/ui/switch';
import { useSession } from '@/modules/login/auth-provider';
import { Fingerprint, Shield, AlertCircle } from 'lucide-react-native';
import { Alert as UIAlert, AlertText, AlertIcon } from '@/components/ui/alert';

const BiometricSettings = () => {
  const { biometricState, disableBiometric } = useSession();
  const [isEnabling] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getBiometricTypeName = () => {
    if (biometricState.supportedTypes.includes(1)) {
      return 'Face ID';
    }
    if (biometricState.supportedTypes.includes(2)) {
      return 'Touch ID';
    }
    return 'Biometric';
  };

  const handleToggleBiometric = async () => {
    setError(null);

    if (biometricState.isEnabled) {
      // Disable biometric
      const success = await disableBiometric();
      if (!success) {
        setError('Failed to disable biometric authentication');
      }
    } else {
      // Enable biometric - need to prompt for credentials
      Alert.alert(
        `Enable ${getBiometricTypeName()}`,
        `To enable ${getBiometricTypeName()} authentication, you'll need to sign in with your email and password first.`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Continue',
            onPress: () => promptForCredentials(),
          },
        ]
      );
    }
  };

  const promptForCredentials = () => {
    // For now, we'll show an alert asking user to sign in normally first
    // In a real app, you might want to show a modal with email/password fields
    Alert.alert(
      'Sign In Required',
      'Please sign in with your email and password first, then you can enable biometric authentication from the login screen after a successful login.',
      [{ text: 'OK' }]
    );
  };

  if (biometricState.isLoading) {
    return (
      <VStack space="md" className="p-4">
        <Text className="font-semibold">Biometric Authentication</Text>
        <Text className="text-gray-600">Loading biometric settings...</Text>
      </VStack>
    );
  }

  if (!biometricState.isSupported) {
    return (
      <VStack space="md" className="p-4">
        <Text className="font-semibold">Biometric Authentication</Text>
        <UIAlert action="info" className="mb-3">
          <AlertIcon as={AlertCircle} />
          <AlertText className="text-sm">
            Biometric authentication is not supported on this device.
          </AlertText>
        </UIAlert>
      </VStack>
    );
  }

  if (!biometricState.isEnrolled) {
    return (
      <VStack space="md" className="p-4">
        <Text className="font-semibold">Biometric Authentication</Text>
        <UIAlert action="warning" className="mb-3">
          <AlertIcon as={AlertCircle} />
          <AlertText className="text-sm">
            No biometric data is enrolled on this device. Please set up{' '}
            {getBiometricTypeName()} in your device settings first.
          </AlertText>
        </UIAlert>
      </VStack>
    );
  }

  return (
    <VStack space="md" className="p-4">
      <Text className="font-semibold">Biometric Authentication</Text>

      {error && (
        <UIAlert action="error" className="mb-3">
          <AlertIcon as={AlertCircle} />
          <AlertText className="text-sm">{error}</AlertText>
        </UIAlert>
      )}

      <VStack
        space="sm"
        className="bg-white rounded-lg p-4 border border-gray-200"
      >
        <HStack className="items-center justify-between">
          <HStack className="items-center flex-1" space="sm">
            <Fingerprint className="text-[#00697B]" size={24} />
            <VStack className="flex-1">
              <Text className="font-medium">
                {getBiometricTypeName()} Login
              </Text>
              <Text className="text-sm text-gray-600">
                Use {getBiometricTypeName()} to sign in quickly and securely
              </Text>
            </VStack>
          </HStack>
          <Switch
            value={biometricState.isEnabled}
            onValueChange={handleToggleBiometric}
            disabled={isEnabling}
            trackColor={{ false: '#e5e5e5', true: '#00697B' }}
            thumbColor={biometricState.isEnabled ? '#ffffff' : '#f4f3f4'}
          />
        </HStack>
      </VStack>

      {biometricState.isEnabled && (
        <VStack
          space="sm"
          className="bg-green-50 rounded-lg p-4 border border-green-200"
        >
          <HStack className="items-center" space="sm">
            <Shield className="text-green-600" size={20} />
            <Text className="text-green-800 font-medium">
              {getBiometricTypeName()} is enabled
            </Text>
          </HStack>
          <Text className="text-sm text-green-700">
            You can now sign in using {getBiometricTypeName()} on the login
            screen.
          </Text>
        </VStack>
      )}
    </VStack>
  );
};

export default BiometricSettings;
