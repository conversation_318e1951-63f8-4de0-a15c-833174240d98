import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';

interface AccountMenuSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export const AccountMenuSection: React.FC<AccountMenuSectionProps> = ({
  title,
  children,
  className = '',
}) => {
  return (
    <VStack className="mb-6">
      <Text className="text-sm font-dm-sans-medium text-typography-500 uppercase tracking-wide px-4 mb-2">
        {title}
      </Text>
      <VStack
        className={`bg-white rounded-2xl mx-4 overflow-hidden shadow-sm ${className}`}
      >
        {children}
      </VStack>
    </VStack>
  );
};
