import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ChevronRight } from 'lucide-react-native';

interface AccountMenuItemProps {
  icon: React.ElementType;
  title: string;
  onPress?: () => void;
  isLast?: boolean;
  showChevron?: boolean;
  rightElement?: React.ReactNode;
  iconColor?: string;
  titleColor?: string;
}

export const AccountMenuItem: React.FC<AccountMenuItemProps> = ({
  icon,
  title,
  onPress,
  isLast = false,
  showChevron = true,
  rightElement,
  iconColor = 'text-primary-500',
  titleColor = 'text-typography-700',
}) => {
  return (
    <VStack>
      <Pressable
        onPress={onPress}
        className="flex-row items-center justify-between px-4 py-4 bg-white active:bg-background-50"
      >
        <HStack space="md" className="items-center flex-1">
          <Icon as={icon} size="lg" className={iconColor} />
          <Text className={`text-base font-dm-sans-regular flex-1 ${titleColor}`}>
            {title}
          </Text>
        </HStack>
        {rightElement || (showChevron && (
          <Icon as={ChevronRight} size="md" className="text-typography-400" />
        ))}
      </Pressable>
      {!isLast && <VStack className="h-px bg-background-100 mx-4" />}
    </VStack>
  );
};
