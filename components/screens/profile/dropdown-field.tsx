import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { Box } from '@/components/ui/box';
import { ChevronDown } from 'lucide-react-native';

interface DropdownFieldProps {
  label: string;
  value: string;
  onPress: () => void;
  placeholder?: string;
}

export const DropdownField: React.FC<DropdownFieldProps> = ({
  label,
  value,
  onPress,
  placeholder = 'Select an option',
}) => {
  return (
    <VStack className="mb-4">
      <Text className="text-sm font-dm-sans-medium text-typography-500 mb-2">
        {label}
      </Text>
      <Pressable onPress={onPress}>
        <Box className="bg-white rounded-xl border border-outline-200">
          <HStack className="items-center justify-between px-4 py-4">
            <Text 
              className={`text-base font-dm-sans-regular flex-1 ${
                value ? 'text-typography-700' : 'text-typography-400'
              }`}
            >
              {value || placeholder}
            </Text>
            <Icon as={ChevronDown} size="md" className="text-typography-400" />
          </HStack>
        </Box>
      </Pressable>
    </VStack>
  );
};
