import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { Box } from '@/components/ui/box';
import { Avatar, AvatarFallbackText, AvatarImage } from '@/components/ui/avatar';
import { Edit } from 'lucide-react-native';

interface ProfileAvatarProps {
  imageUri?: string | null;
  initials: string;
  onEditPress?: () => void;
  size?: 'xl' | '2xl';
  showEditButton?: boolean;
}

export const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  imageUri,
  initials,
  onEditPress,
  size = '2xl',
  showEditButton = true,
}) => {
  return (
    <VStack className="items-center">
      <Box className="relative">
        <Avatar size={size} className="border-4 border-white">
          {imageUri ? (
            <AvatarImage source={{ uri: imageUri }} />
          ) : (
            <AvatarFallbackText>
              {initials}
            </AvatarFallbackText>
          )}
        </Avatar>
        {showEditButton && (
          <Pressable 
            className="absolute -bottom-1 -right-1 bg-primary-500 rounded-full p-2"
            onPress={onEditPress}
          >
            <Icon as={Edit} size="sm" className="text-white" />
          </Pressable>
        )}
      </Box>
    </VStack>
  );
};
