import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { Input, InputField } from '@/components/ui/input';
import { Box } from '@/components/ui/box';
import { Edit } from 'lucide-react-native';

interface ProfileFieldProps {
  label: string;
  value: string;
  onChangeText?: (text: string) => void;
  editable?: boolean;
  placeholder?: string;
  showEditIcon?: boolean;
  onEditPress?: () => void;
}

export const ProfileField: React.FC<ProfileFieldProps> = ({
  label,
  value,
  onChangeText,
  editable = true,
  placeholder,
  showEditIcon = true,
  onEditPress,
}) => {
  return (
    <VStack className="mb-4">
      <Text className="text-sm font-dm-sans-medium text-typography-500 mb-2">
        {label}
      </Text>
      <Box className="bg-white rounded-xl border border-outline-200">
        <Input className="border-0">
          <InputField
            value={value}
            onChangeText={onChangeText}
            editable={editable}
            placeholder={placeholder}
            className="text-base font-dm-sans-regular text-typography-700 px-4 py-4"
          />
          {showEditIcon && editable && (
            <Pressable className="px-4 py-4" onPress={onEditPress}>
              <Icon as={Edit} size="md" className="text-typography-400" />
            </Pressable>
          )}
        </Input>
      </Box>
    </VStack>
  );
};
