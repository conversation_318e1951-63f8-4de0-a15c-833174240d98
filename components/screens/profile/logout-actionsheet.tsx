import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import { LogOut } from 'lucide-react-native';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';

interface LogoutActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const LogoutActionSheet: React.FC<LogoutActionSheetProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>
        
        <VStack className="w-full p-6 items-center space-y-4">
          {/* Logout Icon */}
          <Box className="w-16 h-16 bg-error-500 rounded-full items-center justify-center">
            <Icon as={LogOut} size="xl" className="text-white" />
          </Box>

          {/* Title */}
          <Text className="text-xl font-dm-sans-bold text-typography-900 text-center">
            Logout
          </Text>

          {/* Description */}
          <Text className="text-base font-dm-sans-regular text-typography-500 text-center">
            Are you sure you want to logout of this app?
          </Text>

          {/* Buttons */}
          <VStack className="w-full space-y-3 mt-6">
            <Button
              onPress={onClose}
              variant="outline"
              className="w-full rounded-full border-outline-300"
            >
              <ButtonText className="text-typography-700 font-dm-sans-medium">
                Stay on the app
              </ButtonText>
            </Button>

            <Button
              onPress={onConfirm}
              className="w-full bg-error-500 rounded-full"
            >
              <ButtonText className="text-white font-dm-sans-medium">
                Logout
              </ButtonText>
            </Button>
          </VStack>
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
