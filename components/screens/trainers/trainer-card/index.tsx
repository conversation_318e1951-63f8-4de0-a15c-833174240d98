import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Button, ButtonText } from '@/components/ui/button';
import { TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';
import { Trainer } from '@/data/screens/appointments/types';
import { getInitials } from '@/data/common/common.utils';
import { COLOR_CODES } from '@/constants/color-codes';

import { useCanBookTrainer } from '@/hooks/useUserPasses';

export const TrainerCard: React.FC<Trainer> = ({
  id,
  first_name,
  last_name,
  years_experience,
  specialties,
  profile_image,
}) => {
  const { canBook } = useCanBookTrainer();

  const handleCardPress = () => {
    router.push({
      pathname: '/trainer-details',
      params: { trainerId: id },
    });
  };

  const handleButtonPress = () => {
    if (canBook) {
      // Navigate to booking page with trainer pre-selected
      router.push({
        pathname: '/book-appointment',
        params: { trainerId: id },
      });
    } else {
      // Navigate to request info page
      router.push('/request-info');
    }
  };

  const fullName = `${first_name} ${last_name}`;

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <View
        className="bg-white rounded-2xl p-4 border border-background-200 mb-3"
        style={{
          shadowColor: '#000',
          shadowOpacity: 0.08,
          shadowRadius: 8,
          shadowOffset: { width: 0, height: 2 },
          elevation: 3,
        }}
      >
        <HStack space="md" className="items-start">
          {profile_image ? (
            <Avatar size="xl" className="w-20 h-20">
              <AvatarImage source={{ uri: profile_image }} alt={fullName} />
            </Avatar>
          ) : (
            <View
              className="w-20 h-20 rounded-full items-center justify-center"
              style={{ backgroundColor: COLOR_CODES.trainer.circle }}
            >
              <Text
                className={`text-[${COLOR_CODES.trainer.text}] text-xl font-dm-sans-bold`}
              >
                {getInitials(fullName)}
              </Text>
            </View>
          )}

          <VStack className="flex-1" space="xs">
            <HStack className="justify-between items-center">
              <Text
                numberOfLines={1}
                className="text-[#00889F] text-lg font-dm-sans-bold flex-1"
              >
                {fullName}
              </Text>
              <Button
                variant="solid"
                size="xs"
                className="rounded-full bg-[#E6F9FC] px-4 py-2"
                onPress={handleButtonPress}
              >
                <ButtonText className="text-[#00697B] font-dm-sans-medium text-xs">
                  {canBook ? 'Book' : 'Request Info'}
                </ButtonText>
              </Button>
            </HStack>

            <Text className="text-typography-500 text-xs font-dm-sans-regular">
              {years_experience} years of experience
            </Text>

            <VStack space="xs" className="mt-2">
              {/* First row - up to 2 specialties */}
              <HStack space="xs" className="flex-wrap">
                {specialties.slice(0, 2).map((specialty, index) => (
                  <View
                    key={index}
                    className="px-3 py-2 rounded-full border border-background-200"
                  >
                    <Text className="text-typography-600 text-xs font-dm-sans-regular">
                      {specialty.name}
                    </Text>
                  </View>
                ))}
              </HStack>

              {/* Second row - remaining specialties */}
              {specialties.length > 2 && (
                <HStack space="xs" className="flex-wrap">
                  {specialties.slice(2, 3).map((specialty, index) => (
                    <View
                      key={index + 2}
                      className="px-3 py-2 rounded-full border border-background-200"
                    >
                      <Text className="text-typography-600 text-xs font-dm-sans-regular">
                        {specialty.name}
                      </Text>
                    </View>
                  ))}

                  {specialties.length > 3 && (
                    <View className="px-3 py-2 rounded-full border border-background-200">
                      <Text className="text-typography-600 text-xs font-dm-sans-regular">
                        +{specialties.length - 3}
                      </Text>
                    </View>
                  )}
                </HStack>
              )}
            </VStack>
          </VStack>
        </HStack>
      </View>
    </TouchableOpacity>
  );
};
