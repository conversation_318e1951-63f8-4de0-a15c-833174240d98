import { addToCalendar } from '@/utils/calendar';
import { formatDate } from '@/data/common/common.utils';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { ActionButton } from '@/components/shared/status-actionsheet';

type ReserveActionsOptions = {
  isCalendarSynced?: boolean;
  onCalendarSynced?: () => void;
  setShowToast?: (show: boolean) => void;
};

export const reserveActions = (
  data?: ClassDetailsResponse,
  selectedDate?: Date | string,
  options?: ReserveActionsOptions
): ActionButton[] => {
  const handleAddToCalendar = async () => {
    if (!data) return;
    const date = formatDate(selectedDate);

    let rawId: string | number | undefined;
    const cur = data.current_user_reservation;
    if (cur && typeof cur === 'object') {
      const r = cur as Record<string, unknown>;
      const maybeId = (r.id ?? r.reservation_id) as unknown;
      if (typeof maybeId === 'string' || typeof maybeId === 'number') {
        rawId = maybeId;
      }
    }
    const reservationIdNum = Number(rawId ?? data.reservation_id);

    const eventId = await addToCalendar({
      title: data.name,
      startTime: data.start_time,
      endTime: data.end_time,
      date,
      location: [data.gym_name, data.room_name].filter(Boolean).join(', '),
      description: data.description,
      instructor: `${data.instructor_first_name} ${data.instructor_last_name}`,
      reservationId: Number.isFinite(reservationIdNum)
        ? reservationIdNum
        : undefined,
    });

    if (eventId) options?.onCalendarSynced?.();
  };

  return [
    {
      label: 'Make another reservation',
      onPress: () => options?.setShowToast?.(false),
      variant: 'primary',
    },
    {
      label: 'Add to calendar',
      onPress: () => {
        void handleAddToCalendar();
      },
      variant: 'secondary',
      isDisabled: !!options?.isCalendarSynced,
    },
    // {
    //   label: 'Share with friends',
    //   onPress: noop,
    //   variant: 'outline',
    // },
  ];
};
