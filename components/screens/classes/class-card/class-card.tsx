// import React from 'react';
// import { HStack } from '@/components/ui/hstack';
// import { VStack } from '@/components/ui/vstack';
// import { Text } from '@/components/ui/text';

// import { Box } from '@/components/ui/box';
// import { Heart } from 'lucide-react-native';
// import { Icon } from '@/components/ui/icon';
// import { router } from 'expo-router';
// import { TouchableOpacity } from 'react-native';
// import { ClassStatusButton } from './class-button';
// import { ClassDetailsResponse } from '@/data/screens/classes/types';
// import { formatDate, obtainDateFrame } from '@/data/common/common.utils';
// import {
//   obtainSpotsAvailable,
//   obtainStatus,
// } from '@/data/screens/classes/utils';
// import { Image } from '@/components/ui/image';
// import { truncateText } from '@/utils/common';
// import { Badge, BadgeText } from '@/components/ui/badge';

// export const ClassCard = (
//   data: ClassDetailsResponse & { selectedDate: Date }
// ) => {
//   const {
//     name,
//     class_type,
//     start_time,
//     end_time,
//     instructor_first_name,
//     instructor_last_name,
//     gym_name,
//     id,
//     images,
//     selectedDate,
//   } = data;

//   const handleCardPress = () =>
//     router.push({
//       pathname: '/(class-details)/[id]',
//       params: { id, date: formatDate(selectedDate) },
//     });

//   return (
//     <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
//       <VStack
//         space="sm"
//         className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
//       >
//         {/* Top row with image, title, status badge, and heart */}
//         <HStack space="sm" className="items-start">
//           <Image
//             source={{ uri: images[0] }}
//             className="w-12 h-12 rounded-lg"
//             alt={name}
//           />
//           <VStack className="flex-1" space="xs">
//             <HStack className="items-center justify-between">
//               <HStack className="items-center flex-1" space="sm">
//                 <Text
//                   className="text-[#00697B] font-dm-sans-bold text-base line-clamp-1 max-w-32"
//                   numberOfLines={2}
//                 >
//                   {truncateText(name, 20)}
//                 </Text>
//                 {/* {obtainStatus(class_type).text && (
//                   <Badge
//                   // size="lg" variant="solid" action="muted"
//                     // className={`px-2 py-1 rounded-md  ${
//                     //   obtainStatus(class_type).classes
//                     // }`}
//                   >
//                     <BadgeText
//                       className={`text-xs font-dm-sans-medium ${
//                         obtainStatus(class_type).classes
//                       }`}
//                     >
//                       {obtainStatus(class_type).text}
//                     </BadgeText>
//                   </Badge>
//                 )} */}
//               </HStack>
//               <TouchableOpacity
//                 className="ml-2"
//                 onPress={e => {
//                   e.stopPropagation();
//                 }}
//               >
//                 <Icon
//                   as={Heart}
//                   size="sm"
//                   className={
//                     false // fix later
//                       ? 'text-error-500 fill-error-500'
//                       : 'text-typography-400'
//                   }
//                 />
//               </TouchableOpacity>
//             </HStack>

//             {/* Time and spots row */}
//             <HStack className="items-center" space="xs">
//               <Text className="text-sm font-dm-sans-regular text-[#5D6679]">
//                 {obtainDateFrame(start_time, end_time)}
//               </Text>
//               <Box className="w-1 h-1 bg-[#00697B] rounded-full mx-1" />
//               <Text className="text-sm font-dm-sans-regular text-[#5D6679]">
//                 {`${obtainSpotsAvailable(class_type, data)} spots left`}
//               </Text>
//             </HStack>
//           </VStack>
//         </HStack>

//         {/* Bottom row with instructor, location, and button */}
//         <HStack className="justify-between items-center">
//           <VStack space="xs">
//             <Text className="text-sm  text-typography-600">
//               {instructor_first_name} {instructor_last_name}
//             </Text>
//             <Text className="text-xs font-dm-sans-regular text-typography-600">
//               {truncateText(gym_name, 25)}
//             </Text>
//           </VStack>
//           <ClassStatusButton data={data} selectedDate={selectedDate} />
//         </HStack>
//       </VStack>
//     </TouchableOpacity>
//   );
// };
