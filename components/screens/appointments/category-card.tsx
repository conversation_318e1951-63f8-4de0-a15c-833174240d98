import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Box } from '@/components/ui/box';
import { CategoryType } from '@/data/screens/appointments/types';
import { getInitials } from '@/data/common/common.utils';
import { router } from 'expo-router';
import { COLOR_CODES } from '@/constants/color-codes';
import { Image, TouchableOpacity } from 'react-native';
import { truncateText } from '@/utils/common';

export const CategoryCard = ({
  id,
  category_name,
  purchase_url,
  image_url,
}: CategoryType) => {
  const handleTrainersPress = () => {
    router.push({
      pathname: '/trainers',
      params: { id },
    });
  };

  const handlePurchasePress = () => {
    if (purchase_url) {
      router.push({
        pathname: '/webview',
        params: { url: purchase_url },
      });
    }
  };

  const handleSchedulePress = () => {
    router.push({
      pathname: '/appointments',
      params: { id, categoryName: category_name },
    });
  };

  return (
    <TouchableOpacity onPress={handleSchedulePress} activeOpacity={0.7}>
      <VStack
        space="sm"
        className="bg-white rounded-2xl p-2 border border-background-200 mb-2"
      >
        <HStack space="md">
          {image_url ? (
            <Image
              source={{ uri: image_url }}
              className="w-16 h-16 rounded-lg"
            />
          ) : (
            <Box
              className="w-16 h-16 rounded-lg items-center justify-center"
              style={{ backgroundColor: COLOR_CODES.trainer.circle }}
            >
              <Text className="text-sm font-dm-sans-bold text-white">
                {getInitials(category_name)}
              </Text>
            </Box>
          )}

          <VStack className="flex-1" space="xs">
            <Text className="font-dm-sans-bold text-base">
              {truncateText(category_name, 25)}
            </Text>
            <HStack space="sm">
              <Button
                variant="outline"
                size="xs"
                className="flex-1 rounded-lg border-[#607D8B] rounded-full"
                onPress={handleTrainersPress}
              >
                <ButtonText className="font-dm-sans-medium text-[#263238]">
                  Trainers
                </ButtonText>
              </Button>

              {purchase_url && (
                <Button
                  variant="outline"
                  size="xs"
                  className="flex-1 rounded-lg border-[#607D8B] rounded-full"
                  onPress={handlePurchasePress}
                >
                  <ButtonText className="text-[#263238] font-dm-sans-medium">
                    Purchase
                  </ButtonText>
                </Button>
              )}

              <TouchableOpacity
                className="flex-1 rounded-full bg-[#E0F7FA] border border-[#00838F] h-9 items-center justify-center px-2"
                onPress={handleSchedulePress}
                activeOpacity={0.7}
              >
                <Text className="text-[#00838F] font-dm-sans-bold text-center text-[10px]">
                  Schedule
                </Text>
              </TouchableOpacity>
            </HStack>
          </VStack>
        </HStack>
      </VStack>
    </TouchableOpacity>
  );
};
