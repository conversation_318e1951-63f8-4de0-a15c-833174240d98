import React from 'react';
import { EventResponse } from '@/data/screens/events/types';
import { obtainDateFrame } from '@/data/common/common.utils';
import { format, parseISO } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';
import { useFavoriteMutation } from '@/data/screens/common/queries/useFavoriteMutation';
import { useReserveMutation } from '@/data/screens/common/mutations/useReserveMutation';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';
import { EventDetailsSkeleton } from './event-details-skeleton';
import {
  BaseDetailsView,
  InfoSection,
  DescriptionSection,
} from '@/components/shared/details-view';
import { StatusButton } from '@/components/shared/status-button';
import { Icon } from '@/components/ui/icon';
import { DollarSign } from 'lucide-react-native';
import { convertEventToActivityCard, getStatusButtonConfig } from '../events-utils';

interface EventDetailsProps {
  eventItem: EventResponse;
  isLoading?: boolean;
}

export const EventDetails: React.FC<EventDetailsProps> = ({
  eventItem,
  isLoading = false,
}) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();
  const { mutate: cancelReservation, isPending: isCancelling } = useCancelReservation();
  const { mutate: reserveEvent, isPending: isReserving } = useReserveMutation();

  if (isLoading) {
    return <EventDetailsSkeleton />;
  }

  const handleFavorite = () => {
    favoriteMutation({
      type: 'class',
      item_id: eventItem.id,
    });
  };

  const activityData = convertEventToActivityCard(eventItem);
  const buttonConfig = getStatusButtonConfig(eventItem);

  const handleButtonPress = () => {
    switch (buttonConfig?.variant) {
      case 'cancel_reservation':
        return cancelReservation(activityData.reservationId as number);
      case 'reserve':
        return reserveEvent({ 
          class_id: Number(activityData.id), 
          date: activityData.date as string, 
          type: 'class' 
        });
      default:
        break;
    }
  };

  // Additional info for events (price if paid)
  const additionalInfo = [];
  if (eventItem.is_paid) {
    additionalInfo.push({
      icon: <Icon as={DollarSign} size="sm" className="text-typography-600" />,
      text: `$${eventItem.price} (Member: $${eventItem.member_price})`,
    });
  }

  return (
    <BaseDetailsView
      item={eventItem}
      heroImageUrl={eventItem.image_url || undefined}
      onFavorite={handleFavorite}
      renderActionButton={() => (
        <StatusButton
          variant={buttonConfig.variant}
          text={buttonConfig.text}
          disabled={buttonConfig.disabled}
          size="lg"
          isLoading={isReserving || isCancelling}
          onPress={handleButtonPress}
        />
      )}
    >
      <InfoSection
        date={format(
          parseISO(eventItem.date),
          DATE_FORMAT.DAY_MONTH_DAY_YEAR
        )}
        timeFrame={obtainDateFrame(eventItem.start_time, eventItem.end_time)}
        spotsLeft={activityData.spotsLeft}
        location={eventItem.gym_name}
        room={eventItem.room_name}
        additionalInfo={additionalInfo}
      />

      <DescriptionSection description={eventItem.description} />
    </BaseDetailsView>
  );
};

export default EventDetails;
