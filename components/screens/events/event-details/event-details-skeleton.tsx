import React from 'react';
import { View, StatusBar } from 'react-native';
import { ScrollView } from '@/components/ui/scroll-view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Skeleton, SkeletonText } from '@/components/ui/skeleton';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft, Heart, Share2 } from 'lucide-react-native';
import { router } from 'expo-router';

export const EventDetailsSkeleton = () => {
  const statusBarHeight = StatusBar.currentHeight || 44;

  return (
    <View className="flex-1 bg-background-0">
      <StatusBar barStyle="light-content" />

      {/* Hero Image Skeleton */}
      <View className="h-[300]">
        <Skeleton className="h-full w-full bg-background-300" />

        {/* Back button */}
        <Pressable
          onPress={() => router.back()}
          style={{
            position: 'absolute',
            top: statusBarHeight + 16,
            left: 16,
            zIndex: 10,
          }}
          className="p-2 bg-slate-500 rounded-full"
        >
          <Icon
            as={ArrowLeft}
            size="sm"
            color="white"
            className="text-typography-900"
          />
        </Pressable>

        {/* Share and favorite buttons */}
        <HStack
          style={{
            position: 'absolute',
            top: statusBarHeight + 16,
            right: 16,
            zIndex: 10,
          }}
          space="sm"
        >
          <Pressable className="p-2 bg-slate-500 rounded-full">
            <Icon as={Heart} size="sm" color="white" className="text-white" />
          </Pressable>
          <Pressable className="p-2 bg-slate-500 rounded-full">
            <Icon
              as={Share2}
              color="white"
              size="sm"
              className="text-typography-900"
            />
          </Pressable>
        </HStack>

        {/* Title overlay skeleton */}
        <Box className="absolute bottom-0 left-0 right-0 p-6 pb-8">
          <SkeletonText className="h-10 w-64 rounded bg-background-400" />
        </Box>
      </View>

      {/* Content card skeleton */}
      <Box className="bg-background-0 rounded-t-3xl -mt-5 flex-1">
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <VStack space="lg" className="px-4 pt-6 pb-24">
            {/* Metrics card skeleton - only duration for events */}
            <Box className="bg-white rounded-xl border border-background-200 pt-4 pb-4 mb-4">
              <HStack className="flex justify-center">
                <VStack className="items-center">
                  <Skeleton className="h-12 w-12 rounded-lg" />
                  <SkeletonText className="h-5 w-20 rounded mt-2" />
                  <SkeletonText className="h-3 w-20 rounded mt-1" />
                </VStack>
              </HStack>
            </Box>

            {/* Info items skeleton */}
            <VStack space="md">
              {/* Date */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-48 rounded" />
              </HStack>

              {/* Time */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-32 rounded" />
              </HStack>

              {/* Spots */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-24 rounded" />
              </HStack>

              {/* Location */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-40 rounded" />
              </HStack>

              {/* Room */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-28 rounded" />
              </HStack>

              {/* Price (for paid events) */}
              <HStack className="items-center" space="md">
                <Skeleton className="h-5 w-5 rounded" />
                <SkeletonText className="h-4 w-36 rounded" />
              </HStack>
            </VStack>

            {/* Description skeleton */}
            <VStack space="sm">
              <SkeletonText className="h-5 w-24 rounded" />
              <SkeletonText className="h-4 w-full rounded" />
              <SkeletonText className="h-4 w-full rounded" />
              <SkeletonText className="h-4 w-3/4 rounded" />
            </VStack>
          </VStack>
        </ScrollView>

        {/* Action button skeleton */}
        <Box className="px-6 pt-2 pb-6 bg-white border-t border-background-200">
          <Skeleton className="h-12 w-full rounded-full" />
        </Box>
      </Box>
    </View>
  );
};
