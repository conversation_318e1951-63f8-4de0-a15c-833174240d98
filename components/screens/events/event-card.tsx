import React from 'react';
import { router } from 'expo-router';
import { format } from 'date-fns';
import { EventResponse } from '@/data/screens/events/types';
import { ActivityCard, ActivityCardData } from '@/components/shared/activity-card';
import { StatusButton } from '@/components/shared/status-button';
import { useFavoriteMutation } from '@/data/screens/common/queries/useFavoriteMutation';
import { useReserveMutation } from '@/data/screens/common/mutations/useReserveMutation';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';
import { convertEventToActivityCard, getStatusButtonConfig } from './events-utils';
import { DATE_FORMAT } from '@/constants/date-formats';

interface EventCardProps {
  event: EventResponse;
  selectedDate: Date;
}

export const EventCard = ({ event, selectedDate }: EventCardProps) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();
  const { mutate: cancelReservation, isPending: isCancelling } = useCancelReservation();
  const { mutate: reserveEvent, isPending: isReserving } = useReserveMutation();

  const activityData = convertEventToActivityCard(event);
  const buttonConfig = getStatusButtonConfig(event);

  const handleButtonPress = (buttonConfig: Record<string, unknown>, item: ActivityCardData) => {
    switch (buttonConfig?.variant) {
      case 'cancel_reservation':
        return cancelReservation(item.reservationId as number);
      case 'reserve':
        return reserveEvent({ 
          class_id: Number(item.id), 
          date: item.date as string, 
          type: 'class' 
        });
      default:
        break;
    }
  };

  const handlePress = () => {
    router.push({
      pathname: '/(event-details)/[id]',
      params: {
        id: event.id,
        month_year: format(selectedDate, DATE_FORMAT.YEAR_MONTH),
        date: event.date,
      },
    });
  };

  const handleFavoritePress = () => {
    favoriteMutation({
      type: 'class',
      item_id: event.id,
    });
  };
  
  console.log({activityData});

  return (
    <ActivityCard
      data={activityData}
      onPress={handlePress}
      onFavoritePress={handleFavoritePress}
      showInstructor={false}
      renderButton={() => (
        <StatusButton
          isLoading={isReserving || isCancelling}
          variant={buttonConfig.variant}
          text={buttonConfig.text}
          disabled={buttonConfig.disabled}
          size="sm"
          onPress={() => handleButtonPress(buttonConfig, activityData)}
        />
      )}
    />
  );
};
