import React from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { EventResponse } from '@/data/screens/events/types';
import { CardSkeletonLoader } from '../../shared/card-skeleton';
import { EmptySearchState } from '@/components/shared/empty-search';
import { EmptyState } from '../classes/empty-state';
import { uniqueId } from 'lodash/fp';
import { EventCard } from './event-card';

interface EventsListProps {
  events: EventResponse[];
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  selectedDate: Date;
  onRefresh: () => void;
  onClearSearch: () => void;
}


export const EventsList = ({
  events,
  isLoading,
  isRefreshing,
  searchTerm,
  selectedDate,
  onRefresh,
  onClearSearch,
}: EventsListProps) => {

  if (isLoading) {
    return <CardSkeletonLoader />;
  }

  if (events.length === 0 && searchTerm) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (!events.length) {
    return (
      <EmptyState
        title="No events available"
        subtitle="There are no events scheduled for this date. Please select a different date."
      />
    );
  }
  
  return (
    <FlatList
      data={events}
      keyExtractor={item => uniqueId(String(item.id))}
      renderItem={({ item }) => (
        <EventCard event={item} selectedDate={selectedDate} />
      )}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 16,
        paddingBottom: 600,
      }}
    />
  );
};
