import React, { useState } from 'react';
import { View } from 'react-native';
import { Input, InputField, InputSlot, InputIcon } from '@/components/ui/input';
import { Button, ButtonText } from '@/components/ui/button';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react-native';
import { useForm } from '@tanstack/react-form';
import { loginSchema } from '@/modules/login/schemas';
import { isEmpty } from 'lodash/fp';

interface LoginFormProps {
  onSubmit: (values: { email: string; password: string }) => void;
  isLoading?: boolean;
  onForgotPassword: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  isLoading = false,
  onForgotPassword,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onChange: loginSchema,
    },
    onSubmit: async ({ value }) => {
      onSubmit(value);
    },
  });

  return (
    <View>
      {/* Email Input */}
      <form.Field
        name="email"
        children={field => (
          <FormControl className="mb-4">
            <FormControlLabel>
              <FormControlLabelText className="text-gray-600">
                Email
              </FormControlLabelText>
            </FormControlLabel>
            <Input
              variant="outline"
              size="lg"
              className={`border ${
                !isEmpty(field.state.meta.errors)
                  ? 'border-red-500'
                  : 'border-gray-300'
              } rounded-lg`}
              accessibilityLabel="Email input"
              accessibilityHint="Enter your email address"
            >
              <InputSlot className="pl-3">
                <InputIcon as={Mail} className="text-gray-500" />
              </InputSlot>
              <InputField
                placeholder="Enter your email address"
                value={field.state.value}
                onChangeText={field.handleChange}
                onBlur={field.handleBlur}
                keyboardType="email-address"
                autoCapitalize="none"
                className="text-gray-800"
                accessibilityRole="text"
                textContentType="emailAddress"
                returnKeyType="next"
              />
            </Input>
            {field.state.meta.errors ? (
              <FormControlError>
                <FormControlErrorText>
                  {field.state.meta.errors.join(', ')}
                </FormControlErrorText>
              </FormControlError>
            ) : null}
          </FormControl>
        )}
      />

      {/* Password Input */}
      <form.Field
        name="password"
        children={field => (
          <FormControl className="mb-2">
            <FormControlLabel>
              <FormControlLabelText className="text-gray-600">
                Password
              </FormControlLabelText>
            </FormControlLabel>
            <Input
              variant="outline"
              size="lg"
              className={`border ${
                !isEmpty(field.state.meta.errors)
                  ? 'border-red-500'
                  : 'border-gray-300'
              } rounded-lg`}
              accessibilityLabel="Password input"
              accessibilityHint="Enter your password"
            >
              <InputSlot className="pl-3">
                <InputIcon as={Lock} className="text-gray-500" />
              </InputSlot>
              <InputField
                placeholder="Enter your password"
                value={field.state.value}
                onChangeText={field.handleChange}
                onBlur={field.handleBlur}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                className="text-gray-800"
                accessibilityRole="text"
                textContentType="password"
                returnKeyType="done"
                onSubmitEditing={() => form.handleSubmit()}
              />
              <InputSlot
                className="pr-3"
                onPress={() => setShowPassword(!showPassword)}
                accessibilityLabel={
                  showPassword ? 'Hide password' : 'Show password'
                }
                accessibilityRole="button"
              >
                <InputIcon
                  as={showPassword ? EyeOff : Eye}
                  className="text-gray-500"
                />
              </InputSlot>
            </Input>
            {field.state.meta.errors ? (
              <FormControlError>
                <FormControlErrorText>
                  {field.state.meta.errors.join(', ')}
                </FormControlErrorText>
              </FormControlError>
            ) : null}
          </FormControl>
        )}
      />

      {/* Forgot Password Link */}
      <View className="items-end mb-4">
        <Button variant="link" onPress={onForgotPassword} className="p-0">
          <ButtonText className="text-[#00697B] font-dm-sans-medium">
            Forgot password?
          </ButtonText>
        </Button>
      </View>

      {/* Submit Button */}
      <form.Subscribe
        selector={state => [
          state.canSubmit,
          state.isSubmitting,
          state.isValidating,
        ]}
      >
        {([canSubmit, isSubmitting]) => (
          <Button
            className="bg-[#00697B] rounded-full mb-4"
            onPress={() => form.handleSubmit()}
            disabled={!canSubmit || isLoading || isSubmitting}
            accessibilityLabel={isLoading ? 'Logging in...' : 'Login'}
            accessibilityRole="button"
            accessibilityState={{
              disabled: !canSubmit || isLoading || isSubmitting,
            }}
          >
            <ButtonText>{isLoading ? 'Logging in...' : 'Login'}</ButtonText>
          </Button>
        )}
      </form.Subscribe>
    </View>
  );
};
