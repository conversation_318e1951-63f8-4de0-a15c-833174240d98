import React from 'react';
import { View } from 'react-native';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Fingerprint } from 'lucide-react-native';

interface BiometricLoginProps {
  isSupported: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  onBiometricLogin: () => void;
}

export const BiometricLogin: React.FC<BiometricLoginProps> = ({
  isSupported,
  isEnabled,
  isLoading,
  onBiometricLogin,
}) => {
  if (!isSupported || !isEnabled) {
    return null;
  }

  return (
    <View className="items-center mb-6">
      <Text className="text-gray-600 mb-4 text-center font-dm-sans-regular">
        Or use biometric authentication
      </Text>

      <Button
        variant="outline"
        className="border-[#00697B] rounded-full px-8 py-3"
        onPress={onBiometricLogin}
        disabled={isLoading}
        accessibilityLabel="Login with biometric authentication"
        accessibilityRole="button"
        accessibilityHint="Use fingerprint or face recognition to login"
      >
        <View className="flex-row items-center">
          <Fingerprint size={20} color="#00697B" className="mr-2" />
          <ButtonText className="text-[#00697B] font-dm-sans-medium ml-2">
            {isLoading ? 'Authenticating...' : 'Use Biometric'}
          </ButtonText>
        </View>
      </Button>
    </View>
  );
};
