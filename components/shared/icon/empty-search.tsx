import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const EmptySearchIcon = (props: SvgProps) => (
  <Svg width={141} height={140} fill="none" {...props}>
    <Path
      fill="#E6F9FC"
      fillOpacity={1}
      d="M26.83 63.514s19.386-.776 23.575-32.641c3.72-28.3 36.409-15.419 45.858-4.138 12.241 14.613 5.715 39.752 21.55 42.927 15.835 3.176 5.494 38.484-18.542 35.104-29.9-4.204-22.666 13.268-31.642 20.664-6.441 5.307-32.855.198-33.735-19.069-.74-16.213-7.58-16.161-12.427-18-6.991-2.65-11.396-21.853 5.363-24.847Z"
    />
    <Path
      fill="#33CCE6"
      fillOpacity={1}
      d="m92.73 98.904-19.046-18.82-3.772 3.817 19.046 18.819 3.771-3.816Z"
    />
    <Path
      fill="#8AE2F1"
      fillOpacity={1}
      d="M100.006 103.96 85.188 89.319a.21.21 0 0 0-.297.001l-5.608 5.676a.21.21 0 0 0 .002.297l14.818 14.642a.21.21 0 0 0 .296-.002l5.609-5.676a.21.21 0 0 0-.002-.297ZM56.803 89.549c13.103 0 23.725-10.622 23.725-23.725 0-13.103-10.622-23.725-23.725-23.725-13.103 0-23.725 10.622-23.725 23.725 0 13.103 10.622 23.725 23.725 23.725Z"
    />
    <Path
      fill="#B0EBF5"
      fillOpacity={1}
      d="M56.803 85.677c10.965 0 19.853-8.888 19.853-19.853 0-10.965-8.888-19.854-19.853-19.854-10.965 0-19.854 8.889-19.854 19.854s8.889 19.853 19.854 19.853Z"
    />
    <Path
      fill="#8AE2F1"
      fillOpacity={1}
      d="M96.913 44.377c-5.565-1.804-11.58 1.246-13.39 6.834-1.804 5.565 1.246 11.58 6.835 13.39 5.587 1.812 11.586-1.269 13.39-6.834 1.804-5.566-1.27-11.587-6.835-13.39Zm-1.68 5.18a3.306 3.306 0 1 1-4.156 2.103c.557-1.72 2.437-2.66 4.157-2.103ZM91.033 62.52a8.375 8.375 0 0 1-4.48-3.452c1.96-1.114 4.01-1.425 5.866-.823 1.855.6 3.364 2.04 4.268 4.108a8.375 8.375 0 0 1-5.654.167ZM50.594 95.088a5.488 5.488 0 1 0 7.382 2.393 5.493 5.493 0 0 0-7.382-2.393Zm1.278 2.504a1.707 1.707 0 1 1 1.552 3.041 1.707 1.707 0 0 1-1.552-3.041Zm3.198 6.265a4.32 4.32 0 0 1-2.896.375c.31-1.122.945-1.984 1.841-2.442.897-.457 1.973-.483 3.058-.058a4.326 4.326 0 0 1-2.003 2.125Z"
    />
  </Svg>
);
