import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>pinner, ButtonText } from '@/components/ui/button';

export type StatusButtonVariant =
  | 'reserve'
  | 'cancel_reservation'
  | 'join_waitlist'
  | 'event_cancelled'
  | 'event_full'
  | 'class_cancelled'
  | 'class_full';

interface StatusButtonProps {
  variant: StatusButtonVariant;
  text: string;
  onPress?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const getButtonStyles = (variant: StatusButtonVariant) => {
  switch (variant) {
    case 'reserve':
      return 'bg-cyan-50 border-cyan-300 text-cyan-800';
    case 'cancel_reservation':
      return 'bg-rose-100 border-rose-200 text-rose-800';
    case 'join_waitlist':
      return 'bg-emerald-50 border-green-300 text-green-700';
    case 'event_cancelled':
    case 'class_cancelled':
    case 'event_full':
    case 'class_full':
      return 'bg-gray-200 border-gray-300 text-slate-500';
    default:
      return 'bg-[#E6F9FC] border-[#00BFE0] text-[#00697B]';
  }
};

export const StatusButton = ({
  variant,
  text,
  onPress,
  disabled = false,
  isLoading = false,
  size = 'sm',
}: StatusButtonProps) => {
  const buttonStyles = getButtonStyles(variant);

  return (
    <Button
      size={size}
      variant="outline"
      className={`rounded-full ${buttonStyles}`}
      disabled={disabled}
      onPress={onPress}
    >
      <ButtonText className={buttonStyles}>{text}</ButtonText>
      {isLoading && <ButtonSpinner color="gray" className="ml-2" />}
    </Button>
  );
};
