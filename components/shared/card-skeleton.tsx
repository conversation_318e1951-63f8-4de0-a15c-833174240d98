import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

import { Skeleton, SkeletonText } from '@/components/ui/skeleton';
import { FlatList } from 'react-native';

export const CardSkeleton = () => {
  return (
    <VStack
      space="sm"
      className="bg-white rounded-2xl pl-2 pr-2 pt-3 pb-3 border border-background-200 mb-4"
    >
      <HStack className="justify-between items-start">
        <HStack space="sm" className="flex-1">
          {/* Avatar skeleton */}
          <Skeleton className="w-14 h-14 rounded-lg" />

          <VStack className="flex-1 flex gap-2" space="xs">
            <HStack className="items-center" space="xs">
              {/* Class name skeleton */}
              <SkeletonText className="h-5 w-32 rounded" />

              {/* Virtual badge skeleton */}
              <Skeleton className="h-6 w-16 rounded-full" />

              {/* Heart icon skeleton */}
              <Skeleton className="ml-auto h-6 w-6 rounded" />
            </HStack>

            <HStack className="items-center" space="md">
              {/* Time skeleton */}
              <SkeletonText className="h-4 w-20 rounded" />

              {/* Dot separator */}
              <Skeleton className="w-1 h-1 rounded-full" />

              {/* Spots available skeleton */}
              <SkeletonText className="h-4 w-16 rounded" />
            </HStack>
          </VStack>
        </HStack>
      </HStack>

      <HStack className="justify-between items-center mt-2 flex flex-row">
        <VStack space="xs" className="flex gap-1">
          {/* Instructor name skeleton */}
          <SkeletonText className="h-4 w-24 rounded" />

          {/* Location skeleton */}
          <SkeletonText className="h-3 w-32 rounded" />
        </VStack>

        {/* Status button skeleton */}
        <Skeleton className="h-8 w-20 rounded-2xl" />
      </HStack>
    </VStack>
  );
};

const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

export const CardSkeletonLoader = () => {
  return (
    <VStack space="md" className={'px-4 bg-gray-100 mt-8'}>
      <FlatList
        data={skeletonData}
        renderItem={() => <CardSkeleton />}
        keyExtractor={item => String(item.id)}
        showsVerticalScrollIndicator={false}
      />
    </VStack>
  );
};
