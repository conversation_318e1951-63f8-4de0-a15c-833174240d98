import React, { useState, useEffect } from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { ChevronLeft, ChevronRight } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';
import {
  getDaysInMonth,
  getDay,
  addMonths,
  subMonths,
  isToday,
  isSameDay,
  format,
  isBefore,
  startOfDay,
  startOfMonth,
} from 'date-fns';

interface CalendarWidgetProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

const CalendarWidget: React.FC<CalendarWidgetProps> = ({
  selectedDate,
  onDateSelect,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [isPrevMonthDisabled, setIsPrevMonthDisabled] = useState(false);

  useEffect(() => {
    const prevMonth = subMonths(currentMonth, 1);
    const firstDayOfPrevMonth = startOfMonth(prevMonth);
    setIsPrevMonthDisabled(
      isBefore(firstDayOfPrevMonth, startOfMonth(new Date()))
    );
  }, [currentMonth]);

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getDay(startOfMonth(currentMonth));
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    while (days.length % 7 !== 0) {
      days.push(null);
    }
    return days;
  };

  const isTodayDate = (day: number) => {
    const dayDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return isToday(dayDate);
  };

  const isSelectedDate = (day: number) => {
    const dayDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return isSameDay(dayDate, selectedDate);
  };

  const isPastDate = (day: number) => {
    const dayDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return isBefore(dayDate, startOfDay(new Date()));
  };

  const handleDatePress = (day: number) => {
    if (isPastDate(day)) return;
    const newDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    onDateSelect(newDate);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentMonth(subMonths(currentMonth, 1));
    } else {
      setCurrentMonth(addMonths(currentMonth, 1));
    }
  };

  const calendarDays = generateCalendarDays();

  return (
    <VStack space="lg" className="w-full pb-4">
      {/* Header */}
      <HStack className="justify-between items-center px-4">
        <Text className="text-lg font-bold">
          {format(currentMonth, 'MMMM yyyy')}
        </Text>
        <HStack space="md">
          <Pressable
            onPress={() => navigateMonth('prev')}
            disabled={isPrevMonthDisabled}
            className={`p-2 rounded-full ${isPrevMonthDisabled ? 'opacity-50' : ''}`}
          >
            <Icon as={ChevronLeft} size="md" />
          </Pressable>
          <Pressable
            onPress={() => navigateMonth('next')}
            className="p-2 rounded-full"
          >
            <Icon as={ChevronRight} size="md" />
          </Pressable>
        </HStack>
      </HStack>

      {/* Day names */}
      <HStack>
        {dayNames.map(dayName => (
          <Box key={dayName} className="flex-1 items-center py-2">
            <Text className="text-sm text-gray-400">{dayName}</Text>
          </Box>
        ))}
      </HStack>

      {/* Calendar grid */}
      <VStack space="md">
        {Array.from(
          { length: Math.ceil(calendarDays.length / 7) },
          (_, weekIndex) => (
            <HStack key={weekIndex}>
              {calendarDays
                .slice(weekIndex * 7, (weekIndex + 1) * 7)
                .map((day, dayIndex) => (
                  <Box
                    key={dayIndex}
                    className="flex-1 items-center justify-center"
                  >
                    {day && (
                      <Pressable
                        onPress={() => handleDatePress(day)}
                        disabled={isPastDate(day)}
                        className={`w-10 h-8 rounded-full items-center justify-center ${
                          isSelectedDate(day)
                            ? 'bg-primary-500'
                            : isTodayDate(day)
                              ? 'border border-primary-500'
                              : ''
                        } ${isPastDate(day) ? 'opacity-50' : ''}`}
                      >
                        <Text
                          className={`${
                            isSelectedDate(day)
                              ? 'text-white'
                              : isTodayDate(day)
                                ? 'text-primary-500 text-sm'
                                : isPastDate(day)
                                  ? 'text-gray-400'
                                  : 'text-typography-900'
                          }`}
                        >
                          {day}
                        </Text>
                      </Pressable>
                    )}
                  </Box>
                ))}
            </HStack>
          )
        )}
      </VStack>
    </VStack>
  );
};

export default CalendarWidget;
