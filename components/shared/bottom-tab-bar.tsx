import React, { useState, useEffect, useRef } from 'react';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Platform, Animated } from 'react-native';
import { Icon } from '@/components/ui/icon';
import { LinearGradient } from '@/components/ui/linear-gradient';
import { useRouter } from 'expo-router';

import { Sparkles } from 'lucide-react-native';

import { Card, Home, More, Profile2User } from 'iconsax-react-nativejs';

interface TabItem {
  name: string;
  label: string;
  path: string;
  inActiveIcon: React.ElementType;
  icon: React.ElementType;
}

const tabItems: TabItem[] = [
  {
    name: '(home)',
    label: 'Home',
    path: '(home)',
    inActiveIcon: () => <Home size="24" color="#00aecc" />,
    icon: () => <Home size="24" color="#00aecc" variant="Bulk" />,
  },
  {
    name: 'classes',
    label: 'Classes',
    path: 'classes',
    inActiveIcon: () => <Profile2User size="24" color="#00aecc" />,
    icon: () => <Profile2User size="24" color="#00aecc" variant="Bulk" />,
  },
  {
    name: 'reservation',
    label: 'My reservation',
    path: 'reservation',
    inActiveIcon: () => <Card size="24" color="#00aecc" />,
    icon: () => <Card size="24" color="#00aecc" variant="Bulk" />,
  },
  {
    name: 'more',
    label: 'More',
    path: 'more',
    inActiveIcon: () => <More size="24" color="#00aecc" />,
    icon: () => <More size="24" color="#00aecc" variant="Bulk" />,
  },
];

interface TabItemComponentProps {
  item: TabItem;
  isActive: boolean;
  onPress: () => void;
}

const TabItemComponent: React.FC<TabItemComponentProps> = ({
  item,
  isActive,
  onPress,
}: TabItemComponentProps) => (
  <Pressable
    className="flex-1 items-center justify-center px-1 py-2"
    onPress={onPress}
  >
    <Box className="items-center justify-center">
      <Icon as={isActive ? item.icon : item.inActiveIcon} size="lg" />
      {isActive && (
        <Box className="w-1.5 h-1.5 rounded-full bg-primary-500 mt-1" />
      )}
    </Box>
    {!isActive && (
      <Text
        numberOfLines={1}
        size="xs"
        className="mt-1 font-medium text-gray-500"
      >
        {item.label}
      </Text>
    )}
  </Pressable>
);

const AIButton: React.FC<{ onPress: () => void }> = ({ onPress }) => {
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(sparkleAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(sparkleAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, [sparkleAnim]);

  const sparkleStyle = {
    opacity: sparkleAnim.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0, 1, 0],
    }),
    transform: [
      {
        scale: sparkleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.5, 1.5],
        }),
      },
    ],
  };

  return (
    <Pressable
      onPress={onPress}
      className="absolute"
      style={{
        top: -25,
        alignSelf: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 8,
      }}
    >
      <Box
        className="w-[70px] h-[70px] rounded-full items-center justify-center border-10 border-gray-300"
        style={{ backgroundColor: '#E5E7EB' }}
      >
        <LinearGradient
          colors={['#9CF6FF', '#00D8E6', '#00A8CC']}
          start={{ x: 0.15, y: 0.05 }}
          end={{ x: 0.9, y: 0.9 }}
          className="w-[60px] h-[60px] rounded-full items-center justify-center relative overflow-hidden"
        >
          {/* Main AI sparkles icon */}
          <Box className="mb-1">
            <Sparkles size={20} color="white" />
          </Box>

          {/* Single animated sparkle overlay */}
          <Animated.View
            style={[
              {
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                alignItems: 'center',
                justifyContent: 'center',
              },
              sparkleStyle,
            ]}
          >
            <Sparkles size={16} color="rgba(255, 255, 255, 0.8)" />
          </Animated.View>
        </LinearGradient>
      </Box>
    </Pressable>
  );
};

export const BottomTabBar = (props: BottomTabBarProps) => {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const [, setBarWidth] = useState(0);

  const leftItems = tabItems.slice(0, 2);
  const rightItems = tabItems.slice(2);

  return (
    <Box className="bg-background-0">
      <Box
        className="relative"
        onLayout={e => setBarWidth(e.nativeEvent.layout.width)}
      >
        {/* Bar content */}
        <HStack
          className="bg-background-0 pt-4 px-7 rounded-t-3xl min-h-[78px]"
          style={{
            paddingBottom: Platform.OS === 'ios' ? insets.bottom : 16,
            boxShadow: '0px -10px 12px 0px rgba(0, 0, 0, 0.04)',
          }}
          space="md"
        >
          <HStack className="flex-1 items-center justify-around">
            {leftItems.map(item => {
              const isActive =
                props.state.routeNames[props.state.index] === item.path;
              return (
                <TabItemComponent
                  key={item.name}
                  item={item}
                  isActive={isActive}
                  onPress={() => props.navigation.navigate(item.path)}
                />
              );
            })}
          </HStack>

          {/* Spacer for center AI button */}
          <Box className="w-[50px]" />

          <HStack className="flex-1 items-center justify-around">
            {rightItems.map(item => {
              const isActive =
                props.state.routeNames[props.state.index] === item.path;
              return (
                <TabItemComponent
                  key={item.name}
                  item={item}
                  isActive={isActive}
                  onPress={() => props.navigation.navigate(item.path)}
                />
              );
            })}
          </HStack>
        </HStack>

        <AIButton onPress={() => router.push('/ai')} />
      </Box>
    </Box>
  );
};
