import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import {
  getInitials,
  getRandomColorForInitials,
} from '@/data/common/common.utils';
import { HStack } from '../ui/hstack';

interface ImageAvatarsProps {
  title: string;
  imageUrl?: string | null;
  size?: 'sm' | 'md' | 'lg';
}

const sizeClasses = {
  sm: {
    avatar: 'w-8 h-8',
    text: 'text-xs',
    overlap: '-ml-2',
    countBox: 'w-6 h-6',
    countText: 'text-2xs',
  },
  md: {
    avatar: 'w-12 h-12',
    text: 'text-base',
    overlap: '-ml-3',
    countBox: 'w-8 h-8',
    countText: 'text-xs',
  },
  lg: {
    avatar: 'w-16 h-16',
    text: 'text-xl',
    overlap: '-ml-4',
    countBox: 'w-10 h-10',
    countText: 'text-sm',
  },
};

export const ImageAvatars = ({
  title,
  imageUrl,
  size = 'md',
}: ImageAvatarsProps) => {
  const classes = sizeClasses[size];
  return (
    <HStack>
      {imageUrl ? (
        <Image
          source={{ uri: imageUrl }}
          className={`${classes.avatar} rounded-lg`}
          alt={title}
        />
      ) : (
        <Box
          className={`${classes.avatar} rounded-lg items-center justify-center`}
          style={{ backgroundColor: getRandomColorForInitials(title) }}
        >
          <Text className={`text-white font-dm-sans-bold ${classes.text}`}>
            {getInitials(title)}
          </Text>
        </Box>
      )}
    </HStack>
  );
};
