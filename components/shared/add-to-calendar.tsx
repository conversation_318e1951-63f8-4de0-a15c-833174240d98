import React, { useState } from 'react';
import { addToCalendar } from '@/utils/calendar';
import { formatDate } from '@/data/common/common.utils';
import { CalendarTick } from 'iconsax-react-nativejs';
import { Pressable } from 'react-native';
import { CalendarSuccessActionSheet } from '@/components/shared/calendar-success-actionsheet';
import { useCalendarSynced } from '@/hooks/useCalendarSynced';

export const AddToCalendarButton = ({
  name,
  selectedDate = new Date().toISOString(),
  location,
  description,
  instructor,
  reservationId,
  startTime,
  endTime,
}: {
  name: string;
  selectedDate: string;
  location: string;
  description: string;
  instructor: string;
  reservationId: number;
  startTime: string;
  endTime: string;
}) => {
  const [showSuccess, setShowSuccess] = useState(false);
  const { isSynced, setIsSynced } = useCalendarSynced(reservationId);

  const handleAddToCalendar = async () => {
    if (isSynced) return; // already synced, ignore

    const eventId = await addToCalendar({
      title: name,
      startTime,
      endTime,
      date: formatDate(selectedDate),
      location,
      description,
      instructor,
      reservationId,
    });

    // Only show success if an event was actually created in the device calendar
    if (eventId) {
      setIsSynced(true);
      setShowSuccess(true);
    }
  };
  return (
    <>
      <Pressable
        disabled={isSynced}
        onPress={e => {
          e.stopPropagation();
          handleAddToCalendar();
        }}
        className={`w-10 h-10 rounded-full bg-[#E6F9FC] border border-background-[#E6F9FC] items-center justify-center ${isSynced ? 'opacity-50' : ''}`}
      >
        <CalendarTick size="24" color="#00697B" />
      </Pressable>

      <CalendarSuccessActionSheet
        isOpen={showSuccess}
        onClose={() => setShowSuccess(false)}
      />
    </>
  );
};
