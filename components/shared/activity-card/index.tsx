import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Heart } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';
import { TouchableOpacity } from 'react-native';
import { Image } from '@/components/ui/image';
import { truncateText } from '@/utils/common';
import {
  getInitials,
  getRandomColorForInitials,
  obtainDateFrame,
} from '@/data/common/common.utils';
import { Badge, BadgeText } from '@/components/ui/badge';

export interface ActivityCardData {
  id: string;
  title: string;
  imageUrl?: string;
  startTime: string;
  endTime: string;
  spotsLeft: number;
  instructor?: string;
  location: string;
  status?: {
    text?: string;
    classes?: string;
  };
  isFavorite?: boolean;
  reservationId?: number;
  date?: string;
  formattedDate?: string;
}

interface ActivityCardProps {
  data: ActivityCardData;
  onPress?: () => void;
  onFavoritePress?: () => void;
  renderButton?: () => React.ReactNode;
  showInstructor?: boolean;
}

export const ActivityCard = ({
  data,
  onPress,
  onFavoritePress,
  renderButton,
  showInstructor = true,
}: ActivityCardProps) => {
  const {
    title,
    imageUrl,
    startTime,
    endTime,
    spotsLeft,
    instructor,
    location,
    status,
    isFavorite,
    formattedDate,
  } = data;

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <VStack
        space="sm"
        className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
      >
        <HStack space="sm" className="items-start">
          {imageUrl ? (
            <Image
              source={{ uri: imageUrl }}
              className="w-12 h-12 rounded-lg"
              alt={title}
            />
          ) : (
            <Box
              className="w-12 h-12 rounded-lg items-center justify-center"
              style={{ backgroundColor: getRandomColorForInitials(title) }}
            >
              <Text className="text-sm font-dm-sans-bold text-white">
                {getInitials(title)}
              </Text>
            </Box>
          )}
          <VStack className="flex-1" space="xs">
            <HStack className="items-center justify-between">
              <HStack className="items-center flex-1" space="sm">
                <Text
                  className="text-[#00697B] font-dm-sans-bold text-base shrink"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {title}
                </Text>
                {status?.text && (
                  <Badge
                  size="md" variant="solid" action="muted"
                    className={`px-2 py-1 rounded-md ${status.classes} shrink-0`}
                  >
                    <BadgeText
                      className={`text-xs font-dm-sans-medium capitalize  ${status.classes}`}
                    >
                      {status.text}
                    </BadgeText>
                  </Badge>
                )}
              </HStack>
              <TouchableOpacity
                className="ml-3"
                onPress={e => {
                  e.stopPropagation();
                  onFavoritePress?.();
                }}
              >
                <Icon
                  as={Heart}
                  size="sm"
                  className={
                    isFavorite
                      ? 'text-error-500 fill-error-500'
                      : 'text-typography-400'
                  }
                />
              </TouchableOpacity>
            </HStack>

            <HStack className="items-center" space="xs" style={{ flexWrap: 'nowrap' }}>
              {formattedDate && (
                <>
                  <Text className="text-sm font-dm-sans-regular text-[#5D6679]" numberOfLines={1}>
                    {formattedDate}
                  </Text>
                  <Box className="w-1 h-1 bg-[#00697B] rounded-full" />
                </>
              )}
              <Text className="text-sm font-dm-sans-regular text-[#5D6679]" numberOfLines={1}>
                {obtainDateFrame(startTime, endTime)}
              </Text>
              <Box className="w-1 h-1 bg-[#00697B] rounded-full" />
              <Text className="text-sm font-dm-sans-regular text-[#5D6679]" numberOfLines={1}>
                {`${spotsLeft} spots`}
              </Text>
            </HStack>
          </VStack>
        </HStack>

        <HStack className="justify-between items-center">
          <VStack space="xs">
            {showInstructor && instructor && (
              <Text className="text-sm text-typography-600">{instructor}</Text>
            )}
            <Text className="text-xs font-dm-sans-regular text-typography-600">
              {truncateText(location, 25)}
            </Text>
          </VStack>
          {renderButton && renderButton()}
        </HStack>
      </VStack>
    </TouchableOpacity>
  );
};
