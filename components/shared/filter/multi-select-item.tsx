import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Check } from 'lucide-react-native';

export interface SelectOption {
  label: string;
  value: string;
}

export const MultiSelectItem = ({
  option,
  isSelected,
  onToggle,
}: {
  option: SelectOption;
  isSelected: boolean;
  onToggle: () => void;
}) => (
  <Pressable
    onPress={onToggle}
    className="flex-row items-center justify-between px-4 py-3 border-b border-outline-100"
  >
    <Text className="text-typography-900 font-dm-sans-regular text-base flex-1">
      {option.label}
    </Text>
    {isSelected && (
      <Icon as={Check} size="sm" className="text-[#00BFE0] ml-2" />
    )}
  </Pressable>
);

export default MultiSelectItem;
