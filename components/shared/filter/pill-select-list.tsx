'use client';

import React, { useCallback } from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import PillSelectItem from './pill-select-item';

export type PillOption = { label: string; value: string };

export type PillSelectListProps = {
  title?: string;
  mode: 'single' | 'multi';
  options: PillOption[];
  selected: string | string[];
  onChange: (value: string | string[]) => void;
};

export const PillSelectList = ({
  title,
  mode,
  options,
  selected,
  onChange,
}: PillSelectListProps) => {
  const isSelected = useCallback(
    (value: string) => {
      return Array.isArray(selected)
        ? selected.includes(value)
        : selected === value;
    },
    [selected]
  );

  const handleToggle = useCallback(
    (value: string) => {
      if (mode === 'single') {
        onChange(value);
        return;
      }
      const current = Array.isArray(selected) ? selected : [];
      const next = current.includes(value)
        ? current.filter(v => v !== value)
        : [...current, value];
      onChange(next);
    },
    [mode, onChange, selected]
  );

  return (
    <VStack className="px-4 pb-4" space="xs">
      {title ? (
        <Text className="text-xl font-dm-sans-bold text-typography-900 mb-2">
          {title}
        </Text>
      ) : null}
      <HStack className="flex-row flex-wrap">
        {options.map(opt => (
          <PillSelectItem
            key={opt.value}
            label={opt.label}
            isSelected={isSelected(opt.value)}
            onToggle={() => handleToggle(opt.value)}
          />
        ))}
      </HStack>
    </VStack>
  );
};

PillSelectList.displayName = 'PillSelectList';

export default PillSelectList;
