import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { X } from 'lucide-react-native';
import { Badge, BadgeText } from '@/components/ui/badge';

export const RemovableTag = ({
  label,
  onRemove,
  size = 'md',
}: {
  label: string;
  onRemove: () => void;
  size?: 'sm' | 'md' | 'lg';
}) => (
  <Badge
    variant="outline"
    className="rounded-xl px-3 py-1.5 mr-2 mb-2 bg-white border-outline-300 flex-row items-center max-w-[200px] overflow-hidden"
  >
    <BadgeText size={size} className="normal-case">
      {label}
    </BadgeText>
    <Pressable
      onPress={onRemove}
      accessibilityRole="button"
      accessibilityLabel={`Remove ${label}`}
    >
      <Icon as={X} size="xs" className="text-typography-500" />
    </Pressable>
  </Badge>
);

export default RemovableTag;
