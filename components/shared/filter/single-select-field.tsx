import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '@/components/ui/select';
import { Icon } from '@/components/ui/icon';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import PillSelectList from './pill-select-list';

export interface SelectOption {
  label: string;
  value: string;
}

export interface SingleSelectField {
  type: 'select';
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
  listStyle?: 'default' | 'pill';
}

const SingleSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: SingleSelectField;
  value: string;
  onChange: (value: string) => void;
}) => {
  const selectedOption = field.options.find(option => option.value === value);
  const isPill = field.listStyle === 'pill';

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>
      <Select
        selectedValue={isPill ? '__pill_static__' : value}
        onValueChange={isPill ? () => {} : onChange}
      >
        <SelectTrigger
          size="md"
          className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14"
        >
          <SelectInput
            placeholder={
              field.placeholder || `Select ${field.label.toLowerCase()}`
            }
            className="text-typography-700 flex-1 text-base"
            value={selectedOption?.label || ''}
          />
          <Icon as={() => <ArrowDown2 size="20" color="#9CA3AF" />} />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent className="max-h-[350px]">
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {field.listStyle === 'pill' ? (
                <PillSelectList
                  mode="single"
                  options={field.options}
                  selected={value}
                  onChange={v => {
                    if (typeof v === 'string') onChange(v);
                  }}
                />
              ) : (
                field.options.map(option => (
                  <SelectItem
                    key={option.value}
                    label={option.label}
                    value={option.value}
                  />
                ))
              )}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

export default SingleSelectFieldComponent;
