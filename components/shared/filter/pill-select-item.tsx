'use client';

import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Check } from 'lucide-react-native';

export type PillSelectItemProps = {
  label: string;
  isSelected: boolean;
  onToggle: () => void;
};

export const PillSelectItem = React.memo(
  ({ label, isSelected, onToggle }: PillSelectItemProps) => {
    return (
      <Pressable
        accessibilityRole="button"
        onPress={onToggle}
        className={`flex-row items-center rounded-full px-4 py-3 mr-2 mb-2 border ${
          isSelected
            ? 'bg-[#E5F9FD] border-[#BFEFF7]'
            : 'bg-white border-outline-200'
        }`}
      >
        <Pressable
          disabled
          className={`items-center justify-center h-5 w-5 rounded-full border ${
            isSelected ? 'bg-[#20D3EE] border-[#20D3EE]' : 'border-outline-300'
          }`}
        >
          {isSelected ? (
            <Icon as={Check} size="2xs" className="text-white" />
          ) : null}
        </Pressable>
        <Text className="text-typography-900 text-base ml-3">{label}</Text>
      </Pressable>
    );
  }
);

PillSelectItem.displayName = 'PillSelectItem';

export default PillSelectItem;
