import React, { memo } from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Switch } from '@/components/ui/switch';

export interface ToggleField {
  type: 'toggle';
  key: string;
  label: string;
  description?: string;
}

export const ToggleFieldComponent = memo(
  ({
    field,
    value,
    onChange,
  }: {
    field: ToggleField;
    value: boolean;
    onChange: (value: boolean) => void;
  }) => {
    return (
      <VStack space="sm" className="w-full">
        <Box className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4">
          <HStack className="items-center justify-between w-full">
            <VStack className="flex-1">
              <Text className="text-typography-700 font-dm-sans-medium text-base">
                {field.label}
              </Text>
              {field.description && (
                <Text className="text-typography-500 font-dm-sans-regular text-sm mt-1">
                  {field.description}
                </Text>
              )}
            </VStack>
            <Switch
              value={value}
              onValueChange={onChange}
              trackColor={{ true: '#00BFE0', false: '#D1D5DB' }}
              size="md"
              className="ml-3"
            />
          </HStack>
        </Box>
      </VStack>
    );
  }
);

export default ToggleFieldComponent;
