import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '@/components/ui/select';
import { Icon } from '@/components/ui/icon';
import { Clock } from 'lucide-react-native';
import { AvailableTimeSlot } from '@/data/screens/activities/types';

export interface TimeField {
  type: 'time';
  key: string;
  label: string;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

const TimeFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: TimeField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm" className="w-full">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <Select selectedValue={value} onValueChange={onChange}>
      <SelectTrigger
        size="md"
        className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14"
      >
        <SelectInput
          placeholder={
            field.placeholder || `Select ${field.label.toLowerCase()}`
          }
          className="text-typography-500 flex-1 text-base"
          value={
            value
              ? field.timeSlots.find(slot => slot.time_value === value)
                  ?.time_label
              : ''
          }
        />
        <Icon as={Clock} size="sm" className="text-typography-500" />
      </SelectTrigger>
      <SelectPortal>
        <SelectBackdrop />
        <SelectContent>
          <SelectDragIndicatorWrapper>
            <SelectDragIndicator />
          </SelectDragIndicatorWrapper>
          <SelectScrollView>
            {field.timeSlots
              .filter(slot => slot.allow_reservations)
              .map(slot => (
                <SelectItem
                  key={slot.time_value}
                  label={slot.time_label}
                  value={slot.time_value}
                />
              ))}
          </SelectScrollView>
        </SelectContent>
      </SelectPortal>
    </Select>
  </VStack>
);

export default TimeFieldComponent;
