import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { DescriptionSectionProps } from '../types';

export const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  title = 'Description',
  description,
}) => {
  return (
    <VStack space="sm">
      <Text className="text-md font-dm-sans-bold text-typography-900">
        {title}
      </Text>
      <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
        {description}
      </Text>
    </VStack>
  );
};
