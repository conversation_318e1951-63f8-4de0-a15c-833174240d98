import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { DoorClosed } from 'lucide-react-native';
import { Calendar1, Clock, Location, TagUser } from 'iconsax-react-nativejs';
import { InfoSectionProps } from '../types';

export const InfoSection: React.FC<InfoSectionProps> = ({
  date,
  timeFrame,
  spotsLeft,
  location,
  room,
  additionalInfo = []
}) => {
  return (
    <VStack space="md">
      {/* Date */}
      <HStack className="items-center" space="md">
        <Calendar1 size="20" color="#5D6679" variant="Outline" />
        <Text className="text-sm font-dm-sans-medium text-typography-600">
          {date}
        </Text>
      </HStack>

      {/* Time */}
      <HStack className="items-center" space="md">
        <Clock size="20" color="#5D6679" variant="Outline" />
        <Text className="text-sm font-dm-sans-medium text-typography-600">
          {timeFrame}
        </Text>
      </HStack>

      {/* Spots */}
      {Boolean(spotsLeft) && (
        <HStack className="items-center" space="md">
          <TagUser size="20" color="#5D6679" variant="Outline" />
          <Text className="text-sm font-dm-sans-medium text-typography-600">
            {spotsLeft} slots left
          </Text>
        </HStack>
      )}
      {/* Location */}
      <HStack className="items-center" space="md">
        <Location size="20" color="#5D6679" variant="Outline" />
        <Text className="text-sm font-dm-sans-medium text-typography-600">
          {location}
        </Text>
      </HStack>

      {/* Room */}
      <HStack className="items-center" space="md">
        <Icon as={DoorClosed} size="sm" className="text-typography-600" />
        <Text className="text-sm font-dm-sans-medium text-typography-600">
          {room}
        </Text>
      </HStack>

      {/* Additional info items */}
      {additionalInfo.map((info, index) => (
        <HStack key={index} className="items-center" space="md">
          {info.icon}
          <Text className="text-sm font-dm-sans-medium text-typography-600">
            {info.text}
          </Text>
        </HStack>
      ))}
    </VStack>
  );
};
