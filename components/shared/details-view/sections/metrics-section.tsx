import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Chart21 } from 'iconsax-react-nativejs';
import { TimerIcon } from '@/components/shared/icon/timer';
import { MetricsSectionProps } from '../types';

export const MetricsSection: React.FC<MetricsSectionProps> = ({
  intensity,
  duration,
  showIntensity = true,
}) => {
  return (
    <Box className="bg-white rounded-xl border border-background-200 pt-4 pb-4 mb-4 flex">
      <HStack className="flex justify-evenly gap-10">
        {showIntensity && intensity && (
          <VStack className="items-center">
            <Box className="h-12 w-12 items-center justify-center text-center">
              <Chart21 size="20" color="#099137" variant="Bulk" />
            </Box>
            <Text className="font-dm-sans-bold text-typography-900 text-center">
              {intensity.label}
            </Text>
            <Text className="text-xs font-dm-sans-regular text-typography-500">
              INTENSITY
            </Text>
          </VStack>
        )}

        {/* Duration */}
        <VStack className="items-center">
          <Box className="h-12 w-12 items-center justify-center">
            <TimerIcon />
          </Box>
          <Text className="text-base font-dm-sans-bold text-typography-900 text-center">
            {duration}
          </Text>
          <Text className="text-xs font-dm-sans-regular text-typography-500 text-center">
            DURATION
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
};
