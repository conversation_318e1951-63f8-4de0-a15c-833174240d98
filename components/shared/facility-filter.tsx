import { Box } from '../ui/box';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '../ui/select';
import { HStack } from '../ui/hstack';
import { Text } from '../ui/text';
import { Icon } from '../ui/icon';
import { useClientInfo } from '@/data/screens/common/queries/useClientConfig';
import { useMemo, useCallback } from 'react';
import { ArrowDown2, Location } from 'iconsax-react-nativejs';
import RemovableTag from './filter/removable-tag';
import MultiSelectItem from './filter/multi-select-item';
import { Badge, BadgeText } from '@/components/ui/badge';

export const FacilityFilter = ({
  value,
  onChange,
}: {
  value: string[];
  onChange: (value: string[]) => void;
}) => {
  const { data: clientData } = useClientInfo();

  const options = useMemo(
    () =>
      (clientData?.facilities ?? []).map(facility => ({
        label: facility.name,
        value: String(facility.id),
      })),
    [clientData?.facilities]
  );

  const selectedOptions = useMemo(
    () => options.filter(o => value.includes(o.value)),
    [options, value]
  );

  const handleToggle = useCallback(
    (optionValue: string) => {
      const next = value.includes(optionValue)
        ? value.filter(v => v !== optionValue)
        : [...value, optionValue];
      onChange(next);
    },
    [value, onChange]
  );

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select
        key={value.join(',')}
        selectedValue=""
        onValueChange={next => {
          if (typeof next === 'string') handleToggle(next);
        }}
      >
        <SelectTrigger className="flex-row items-center gap-2 px-5 border-0 bg-white min-h-12">
          <Location size="20" color="black" />
          {selectedOptions.length > 0 ? (
            <HStack className="flex-1 flex-wrap">
              {selectedOptions.slice(0, 2).map(opt => (
                <RemovableTag
                  key={opt.value}
                  label={opt.label}
                  onRemove={() => handleToggle(opt.value)}
                />
              ))}
              {selectedOptions.length > 2 && (
                <Badge
                  variant="outline"
                  className="rounded-xl px-3 py-1.5 mr-2 mb-2 bg-white border-outline-300"
                >
                  <BadgeText size="sm" className="normal-case">
                    +{selectedOptions.length - 2}
                  </BadgeText>
                </Badge>
              )}
            </HStack>
          ) : (
            <Text className="text-typography-700 flex-1 font-medium">
              Select location
            </Text>
          )}
          <Icon
            as={() => <ArrowDown2 size="20" color="black" />}
            className="mr-3"
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView className='max-h-[500px]'>
              {options.map(opt => (
                <MultiSelectItem
                  key={opt.value}
                  option={opt}
                  isSelected={value.includes(opt.value)}
                  onToggle={() => handleToggle(opt.value)}
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
