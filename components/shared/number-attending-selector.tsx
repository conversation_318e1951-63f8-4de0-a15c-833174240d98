import React from 'react';
import { Box } from '../ui/box';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '../ui/select';
import { Text } from '../ui/text';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import { AttendingPerson } from '@/data/screens/activities/types';

interface NumberAttendingSelectorProps {
  value: string;
  onChange: (value: string) => void;
  options: AttendingPerson[];
  placeholder?: string;
}

export const NumberAttendingSelector = ({
  value,
  onChange,
  options,
  placeholder = 'Number attending',
}: NumberAttendingSelectorProps) => {
  const selectedOption = options.find(o => String(o.value) === value);

  return (
    <Box className="mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center px-4 h-11 rounded-xl bg-white border border-background-200">
          <SelectInput
            placeholder={placeholder}
            className="flex-1 font-dm-sans-medium text-typography-900"
            value={selectedOption ? selectedOption.label : ''}
          />
          <SelectIcon
            className="ml-2"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent className="h-[400px] max-h-[400px]">
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {options.map(opt => (
                <SelectItem
                  key={opt.value}
                  label={opt.label}
                  value={String(opt.value)}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {opt.label}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
