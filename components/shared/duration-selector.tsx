import React from 'react';
import { Box } from '../ui/box';
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from '../ui/select';
import { Text } from '../ui/text';
import { ArrowDown2 } from 'iconsax-react-nativejs';
import { Duration } from '@/data/screens/activities/types';

interface DurationSelectorProps {
  value: string;
  onChange: (value: string) => void;
  durations: Duration[];
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
}

export const DurationSelector = ({
  value,
  onChange,
  durations,
  placeholder = 'Select duration',
  disabled = false,
  isLoading = false,
}: DurationSelectorProps) => {
  const selectedDuration = durations.find(d => String(d.value) === value);

  return (
    <Box className="mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger
          disabled={disabled}
          className="flex-row items-center px-4 h-11 rounded-xl bg-white border border-background-200"
        >
          <SelectInput
            placeholder={isLoading ? 'Loading...' : placeholder}
            className="flex-1 font-dm-sans-medium text-typography-900"
            value={selectedDuration ? selectedDuration.label : ''}
          />
          <SelectIcon
            className="ml-2"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent className="h-[400px] max-h-[400px]">
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {durations.map(duration => (
                <SelectItem
                  key={duration.value}
                  label={duration.label}
                  value={String(duration.value)}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {duration.label}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
