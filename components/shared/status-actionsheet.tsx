import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Box } from '@/components/ui/box';
import { Icon } from '@/components/ui/icon';
import Svg, { Path } from 'react-native-svg';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { Danger } from 'iconsax-react-nativejs';

const SuccessCheckIcon = (props: any) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={125}
    height={125}
    viewBox="0 0 125 125"
    fill="none"
    {...props}
  >
    <Path
      fill="#91EFC8"
      fillOpacity={0.6}
      d="M20 63c0-23.196 18.804-42 42-42s42 18.804 42 42-18.804 42-42 42-42-18.804-42-42Z"
    />
    <Path
      fill="#91EFC8"
      fillOpacity={0.6}
      d="M25 63c0-20.434 16.566-37 37-37s37 16.566 37 37-16.566 37-37 37-37-16.566-37-37Z"
    />
    <Path
      fill="#5EE8AF"
      d="M30 63c0-17.673 14.327-32 32-32 17.673 0 32 14.327 32 32 0 17.673-14.327 32-32 32-17.673 0-32-14.327-32-32Z"
    />
    <Path
      stroke="#2B2F38"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={4}
      d="m51.334 63 6.667 6.667L73.667 54"
    />
  </Svg>
);

export type StatusType = 'success' | 'error';

export interface ActionButton {
  label: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  isDisabled?: boolean;
}

interface StatusActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  status: StatusType;
  title: string;
  description: string;
  actions: ActionButton[];
  className?: string;
}

const getStatusConfig = (status: StatusType) => {
  switch (status) {
    case 'success':
      return {
        icon: SuccessCheckIcon,
        iconBgColor: 'bg-transparent',
        iconColor: 'text-base',
      };
    case 'error':
      return {
        icon: () => <Danger size="20" color="#700E0B" strokeWidth={2} />,
        iconBgColor: 'bg-red-300',
        iconColor: 'text-red-500',
      };
    default:
      return {
        icon: SuccessCheckIcon,
        iconBgColor: 'bg-transparent',
        iconColor: 'text-green-500',
      };
  }
};

export const StatusActionSheet: React.FC<StatusActionSheetProps> = ({
  isOpen,
  onClose,
  status,
  title,
  description,
  actions,
  className,
}) => {
  const getButtonStyles = (variant: ActionButton['variant'] = 'primary') => {
    switch (variant) {
      case 'primary':
        return {
          buttonClass: 'bg-[#00BFE0] rounded-full',
          textClass: 'text-black font-dm-sans-medium text-base',
        };
      case 'secondary':
        return {
          buttonClass: 'border-[#00BFE0] rounded-full bg-[#E6F9FC]',
          textClass: 'text-[#00BFE0] font-dm-sans-medium text-base',
        };
      case 'outline':
        return {
          buttonClass:
            'border border-typography-300 bg-transparent rounded-full',
          textClass: 'text-typography-600 font-dm-sans-medium text-base',
        };
      default:
        return {
          buttonClass: 'bg-[#00BFE0] rounded-full',
          textClass: 'text-black font-dm-sans-medium text-base',
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose} className={className}>
      <ActionsheetBackdrop className="bg-[#000] opacity-50" />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="sm" className="px-6 py-4 items-center">
          <Box
            className={`w-32 h-32 ${statusConfig.iconBgColor} items-center justify-center mb-1`}
          >
            {status === 'success' ? (
              <SuccessCheckIcon width={125} height={125} />
            ) : (
              <Icon
                as={statusConfig.icon}
                size="md"
                className={statusConfig.iconColor}
                color="red"
              />
            )}
          </Box>

          <Text className="text-lg font-dm-sans-bold text-typography-900 text-center">
            {title}
          </Text>

          <Text className="text-base font-dm-sans-regular text-typography-600 text-center px-4">
            {description}
          </Text>

          <VStack space="sm" className="w-full mt-2">
            {actions.map((action, index) => {
              const buttonStyles = getButtonStyles(action.variant);
              return (
                <Button
                  key={index}
                  onPress={action.onPress}
                  isDisabled={!!action.isDisabled}
                  className={`${buttonStyles.buttonClass} ${action.className || ''} ${action.isDisabled ? 'opacity-50' : ''}`}
                  variant={action.variant === 'outline' ? 'outline' : 'solid'}
                >
                  <ButtonText className={buttonStyles.textClass}>
                    {action.label}
                  </ButtonText>
                </Button>
              );
            })}
          </VStack>
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
