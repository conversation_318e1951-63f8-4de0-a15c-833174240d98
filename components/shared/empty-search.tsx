import { EmptyState } from '../screens/classes/empty-state';
import { Button, ButtonText } from '../ui/button';
import { VStack } from '../ui/vstack';

import { EmptySearchIcon } from '@/components/shared/icon/empty-search';

export const EmptySearchState = ({
  searchTerm,
  onClearSearch,
}: {
  searchTerm: string;
  onClearSearch: () => void;
}) => {
  return (
    <VStack className=" bg-white h-full w-full">
      <EmptyState
        icon={<EmptySearchIcon />}
        title="No results found"
        subtitle={`We couldn't find any results for "${searchTerm}". Try adjusting your search.`}
        action={
          <Button
            variant="outline"
            className="border-[#00697B] rounded-full mt-4 bg-white"
            onPress={onClearSearch}
          >
            <ButtonText className="text-[#00697B]">Clear search</ButtonText>
          </Button>
        }
      />
    </VStack>
  );
};
