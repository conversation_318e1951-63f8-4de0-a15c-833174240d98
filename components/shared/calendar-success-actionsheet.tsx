import React from 'react';
import { StatusActionSheet, type StatusType } from '@/components/shared/status-actionsheet';

interface CalendarSuccessActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  /** Optional override; defaults to the standard calendar-synced copy */
  title?: string;
  description?: string;
  /** Optional callback when the primary button is pressed (defaults to closing) */
  onConfirm?: () => void;
}

/**
 * Reusable success confirmation sheet to show after syncing/adding to calendar.
 * Wraps the shared StatusActionSheet to keep copy and styling consistent.
 */
export const CalendarSuccessActionSheet: React.FC<CalendarSuccessActionSheetProps> = ({
  isOpen,
  onClose,
  title = 'Class reminder set',
  description = 'The selected class has been added to your calendar successfully',
  onConfirm,
}) => {
  return (
    <StatusActionSheet
      isOpen={isOpen}
      onClose={onClose}
      status={'success'}
      title={title}
      description={description}
      actions={[
        {
          label: 'Okay',
          onPress: () => {
            onConfirm?.();
            onClose();
          },
          variant: 'primary',
          className: 'mt-2',
        },
      ]}
    />
  );
};

export default CalendarSuccessActionSheet;

