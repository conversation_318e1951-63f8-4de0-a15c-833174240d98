import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import * as SecureStore from 'expo-secure-store';
import { LoginUserResponse } from '@/modules/login/hooks/useLoginQuery';

interface BiometricState {
  isSupported: boolean;
  isEnrolled: boolean;
  isEnabled: boolean;
  supportedTypes: number[];
  isLoading: boolean;
}

interface AuthState {
  user: LoginUserResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  showTerms: boolean;
  biometricState: BiometricState;

  // Actions
  setUser: (user: LoginUserResponse | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
  setShowTerms: (show: boolean) => void;
  setBiometricState: (state: Partial<BiometricState>) => void;
  signOut: () => void;
  reset: () => void;
}

const initialBiometricState: BiometricState = {
  isSupported: false,
  isEnrolled: false,
  isEnabled: false,
  supportedTypes: [],
  isLoading: false,
};

const secureStorage = {
  getItem: async (name: string): Promise<string | null> => {
    try {
      return await SecureStore.getItemAsync(name);
    } catch {
      return null;
    }
  },
  setItem: async (name: string, value: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(name, value);
    } catch {
      // Handle error silently
    }
  },
  removeItem: async (name: string): Promise<void> => {
    try {
      await SecureStore.deleteItemAsync(name);
    } catch {
      // Handle error silently
    }
  },
};

export const useAuthStore = create<AuthState>()(
  persist(
    set => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      isError: false,
      error: null,
      showTerms: false,
      biometricState: initialBiometricState,

      setUser: user =>
        set({
          user,
          isAuthenticated: !!user,
          isError: false,
          error: null,
        }),

      setLoading: isLoading => set({ isLoading }),

      setError: error =>
        set({
          error,
          isError: !!error,
          isLoading: false,
        }),

      setShowTerms: showTerms => set({ showTerms }),

      setBiometricState: newState =>
        set(state => ({
          biometricState: { ...state.biometricState, ...newState },
        })),

      signOut: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          isError: false,
          error: null,
          showTerms: false,
        });
      },

      reset: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          isError: false,
          error: null,
          showTerms: false,
          biometricState: initialBiometricState,
        });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => secureStorage),
      partialize: state => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        biometricState: state.biometricState,
      }),
    }
  )
);
