import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import * as SecureStore from 'expo-secure-store';

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: Date;
  isTyping?: boolean;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PopularQuestion {
  id: string;
  question: string;
  category?: string;
}

export interface PlanOption {
  id: string;
  title: string;
  description?: string;
  icon: string;
  route?: string;
}

interface AIState {
  // Current conversation
  currentConversation: Conversation | null;
  isTyping: boolean;

  // Chat history
  conversations: Conversation[];

  // UI State
  isLoading: boolean;
  error: string | null;

  // Static data
  popularQuestions: PopularQuestion[];
  planOptions: PlanOption[];

  // Actions
  setCurrentConversation: (conversation: Conversation | null) => void;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  createNewConversation: (title?: string) => Conversation;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  deleteConversation: (id: string) => void;
  setIsTyping: (isTyping: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

const generateId = () =>
  Math.random().toString(36).substring(2) + Date.now().toString(36);

// Custom storage adapter for Expo SecureStore
const expoSecureStoreAdapter = {
  getItem: async (name: string): Promise<string | null> => {
    try {
      return await SecureStore.getItemAsync(name);
    } catch (error) {
      console.warn('Failed to get item from SecureStore:', error);
      return null;
    }
  },
  setItem: async (name: string, value: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(name, value);
    } catch (error) {
      console.warn('Failed to set item in SecureStore:', error);
    }
  },
  removeItem: async (name: string): Promise<void> => {
    try {
      await SecureStore.deleteItemAsync(name);
    } catch (error) {
      console.warn('Failed to remove item from SecureStore:', error);
    }
  },
};

const initialPopularQuestions: PopularQuestion[] = [
  {
    id: '1',
    question: 'What classes do I have today?',
    category: 'schedule',
  },
  {
    id: '2',
    question: 'How many challenges have I completed?',
    category: 'progress',
  },
  {
    id: '3',
    question: 'Can I book a spot for yoga tomorrow?',
    category: 'booking',
  },
];

const initialPlanOptions: PlanOption[] = [
  {
    id: '1',
    title: 'Build a workout plan',
    icon: 'dumbbell',
    route: '/ai/workout-plan',
  },
  {
    id: '2',
    title: 'Get nutrition tips',
    icon: 'user',
    route: '/ai/nutrition',
  },
  {
    id: '3',
    title: 'Help me plan times to take classes',
    icon: 'calendar',
    route: '/ai/class-planning',
  },
];

// Create store with conditional persistence
const createAIStore = () => {
  const storeConfig = (set: any, get: any) => ({
    // Initial state
    currentConversation: null,
    isTyping: false,
    conversations: [],
    isLoading: false,
    error: null,
    popularQuestions: initialPopularQuestions,
    planOptions: initialPlanOptions,

    // Actions
    setCurrentConversation: conversation =>
      set({ currentConversation: conversation }),

    addMessage: messageData => {
      const message: Message = {
        ...messageData,
        id: generateId(),
        timestamp: new Date(),
      };

      const { currentConversation } = get();

      if (currentConversation) {
        const updatedConversation = {
          ...currentConversation,
          messages: [...currentConversation.messages, message],
          updatedAt: new Date(),
        };

        set(state => ({
          currentConversation: updatedConversation,
          conversations: state.conversations.map(conv =>
            conv.id === currentConversation.id ? updatedConversation : conv
          ),
        }));
      }
    },

    createNewConversation: title => {
      const conversation: Conversation = {
        id: generateId(),
        title: title || 'New Chat',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      set(state => ({
        conversations: [conversation, ...state.conversations],
        currentConversation: conversation,
      }));

      return conversation;
    },

    updateConversation: (id, updates) => {
      set(state => ({
        conversations: state.conversations.map(conv =>
          conv.id === id ? { ...conv, ...updates, updatedAt: new Date() } : conv
        ),
        currentConversation:
          state.currentConversation?.id === id
            ? {
                ...state.currentConversation,
                ...updates,
                updatedAt: new Date(),
              }
            : state.currentConversation,
      }));
    },

    deleteConversation: id => {
      set(state => ({
        conversations: state.conversations.filter(conv => conv.id !== id),
        currentConversation:
          state.currentConversation?.id === id
            ? null
            : state.currentConversation,
      }));
    },

    setIsTyping: isTyping => set({ isTyping }),
    setLoading: loading => set({ isLoading: loading }),
    setError: error => set({ error }),
    clearError: () => set({ error: null }),

    reset: () =>
      set({
        currentConversation: null,
        isTyping: false,
        conversations: [],
        isLoading: false,
        error: null,
      }),
  });

  // Try to create store with persistence, fallback to non-persistent if it fails
  try {
    return create<AIState>()(
      persist(storeConfig, {
        name: 'ai-storage',
        storage: createJSONStorage(() => expoSecureStoreAdapter),
        partialize: state => ({
          conversations: state.conversations,
          // Don't persist currentConversation to avoid stale state
        }),
      })
    );
  } catch (error) {
    console.warn(
      'Failed to create persistent store, using non-persistent store:',
      error
    );
    return create<AIState>()(storeConfig);
  }
};

export const useAIStore = createAIStore();
