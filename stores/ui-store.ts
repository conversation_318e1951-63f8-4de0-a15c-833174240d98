import { create } from 'zustand';

interface UIState {
  // Theme
  colorMode: 'light' | 'dark';

  // Navigation
  activeTab: string;

  // Modals and overlays
  isModalOpen: boolean;
  modalType: string | null;
  modalData: unknown;

  // Loading states
  globalLoading: boolean;

  // Toast/Alert states
  toastMessage: string | null;
  toastType: 'success' | 'error' | 'info' | 'warning' | null;

  // Form states
  formErrors: Record<string, string[]>;

  // Actions
  setColorMode: (mode: 'light' | 'dark') => void;
  setActiveTab: (tab: string) => void;
  openModal: (type: string, data?: unknown) => void;
  closeModal: () => void;
  setGlobalLoading: (loading: boolean) => void;
  showToast: (
    message: string,
    type: 'success' | 'error' | 'info' | 'warning'
  ) => void;
  hideToast: () => void;
  setFormErrors: (errors: Record<string, string[]>) => void;
  clearFormErrors: () => void;
  reset: () => void;
}

export const useUIStore = create<UIState>(set => ({
  // Initial state
  colorMode: 'light',
  activeTab: '',
  isModalOpen: false,
  modalType: null,
  modalData: null,
  globalLoading: false,
  toastMessage: null,
  toastType: null,
  formErrors: {},

  // Actions
  setColorMode: colorMode => set({ colorMode }),

  setActiveTab: activeTab => set({ activeTab }),

  openModal: (modalType, modalData = null) =>
    set({
      isModalOpen: true,
      modalType,
      modalData,
    }),

  closeModal: () =>
    set({
      isModalOpen: false,
      modalType: null,
      modalData: null,
    }),

  setGlobalLoading: globalLoading => set({ globalLoading }),

  showToast: (toastMessage, toastType) =>
    set({
      toastMessage,
      toastType,
    }),

  hideToast: () =>
    set({
      toastMessage: null,
      toastType: null,
    }),

  setFormErrors: formErrors => set({ formErrors }),

  clearFormErrors: () => set({ formErrors: {} }),

  reset: () =>
    set({
      colorMode: 'light',
      activeTab: '',
      isModalOpen: false,
      modalType: null,
      modalData: null,
      globalLoading: false,
      toastMessage: null,
      toastType: null,
      formErrors: {},
    }),
}));
