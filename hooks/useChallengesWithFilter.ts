import { useCallback, useMemo, useState } from 'react';
import { matchSorter } from 'match-sorter';
import { useChallengesQuery } from '@/data/screens/challenges/queries/useChallengesQuery';

export type ChallengesTab = 'active' | 'history';

export const useChallengesWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<ChallengesTab>('active');

  const tabQuery =
    selectedTab === 'active' ? { active_only: true } : { history_only: true };

  const {
    data = [],
    isLoading,
    isRefetching,
    refetch,
  } = useChallengesQuery({
    ...tabQuery,
  });

  const filteredData = useMemo(() => {
    if (!searchTerm) return data;
    return matchSorter(data, searchTerm, { keys: ['title'] });
  }, [data, searchTerm, selectedTab]);

  const clearSearch = useCallback(() => setSearchTerm(''), []);

  return {
    challenges: filteredData,
    isLoading,
    isRefetching,
    searchTerm,
    selectedTab,
    setSelectedTab,
    setSearchTerm,
    clearSearch,
    refetch,
  };
};
