import { useEffect, useContext, useCallback } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { ClassTabContext } from '@/contexts/class-screen-context';

export const useChildVisibility = () => {
  const { scrollViewRef, childRefs, setChildRefs, selectedTabIndex } =
    useContext(ClassTabContext);

  const checkChildVisibility = useCallback(() => {
    if (childRefs.length !== 0) {
      let flag = false;
      const updatedChildRefs = childRefs.map(childRef => {
        if (childRef.ref.current && scrollViewRef.current) {
          childRef.ref.current.measureLayout(
            scrollViewRef.current,
            (x: number, y: number, width: number, height: number) => {
              const windowHeight = Dimensions.get('window').height;

              const scrollY = scrollViewRef.current?._scrollY || 0;
              const isVisible =
                y >= scrollY && y <= scrollY + windowHeight - height;

              if (childRef.isVisible === false && isVisible) {
                childRef.isVisible = isVisible;
                flag = true;
              }
            }
          );
        }
        return childRef;
      });
      if (flag) {
        setChildRefs(updatedChildRefs);
      }
    }
  }, [childRefs, scrollViewRef, setChildRefs]);

  const handleScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (scrollViewRef.current) {
      scrollViewRef.current._scrollY = e.nativeEvent.contentOffset.y;
      checkChildVisibility();
    }
  };

  useEffect(() => {
    if (selectedTabIndex === 0) {
      checkChildVisibility();
    }
  }, [selectedTabIndex, checkChildVisibility]);

  return { handleScroll };
};
