import { useCallback } from 'react';
import { useAIStore } from '@/stores/ai-store';
import { AI_RESPONSES, TYPING_DELAY } from '@/constants/ai-mock-data';

export const useAIChat = () => {
  const {
    currentConversation,
    isTyping,
    conversations,
    popularQuestions,
    planOptions,
    setCurrentConversation,
    addMessage,
    createNewConversation,
    setIsTyping,
    setLoading,
    setError,
    clearError
  } = useAIStore();

  const sendMessage = useCallback(async (content: string) => {
    try {
      clearError();
      
      // If no current conversation, create one
      let conversation = currentConversation;
      if (!conversation) {
        conversation = createNewConversation(content.slice(0, 50) + '...');
      }

      // Add user message
      addMessage({
        content,
        role: 'user'
      });

      // Show typing indicator
      setIsTyping(true);

      // Simulate AI response delay
      setTimeout(() => {
        setIsTyping(false);
        
        // Generate AI response based on content
        const aiResponse = generateAIResponse(content);
        
        addMessage({
          content: aiResponse,
          role: 'assistant'
        });
      }, TYPING_DELAY);

    } catch (error) {
      setIsTyping(false);
      setError(error instanceof Error ? error.message : 'Failed to send message');
    }
  }, [currentConversation, addMessage, createNewConversation, setIsTyping, setError, clearError]);

  const startNewConversation = useCallback(() => {
    setCurrentConversation(null);
  }, [setCurrentConversation]);

  const selectConversation = useCallback((conversation: any) => {
    setCurrentConversation(conversation);
  }, [setCurrentConversation]);

  const handlePopularQuestion = useCallback((question: any) => {
    sendMessage(question.question);
  }, [sendMessage]);

  const handlePlanOption = useCallback((option: any) => {
    const message = `I'd like to ${option.title.toLowerCase()}`;
    sendMessage(message);
  }, [sendMessage]);

  return {
    // State
    currentConversation,
    isTyping,
    conversations,
    popularQuestions,
    planOptions,
    
    // Actions
    sendMessage,
    startNewConversation,
    selectConversation,
    handlePopularQuestion,
    handlePlanOption,
  };
};

// Helper function to generate AI responses
const generateAIResponse = (userMessage: string): string => {
  const message = userMessage.toLowerCase();
  
  if (message.includes('workout') || message.includes('exercise') || message.includes('fitness')) {
    return AI_RESPONSES.WORKOUT_PLAN_START;
  }
  
  if (message.includes('nutrition') || message.includes('diet') || message.includes('food')) {
    return AI_RESPONSES.NUTRITION_TIPS;
  }
  
  if (message.includes('class') || message.includes('schedule') || message.includes('time')) {
    return AI_RESPONSES.CLASS_PLANNING;
  }
  
  if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
    return AI_RESPONSES.GREETING;
  }
  
  return AI_RESPONSES.DEFAULT;
};
