import { useCallback, useMemo, useState } from 'react';
import { matchSorter } from 'match-sorter';
import { useEventsQuery } from '@/data/screens/events/queries/useEventsQuery';
import { format } from 'date-fns';
import { useFilter } from './useFilter';
import {
  FilterField,
  FilterValues,
} from '@/components/shared/filter-component';
import { useClientInfo } from '@/data/screens/common/queries/useClientConfig';
import { EventResponse, EventsQueryParams } from '@/data/screens/events/types';
import { DATE_FORMAT } from '@/constants/date-formats';

interface TimeRange {
  startTime: string;
  endTime: string;
}

const DEFAULT_TIME_RANGE = {
  startTime: '04:00',
  endTime: '24:00',
};

const sanitizers = {
  gym_ids: (value: string | undefined | null) => value?.trim() || undefined,

  types: (value: string[] | undefined | null) =>
    value?.length ? value : undefined,

  date: (value: Date | undefined | null) =>
    value instanceof Date && !isNaN(value.getTime())
      ? { month_year: format(value, 'yyyy-MM-dd') }
      : undefined,

  time: (value: TimeRange | undefined | null) => {
    if (!value) return undefined;

    const isDefault =
      value.startTime === DEFAULT_TIME_RANGE.startTime &&
      value.endTime === DEFAULT_TIME_RANGE.endTime;

    return isDefault
      ? undefined
      : {
          start_time: value.startTime,
          end_time: value.endTime,
        };
  },

  only_available_reservations: (value: boolean | undefined | null) =>
    value === true ? value : undefined,
};

export const sanitizeFilterValues = (
  filterValues: FilterValues
): EventsQueryParams => {
  const sanitized = {
    gym_ids: sanitizers.gym_ids(filterValues.gym_ids as string),
    types: sanitizers.types(filterValues.types as string[]),
    ...sanitizers.date(filterValues.date as Date),
    ...sanitizers.time(filterValues.time as TimeRange),
    only_available_reservations: sanitizers.only_available_reservations(
      filterValues.only_available_reservations as boolean
    ),
  };

  return Object.fromEntries(
    Object.entries(sanitized).filter(([, value]) => value !== undefined)
  ) as EventsQueryParams;
};

export const useEventsWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [appliedFilters, setAppliedFilters] = useState<EventsQueryParams>({});

  const { data: clientData } = useClientInfo();

  // Define filter fields for events
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: 'multiselect',
        key: 'types',
        label: 'Event type',
        options: [
          { label: 'Virtual', value: 'virtual' },
          { label: 'Live', value: 'live' },
          { label: 'Paid', value: 'paid' },
          { label: 'Free', value: 'free' },
        ],
      },
      {
        type: 'select',
        key: 'gym_ids',
        label: 'Location',
        placeholder: 'Select location',
        options:
          clientData?.facilities.map(facility => ({
            label: facility.name,
            value: String(facility.id),
          })) || [],
      },
      {
        type: 'date',
        key: 'date',
        label: 'Date',
        placeholder: 'Select date',
      },
      {
        type: 'timeRange',
        key: 'time',
        label: 'Available start times',
        placeholder: 'Select date',
      },
      {
        type: 'toggle',
        key: 'only_available_reservations',
        label: 'Show only events with available reservations',
      },
    ],
    [clientData?.facilities]
  );

  // Handle filter apply callback
  const handleFilterApply = useCallback((values: FilterValues) => {
    const sanitizedFilters = sanitizeFilterValues(values);
    setAppliedFilters(sanitizedFilters);
  }, []);

  // Initialize filter hook with apply callback
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields, {
    onApply: handleFilterApply,
  });

  // Build query parameters
  const queryParams = useMemo(() => {
    const params: EventsQueryParams = {
      month_year: format(selectedDate, DATE_FORMAT.YEAR_MONTH),
      ...appliedFilters,
    };

    return params;
  }, [selectedDate, appliedFilters]);

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery(queryParams);

  // Apply filtering
  const filteredData = useMemo(() => {
    let filtered = eventsData as EventResponse[];

    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ['name', 'gym_name', 'room_name', 'description'],
      });
    }

    return filtered;
  }, [eventsData, searchTerm]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  // Enhanced clear all filters that also resets applied filters
  const handleClearAllFilters = useCallback(() => {
    clearAllFilters();
    setAppliedFilters({});
  }, [clearAllFilters]);

  return {
    // Data
    events: filteredData,
    isLoading,
    isRefetching,
    error,

    // UI State
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    handleDateChange,
    handleSearch,
    setSearchTerm,
    clearSearch,
    refetch,
    clearAllFilters: handleClearAllFilters,

    // Filter props for components
    filterProps,
  };
};
