import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { useAIStore } from '@/stores/ai-store';
import { FITNESS_GOALS, WORKOUT_FREQUENCY_OPTIONS } from '@/constants/ai-mock-data';

const workoutPlanSchema = z.object({
  fitnessGoal: z.string().min(1, 'Please select a fitness goal'),
  workoutFrequency: z.string().min(1, 'Please select workout frequency'),
});

export type WorkoutPlanFormData = z.infer<typeof workoutPlanSchema>;

export const useWorkoutPlanForm = () => {
  const { addMessage, setIsTyping } = useAIStore();

  const form = useForm({
    defaultValues: {
      fitnessGoal: '',
      workoutFrequency: '',
    },
    validators: {
      onChange: workoutPlanSchema,
    },
    onSubmit: async ({ value }) => {
      try {
        // Add user's selections as a message
        const userMessage = `My fitness goal is to ${value.fitnessGoal.toLowerCase()} and I want to work out ${value.workoutFrequency}.`;
        
        addMessage({
          content: userMessage,
          role: 'user'
        });

        // Show typing indicator
        setIsTyping(true);

        // Simulate AI response
        setTimeout(() => {
          setIsTyping(false);
          
          const aiResponse = generateWorkoutPlanResponse(value);
          
          addMessage({
            content: aiResponse,
            role: 'assistant'
          });
        }, 2000);

      } catch (error) {
        setIsTyping(false);
        console.error('Error submitting workout plan:', error);
      }
    },
  });

  const getFieldError = (fieldName: keyof WorkoutPlanFormData) => {
    const field = form.getFieldInfo(fieldName);
    return field?.state.meta.errors?.[0];
  };

  const hasFieldError = (fieldName: keyof WorkoutPlanFormData) => {
    return !!getFieldError(fieldName);
  };

  const setFieldValue = (fieldName: keyof WorkoutPlanFormData, value: string) => {
    const field = form.getFieldInfo(fieldName);
    if (field) {
      field.setValue(value);
    }
  };

  const getFieldValue = (fieldName: keyof WorkoutPlanFormData) => {
    const field = form.getFieldInfo(fieldName);
    return field?.state.value || '';
  };

  return {
    form,
    fitnessGoals: FITNESS_GOALS,
    workoutFrequencyOptions: WORKOUT_FREQUENCY_OPTIONS,
    
    // Field helpers
    getFieldError,
    hasFieldError,
    setFieldValue,
    getFieldValue,
    
    // Form state
    isSubmitting: form.state.isSubmitting,
    canSubmit: form.state.canSubmit,
    isValid: form.state.isValid,
  };
};

const generateWorkoutPlanResponse = (formData: WorkoutPlanFormData): string => {
  const goal = formData.fitnessGoal.toLowerCase();
  const frequency = formData.workoutFrequency;
  
  let response = `Perfect! I'll create a personalized workout plan to help you ${goal}.\n\n`;
  
  if (goal.includes('stronger')) {
    response += `For strength building with ${frequency}, I recommend:\n\n`;
    response += `• Focus on compound movements (squats, deadlifts, bench press)\n`;
    response += `• Progressive overload each week\n`;
    response += `• 3-4 sets of 6-8 reps for main lifts\n`;
  } else if (goal.includes('weight')) {
    response += `For weight loss with ${frequency}, I recommend:\n\n`;
    response += `• Combine cardio and strength training\n`;
    response += `• High-intensity interval training (HIIT)\n`;
    response += `• Focus on full-body workouts\n`;
  } else if (goal.includes('endurance')) {
    response += `For endurance improvement with ${frequency}, I recommend:\n\n`;
    response += `• Gradually increase workout duration\n`;
    response += `• Mix steady-state and interval cardio\n`;
    response += `• Include functional movements\n`;
  } else {
    response += `For staying active with ${frequency}, I recommend:\n\n`;
    response += `• Mix of cardio and strength exercises\n`;
    response += `• Focus on movements you enjoy\n`;
    response += `• Keep workouts varied and fun\n`;
  }
  
  response += `\nWould you like me to create a detailed weekly schedule for you?`;
  
  return response;
};
