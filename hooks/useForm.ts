import { useCallback } from 'react';
import { useForm as useTanStackForm } from '@tanstack/react-form';
import { useUIStore } from '@/stores/ui-store';
import { ZodValidator } from '@tanstack/zod-form-adapter';
import { z } from 'zod';

interface UseFormOptions<T extends Record<string, unknown>> {
  defaultValues: T;
  validationSchema?: z.ZodSchema<T>;
  onSubmit: (values: T) => Promise<void> | void;
  onSuccess?: (values: T) => void;
  onError?: (error: Error) => void;
}

export const useForm = <T extends Record<string, unknown>>({
  defaultValues,
  validationSchema,
  onSubmit,
  onSuccess,
  onError,
}: UseFormOptions<T>) => {
  const { setFormErrors, clearFormErrors, showToast } = useUIStore();

  const form = useTanStackForm({
    defaultValues,
    validators: validationSchema
      ? {
          onChange: validationSchema,
        }
      : undefined,
    onSubmit: async ({ value }) => {
      try {
        clearFormErrors();
        await onSubmit(value);
        onSuccess?.(value);
        showToast('Form submitted successfully', 'success');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'An error occurred';
        showToast(errorMessage, 'error');
        onError?.(error as Error);

        // Handle validation errors
        if (error instanceof Error && error.message.includes('validation')) {
          // Parse validation errors if they come in a specific format
          // This is a simplified example - adjust based on your error format
          setFormErrors({ general: [errorMessage] });
        }
      }
    },
  });

  const getFieldError = useCallback(
    (fieldName: string) => {
      const field = form.getFieldInfo(fieldName);
      return field?.state.meta.errors?.[0] || null;
    },
    [form]
  );

  const hasFieldError = useCallback(
    (fieldName: string) => {
      const field = form.getFieldInfo(fieldName);
      return !!field?.state.meta.errors?.length;
    },
    [form]
  );

  const isFieldTouched = useCallback(
    (fieldName: string) => {
      const field = form.getFieldInfo(fieldName);
      return field?.state.meta.isTouched || false;
    },
    [form]
  );

  const resetForm = useCallback(() => {
    form.reset();
    clearFormErrors();
  }, [form, clearFormErrors]);

  const setFieldValue = useCallback(
    <K extends keyof T>(fieldName: K, value: T[K]) => {
      const field = form.getFieldInfo(fieldName);
      if (field) {
        field.setValue(value);
      }
    },
    [form]
  );

  const getFieldValue = useCallback(
    <K extends keyof T>(fieldName: K): T[K] | undefined => {
      const field = form.getFieldInfo(String(fieldName));
      return field?.state.value as T[K] | undefined;
    },
    [form]
  );

  return {
    form,

    // Field helpers
    getFieldError,
    hasFieldError,
    isFieldTouched,
    setFieldValue,
    getFieldValue,

    // Form actions
    resetForm,

    // Form state
    isSubmitting: form.state.isSubmitting,
    canSubmit: form.state.canSubmit,
    isValid: form.state.isValid,
    isDirty: form.state.isDirty,
  };
};

// Specific form hooks for common use cases
export const useLoginForm = () => {
  const loginSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
  });

  return useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validationSchema: loginSchema,
    onSubmit: async values => {
      // This will be handled by the component using the hook
      console.log('Login form submitted:', values);
    },
  });
};

export const useSearchForm = () => {
  return useForm({
    defaultValues: {
      searchTerm: '',
    },
    onSubmit: async values => {
      // This will be handled by the component using the hook
      console.log('Search form submitted:', values);
    },
  });
};
