import { useMutation, useQuery } from '@tanstack/react-query';
import { bookAppointment, getUserPasses, BookingRequest } from '@/data/api-client/booking';

export const useBookAppointment = () => {
  return useMutation({
    mutationFn: (booking: BookingRequest) => bookAppointment(booking),
    onSuccess: (data) => {
      console.log('Booking successful:', data);
    },
    onError: (error) => {
      console.error('Booking failed:', error);
    },
  });
};

export const useUserPasses = () => {
  return useQuery({
    queryKey: ['user-passes'],
    queryFn: getUserPasses,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
