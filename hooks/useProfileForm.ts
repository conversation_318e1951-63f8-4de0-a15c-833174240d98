import { useForm } from '@tanstack/react-form';
import { useUserProfile, UserProfileData } from './useUserProfile';
import { Alert } from 'react-native';

export const useProfileForm = () => {
  const { profileData, updateProfileData, saveProfile } = useUserProfile();

  const form = useForm({
    defaultValues: {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      email: profileData.email,
      phone: profileData.phone,
      gender: profileData.gender,
      age: profileData.age,
      height: profileData.height,
      weight: profileData.weight,
    },
    onSubmit: async ({ value }) => {
      try {
        // Update local state
        updateProfileData(value);
        
        // Save to backend
        const success = await saveProfile();
        
        if (success) {
          Alert.alert('Success', 'Profile updated successfully!');
        } else {
          Alert.alert('Error', 'Failed to update profile. Please try again.');
        }
      } catch (error) {
        console.error('Profile update error:', error);
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      }
    },
  });

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) return 'Email is required';
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return undefined;
  };

  const validatePhone = (phone: string) => {
    if (!phone) return undefined; // Phone is optional
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return undefined;
  };

  const validateName = (name: string) => {
    if (!name || name.trim().length === 0) return 'Name is required';
    if (name.trim().length < 2) return 'Name must be at least 2 characters';
    return undefined;
  };

  return {
    form,
    validateEmail,
    validatePhone,
    validateName,
    profileData,
    updateProfileData,
  };
};
