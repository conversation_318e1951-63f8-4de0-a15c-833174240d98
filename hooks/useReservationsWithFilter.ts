import { useCallback, useMemo, useState } from 'react';
import { matchSorter } from 'match-sorter';
import { groupBy, mapValues } from 'lodash/fp';
import { useReservationsQuery } from '@/data/screens/reservations/queries/useReservationsQuery';
import type { ReservationCategory } from '@/data/screens/reservations/types';

export type ReservationsTab = 'upcoming' | 'past';

export const useReservationsWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<ReservationsTab>('upcoming');
  const [selectedCategory, setSelectedCategory] =
    useState<ReservationCategory>('class');

  const {
    data = [],
    isLoading,
    isRefetching,
    refetch,
  } = useReservationsQuery({
    status: selectedTab,
  });
  
  const reservationsByCategory = useMemo(() => {
    const grouped = groupBy('category', data);
    return {
      class: grouped.class || [],
      appointment: grouped.appointment || [],
      equipment: grouped.equipment || [],
    };
  }, [data]);

  // Filter data by selected category
  const filteredData = useMemo(() => {
    const byCategory = reservationsByCategory[selectedCategory] || [];
    if (!searchTerm) return byCategory;
    return matchSorter(byCategory, searchTerm, {
      keys: ['title', 'subtitle', 'instructor', 'location'],
    });
  }, [reservationsByCategory, selectedCategory, searchTerm]);

  const clearSearch = useCallback(() => setSearchTerm(''), []);

  const counts = useMemo(
    () => mapValues('length', reservationsByCategory),
    [reservationsByCategory]
  );

  return {
    reservations: filteredData,
    isLoading,
    isRefetching,
    selectedTab,
    setSelectedTab,
    selectedCategory,
    setSelectedCategory,
    searchTerm,
    setSearchTerm,
    clearSearch,
    refetch,
    counts,
  };
};
