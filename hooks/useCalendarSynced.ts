import { useCallback, useEffect, useState } from 'react';
import { hasCalendarEventForReservation } from '@/utils/calendar';

/**
 * Check and subscribe (manually via refresh) to whether a reservation already
 * has a calendar event stored on device.
 */
export function useCalendarSynced(reservationId?: number) {
  const [isSynced, setIsSynced] = useState(false);

  const refresh = useCallback(async () => {
    const synced = await hasCalendarEventForReservation(reservationId);
    setIsSynced(!!synced);
  }, [reservationId]);

  useEffect(() => {
    refresh();
  }, [refresh]);

  return { isSynced, refresh, setIsSynced } as const;
}

