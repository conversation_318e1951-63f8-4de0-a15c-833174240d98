import { useState, useEffect } from 'react';
import { useSession } from '@/modules/login/auth-provider';
import { useUserInfo } from '@/data/screens/common/queries/useUserInfo';
import { updateUserProfile } from '@/data/api-client/profile';

export interface UserProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  gender: string;
  age: string;
  height: string;
  weight: string;
  profileImage?: string | null;
}

export const useUserProfile = () => {
  const { data: session } = useSession();
  const {
    data: userInfo,
    isLoading,
    error,
  } = useUserInfo(session?.id?.toString() || '');

  const [profileData, setProfileData] = useState<UserProfileData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    gender: 'Male',
    age: '25yrs',
    height: '170cm',
    weight: '70kg',
    profileImage: null,
  });

  useEffect(() => {
    if (session) {
      setProfileData({
        firstName: session.first_name || '',
        lastName: session.last_name || '',
        email: session.email || '',
        phone: session.phone || '',
        gender: session.sex || 'Male',
        age: calculateAge(session.dob) || '25yrs',
        height: '170cm', // Default value, would come from user preferences
        weight: '70kg', // Default value, would come from user preferences
        profileImage: session.profile_image,
      });
    }
  }, [session]);

  const calculateAge = (dob: string | null): string => {
    if (!dob) return '25yrs';

    const birthDate = new Date(dob);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      return `${age - 1}yrs`;
    }

    return `${age}yrs`;
  };

  const getInitials = (firstName?: string, lastName?: string): string => {
    if (!firstName && !lastName) return 'U';
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const getFullName = (): string => {
    return `${profileData.firstName} ${profileData.lastName}`.trim() || 'User';
  };

  const getUsername = (): string => {
    return session?.username || session?.email || '';
  };

  const updateProfileData = (updates: Partial<UserProfileData>) => {
    setProfileData(prev => ({ ...prev, ...updates }));
  };

  const saveProfile = async (): Promise<boolean> => {
    try {
      if (!session?.id) {
        console.error('No user session found');
        return false;
      }

      const response = await updateUserProfile(
        session.id.toString(),
        profileData
      );
      return response.success;
    } catch (error) {
      console.error('Failed to save profile:', error);
      return false;
    }
  };

  return {
    profileData,
    updateProfileData,
    saveProfile,
    getInitials: () => getInitials(profileData.firstName, profileData.lastName),
    getFullName,
    getUsername,
    isLoading,
    error,
  };
};
