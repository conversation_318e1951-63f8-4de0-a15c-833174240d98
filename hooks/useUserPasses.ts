import { useSession } from '@/modules/login/auth-provider';
import { useMemo } from 'react';

/**
 * Hook to check if the current user has available passes for booking trainers
 *
 * For now, this is a simple implementation that checks if the user has any memberships.
 * In the future, this can be extended to check for specific pass types, credits, etc.
 *
 * @returns Object with pass availability information
 */
export const useUserPasses = () => {
  const { data: session } = useSession();

  const passInfo = useMemo(() => {
    // If no session data, assume no passes
    if (!session) {
      return {
        hasAvailablePasses: false,
        totalPasses: 0,
        memberships: [],
      };
    }

    // Check if user has any memberships
    // This is a simple check - in the future this could be more sophisticated
    // to check for specific trainer booking passes, credits, etc.
    // The session might have memberships property or we might need to check other properties
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const memberships = (session as any).memberships || [];

    // For now, we'll assume users without passes need to request info
    // This can be updated based on actual API response structure
    const hasAvailablePasses = memberships.length > 0;

    return {
      hasAvailablePasses,
      totalPasses: memberships.length,
      memberships,
    };
  }, [session]);

  return passInfo;
};

/**
 * Hook specifically for checking if user can book trainers
 * This is separate from general passes as trainer booking might have different requirements
 */
export const useCanBookTrainer = () => {
  const { hasAvailablePasses } = useUserPasses();

  // For now, this is the same as having passes
  // In the future, this could check for specific trainer booking eligibility
  return {
    canBook: hasAvailablePasses,
    reason: hasAvailablePasses ? null : 'No available passes',
  };
};
