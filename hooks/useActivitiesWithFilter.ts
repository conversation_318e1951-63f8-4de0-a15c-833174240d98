import { useState, useMemo, useCallback, useEffect } from 'react';
import { useActivitiesQuery } from '@/data/screens/activities/queries/useActivitiesQuery';
import { formatDate } from '@/data/common/common.utils';
import { matchSorter } from 'match-sorter';
import { useFilter } from './useFilter';
import {
  FilterField,
  FilterValues,
} from '@/components/shared/filter-component';
import { useClientInfo } from '@/data/screens/common/queries/useClientConfig';
import {
  ActivitiesApiResponse,
  ActivitiesQueryParams,
} from '@/data/screens/activities/types';

const DEFAULT_START_TIME = '04:00';
const DEFAULT_END_TIME = '24:00';

type TimeRangeValue = { startTime: string; endTime: string };

const isString = (value: unknown): value is string => typeof value === 'string';
const isTimeRangeValue = (value: unknown): value is TimeRangeValue => {
  if (!value || typeof value !== 'object') return false;
  const maybe = value as { startTime?: unknown; endTime?: unknown };
  return (
    typeof maybe.startTime === 'string' && typeof maybe.endTime === 'string'
  );
};

const sanitizeActivitiesFilters = (
  values: FilterValues
): ActivitiesQueryParams => {
  const sanitized: ActivitiesQueryParams = {};

  const facilityRaw = values['facility'];
  const gymId = isString(facilityRaw) ? facilityRaw.trim() : '';
  if (gymId) sanitized.gym_id = gymId;

  const equipmentTypeIdsRaw = values['equipment_type_id'];
  if (Array.isArray(equipmentTypeIdsRaw)) {
    const equipmentTypeIds = equipmentTypeIdsRaw.filter(isString);
    if (equipmentTypeIds.length > 0)
      sanitized.equipment_type_id = equipmentTypeIds;
  }

  if (values['only_available'] === true) sanitized.only_available = true;

  const timeRangeRaw = values['time'];
  if (isTimeRangeValue(timeRangeRaw)) {
    if (timeRangeRaw.startTime !== DEFAULT_START_TIME)
      sanitized.start_time = timeRangeRaw.startTime;
    if (timeRangeRaw.endTime !== DEFAULT_END_TIME)
      sanitized.end_time = timeRangeRaw.endTime;
  }

  return sanitized;
};

export const useActivitiesWithFilter = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedFilters, setAppliedFilters] = useState<ActivitiesQueryParams>(
    {}
  );
  const { data: clientData } = useClientInfo();

  const queryParams = useMemo<ActivitiesQueryParams>(() => {
    return {
      date: formatDate(selectedDate),
      ...appliedFilters,
    };
  }, [selectedDate, appliedFilters]);

  const {
    data: activitiesResponse,
    isLoading,
    isRefetching,
    refetch,
  } = useActivitiesQuery(queryParams);

  // Define filter fields for activities
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: 'multiselect' as const,
        key: 'equipment_type_id',
        label: 'Types',
        listStyle: 'pill',
        options:
          clientData?.equipment_types?.map(type => ({
            label: type.name,
            value: String(type.id ?? ''),
          })) ?? [],
      },
      {
        type: 'select' as const,
        key: 'facility',
        label: 'Facility',
        placeholder: 'Select facility',
        options:
          activitiesResponse?.gyms?.map(facility => ({
            label: facility.name,
            value: String(facility.id),
          })) ?? [],
      },
      {
        type: 'timeRange' as const,
        key: 'time',
        label: 'Time range',
      },
      {
        type: 'toggle' as const,
        key: 'only_available',
        label: 'Only show what is available',
      },
    ],
    [clientData?.equipment_types, activitiesResponse?.gyms]
  );

  // Initialize filter hook with "Apply" callback like events
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters: baseClearAllFilters,
    handleFilterChange: internalHandleFilterChange,
  } = useFilter(filterFields, {
    onApply: values => {
      setAppliedFilters(sanitizeActivitiesFilters(values));
    },
    onReset: () => {
      setAppliedFilters({});
    },
  });

  // Keep facility selector (external row) in sync by applying gym_id immediately
  useEffect(() => {
    const facilityRaw = filterValues['facility'];
    const gymId = typeof facilityRaw === 'string' ? facilityRaw.trim() : '';
    setAppliedFilters(prev => {
      const next = { ...prev } as ActivitiesQueryParams;
      if (gymId) {
        next.gym_id = gymId;
      } else if (next.gym_id) {
        delete next.gym_id;
      }
      return next;
    });
  }, [filterValues]);

  // Apply client-side filtering
  const filteredActivities = useMemo(() => {
    const data: ActivitiesApiResponse | undefined = activitiesResponse;
    const baseList = data?.equipment ?? [];

    let filtered = baseList;

    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ['name', 'gym_name', 'room_name', 'type'],
      });
    }

    return filtered;
  }, [activitiesResponse, searchTerm, filterValues]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  const handleFilterChange = useCallback(
    (values: FilterValues) => {
      internalHandleFilterChange(values);
    },
    [internalHandleFilterChange]
  );

  const clearAllFilters = useCallback(() => {
    setAppliedFilters({});
    baseClearAllFilters();
  }, [baseClearAllFilters]);

  return {
    // Data
    activities: filteredActivities,
    isLoading,
    isRefetching,

    // UI State
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    handleDateChange,
    setSearchTerm,
    clearSearch,
    refetch,
    handleFilterChange,
    clearAllFilters,

    // Filter props for components
    filterProps,
  };
};
